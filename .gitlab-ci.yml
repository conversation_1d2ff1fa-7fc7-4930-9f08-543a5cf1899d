stages:
  - build

variables:
  IMA<PERSON>_NAME: 'joltify/frontend_x86_main' # Automatically uses GitLab Container Registry
  TAG: 'latest' # Uses branch name or tag as the image tag
  DOCKER_HUB_USER: '$DOCKER_HUB_USER' # Docker Hub username from GitLab CI/CD Variables
  DOCKER_PASSWORD: '$DOCKER_PASSWORD' # Docker Hub password (or personal access token) from GitLab CI/CD Variables

build-docker-image:
  stage: build
  image: docker:24.0.0 # Use the official Docker image for building
  services:
    - name: docker:24.0.0-dind # Docker-in-Docker service
      alias: docker # Alias the service as "docker"
  variables:
    DOCKER_HOST: tcp://docker:2375 # Point to Docker-in-Docker service via alias
    DOCKER_TLS_CERTDIR: '' # Disable TLS for Docker-in-Docker
  script:
    - set -x
    - unset DOCKER_HOST
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_HUB_USER" --password-stdin
    - echo "done..."
    - docker build -t "$IMAGE_NAME:$TAG" -f Dockerfile .
    - docker push "$IMAGE_NAME:$TAG"
  only:
    - mainnet
