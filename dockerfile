FROM node:22.8.0-alpine AS builder

RUN apk add --no-cache python3 make g++ bash

WORKDIR /app

COPY package.json yarn.lock ./

RUN corepack enable && \
    corepack prepare yarn@3.2.3 --activate && \
    yarn install

COPY . .

WORKDIR /app/packages/nextjs
RUN echo "start build...."
RUN yarn install && \
    yarn build


FROM node:22.8.0-alpine
WORKDIR /app
COPY --from=builder /app/packages/nextjs/.next ./.next
COPY --from=builder /app/packages/nextjs/node_modules ./node_modules
COPY --from=builder /app/packages/nextjs/public ./public
COPY --from=builder /app/packages/nextjs/package.json ./package.json
COPY --from=builder /app/packages/nextjs/next.config.js ./next.config.js
COPY --from=builder /app/packages/nextjs/server.js ./server.js

ENV NODE_ENV=production

EXPOSE 3000

CMD ["node", "server.js"]
