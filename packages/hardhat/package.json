{"name": "@se-2/hardhat", "version": "0.0.1", "scripts": {"account": "hardhat run scripts/listAccount.ts", "chain": "hardhat node --network hardhat --no-deploy", "compile": "hardhat compile", "deploy": "hardhat deploy", "flatten": "hardhat flatten", "fork": "MAINNET_FORKING_ENABLED=true hardhat node --network hardhat --no-deploy", "format": "prettier --write ./*.ts ./deploy/**/*.ts ./scripts/**/*.ts ./test/**/*.ts", "generate": "hardhat run scripts/generateAccount.ts", "hardhat-verify": "hardhat verify", "lint": "eslint --config ./.eslintrc.json --ignore-path ./.eslintignore ./*.ts ./deploy/**/*.ts ./scripts/**/*.ts ./test/**/*.ts", "lint-staged": "eslint --config ./.eslintrc.json --ignore-path ./.eslintignore", "test": "REPORT_GAS=true hardhat test --network hardhat", "verify": "hardhat etherscan-verify"}, "dependencies": {"@openzeppelin/contracts": "~5.0.2", "@typechain/ethers-v6": "~0.5.1", "dotenv": "~16.0.3", "envfile": "~6.18.0", "qrcode": "~1.5.1"}, "devDependencies": {"@ethersproject/abi": "~5.7.0", "@ethersproject/providers": "~5.7.2", "@nomicfoundation/hardhat-chai-matchers": "~2.0.7", "@nomicfoundation/hardhat-ethers": "~3.0.8", "@nomicfoundation/hardhat-network-helpers": "~1.0.11", "@nomicfoundation/hardhat-verify": "~2.0.10", "@typechain/ethers-v5": "~11.1.2", "@typechain/hardhat": "~9.1.0", "@types/eslint": "~8", "@types/mocha": "~10.0.8", "@types/prettier": "~2", "@types/qrcode": "~1", "@typescript-eslint/eslint-plugin": "~6.7.3", "@typescript-eslint/parser": "~6.7.3", "chai": "~4.5.0", "eslint": "~8.26.0", "eslint-config-prettier": "~8.5.0", "eslint-plugin-prettier": "~4.2.1", "ethers": "~6.13.2", "hardhat": "~2.22.10", "hardhat-deploy": "~0.12.4", "hardhat-deploy-ethers": "~0.4.2", "hardhat-gas-reporter": "~2.2.1", "prettier": "~2.8.4", "solidity-coverage": "~0.8.13", "ts-node": "~10.9.1", "typechain": "~8.1.0", "typescript": "~5.1.6"}}