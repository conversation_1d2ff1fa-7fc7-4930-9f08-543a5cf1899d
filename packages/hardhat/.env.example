# Template for Hardhat environment variables.

# To use this template, copy this file, rename it .env, and fill in the values.

# If not set, we provide default values (check `hardhat.config.ts`) so developers can start prototyping out of the box,
# but we recommend getting your own API Keys for Production Apps.

# To access the values stored in this .env file you can use: process.env.VA<PERSON><PERSON><PERSON>NAME
ALCHEMY_API_KEY=
DEPLOYER_PRIVATE_KEY=
ETHERSCAN_API_KEY=
