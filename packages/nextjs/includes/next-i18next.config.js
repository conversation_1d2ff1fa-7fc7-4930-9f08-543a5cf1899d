import en from "../locales/en.json";
import ko from "../locales/ko.json";
import zh from "../locales/zh.json";
import { getItem, setItem } from "../utils/other/storage";
import i18n from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import { initReactI18next } from "react-i18next";

const resources = {
  en,
  zh,
  ko,
};

const storedLanguage = getItem("current_language");

if (!storedLanguage && typeof window !== "undefined") {
  const browserLanguage = navigator.language.split("-")[0]; // 获取浏览器语言代码
  const supportedLanguages = ["en", "zh", "ko"];

  const initialLanguage = supportedLanguages.includes(browserLanguage) ? browserLanguage : "en";

  setItem("current_language", initialLanguage);
} else {
  setItem("current_language", storedLanguage);
}

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: "en", // fallback language
    detection: {
      order: ["querystring", "cookie", "localStorage", "navigator", "htmlTag", "path", "subdomain"],
      caches: ["localStorage", "cookie"],
    },
    interpolation: {
      escapeValue: false,
    },
  });

const nextI18NextConfig = {
  i18n: {
    defaultLocale: "en",
    locales: ["en", "zh", "ko"],
  },
};

export { nextI18NextConfig };
export default i18n;
