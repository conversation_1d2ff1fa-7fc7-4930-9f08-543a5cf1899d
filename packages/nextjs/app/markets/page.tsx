"use client";

import React, { useCallback, useEffect, useState } from "react";

// 404页面组件
const Markets404 = () => {
  const [countdown, setCountdown] = useState(5);

  // 跳转到首页的函数
  const redirectToHome = useCallback(() => {
    console.log("Redirecting to homepage...");

    // 使用更强制的跳转方法
    window.location.replace("/");

    // 备用方法：如果replace不工作，使用href
    setTimeout(() => {
      if (window.location.pathname !== "/") {
        console.log("Replace failed, trying href...");
        window.location.href = "/";
      }
    }, 100);
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        console.log("Countdown:", prev);
        if (prev <= 1) {
          redirectToHome();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      console.log("Clearing timer");
      clearInterval(timer);
    };
  }, [redirectToHome]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-md mx-4">
        <div className="text-6xl mb-4">📦</div>
        <h1 className="text-3xl font-bold text-gray-800 mb-4">Moved</h1>
        <h2 className="text-xl font-semibold text-gray-600 mb-4">Market page has been migrated to the front page</h2>
        <p className="text-gray-500 mb-6">You&apos;ll be redirected to the homepage in {countdown} seconds.</p>
        <div className="flex justify-center mb-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
        <button
          onClick={() => {
            console.log("Manual redirect button clicked");
            redirectToHome();
          }}
          className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
        >
          Go to Homepage Now
        </button>
      </div>
    </div>
  );
};

const MarketsPage: React.FC = () => {
  return <Markets404 />;
};

export default MarketsPage;
