"use client";

import React, { useState } from "react";
import Image from "next/image";
import VideoCard from "@/components/learning-center/VideoCard";
import VideoModal from "@/components/learning-center/VideoModal";
import "@/styles/learning-center.css";
import { extractYouTubeVideoId, generateYouTubeEmbedUrl, generateYouTubeThumbnailUrl } from "@/utils/youtube";

// 视频数据配置
const videoConfigs = [
  {
    url: "https://www.youtube.com/watch?v=FfNTeelY4LI",
    title: "How To Open An Account In Predict.one",
    description: "Learn the simple steps to create your account on Predict.one and start making predictions today!",
  },
  {
    url: "https://youtube.com/shorts/UmVHNQ7QDcU?feature=share",
    title: "How To Predict In Predict.one",
    description: "A quick guide on placing your predictions in Predict.one—easy, fast, and straightforward!",
  },
  {
    url: "https://youtu.be/jqnDef09rlE",
    title: "How To Claim Profit In Predict.one",
    description: "Discover how to claim your earnings from successful predictions in just a few clicks.",
  },
  {
    url: "https://youtube.com/shorts/TjF7Lz9QtHs?feature=share",
    title: "How to withdraw your fund instantly from predict.one",
    description: "Get your profits fast! Learn how to instantly withdraw your funds from Predict.one.",
  },
];

// 处理视频数据
const videoData = videoConfigs
  .map(config => {
    const videoId = extractYouTubeVideoId(config.url);
    if (!videoId) {
      console.error(`Invalid YouTube URL: ${config.url}`);
      return null;
    }

    return {
      id: videoId,
      title: config.title,
      description: config.description,
      url: config.url,
      embedUrl: generateYouTubeEmbedUrl(videoId),
      thumbnailUrl: generateYouTubeThumbnailUrl(videoId),
    };
  })
  .filter(Boolean) as Array<{
  id: string;
  title: string;
  description: string;
  url: string;
  embedUrl: string;
  thumbnailUrl: string;
}>;

const LearningCenterPage: React.FC = () => {
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleVideoClick = (video: any) => {
    setSelectedVideo(video);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedVideo(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Banner 头部 */}
      <div className="banner bg-gradient-to-br from-blue-50 via-purple-50 to-indigo-100 py-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 标题 */}
          <div className="title text-center">
            <div className="text-6xl font-extrabold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent pb-4">
              Welcome to Learning Center
            </div>
          </div>

          {/* 卡片区域 */}
          <div className="card bg-white rounded-2xl shadow-2xl overflow-hidden max-w-6xl mx-auto mt-8">
            <div className="flex flex-col lg:flex-row">
              {/* 左侧内容 */}
              <div className="lt lg:w-2/5 p-6 lg:p-8">
                <div className="flex items-start space-x-4">
                  {/* Logo */}
                  <Image
                    src="/logo.png"
                    width={40}
                    height={40}
                    className="flex-shrink-0 rounded-full bg-transparent"
                    draggable="false"
                    alt="Learning Center"
                  />

                  {/* 介绍内容 */}
                  <div className="intro flex-1">
                    <div className="intro-title text-xl lg:text-3xl font-bold text-gray-900 mb-3 leading-tight">
                      I&apos;m PredictOne <br />
                      Decentralized prediction market
                    </div>
                    <div className="intro-desc text-gray-600 text-medium lg:text-lg leading-relaxed mb-6">
                      Create your own markets, make bold moves, and earn rewards. Trade crypto, stocks, sports,
                      politics, and beyond!
                    </div>

                    {/* 播放按钮 */}
                    <button
                      className="play-text-btn flex items-center space-x-2 text-purple-600 hover:text-purple-700 transition-all duration-200 group bg-purple-50 hover:bg-purple-100 px-3 py-2 rounded-lg text-sm"
                      onClick={() => handleVideoClick(videoData[0])}
                    >
                      <svg
                        className="icon group-hover:scale-110 transition-transform duration-200"
                        width="20"
                        height="20"
                        fill="none"
                        viewBox="0 0 24 24"
                        style={{ minWidth: "20px", minHeight: "20px" }}
                      >
                        <g>
                          <path
                            fill="#6841EA"
                            d="M3.938 12a8.062 8.062 0 1 1 16.124 0 8.062 8.062 0 0 1-16.125 0ZM12 2.062c-5.488 0-9.938 4.45-9.938 9.938s4.45 9.938 9.938 9.938 9.938-4.45 9.938-9.938S17.488 2.062 12 2.062Zm3.835 11.564c1.254-.721 1.254-2.53 0-3.251l-4.682-2.692c-1.25-.72-2.81.183-2.81 1.625v5.384c0 1.442 1.56 2.344 2.81 1.625l4.682-2.691Z"
                            clipRule="evenodd"
                            fillRule="evenodd"
                          />
                        </g>
                      </svg>
                      <span className="font-semibold">Play Introduction Video</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* 右侧视频 - 占2/3 */}
              <div className="rt lg:w-3/5">
                <div className="youtube-video h-64 lg:h-full lg:min-h-[400px]">
                  <iframe
                    className="iframe w-full h-full lg:rounded-r-2xl"
                    width="100%"
                    height="100%"
                    src={`${videoData[0]?.embedUrl}?controls=0&showinfo=0&rel=0&modestbranding=1&mute=1`}
                    style={{ border: "none" }}
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative py-20 bg-gradient-to-b from-white to-gray-50">
        {/* 装饰性背景元素 */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 视频组 */}
          <div className="video-group">
            {/* 标题区域 */}
            <div className="text-center mb-12">
              <h2 id="introduction" className="video-group-title text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Introduction
                </span>
              </h2>

              {/* 分隔线 */}
              <div className="flex items-center justify-center mt-8">
                <div className="flex-1 border-t border-gray-200"></div>
                <div className="px-4">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                </div>
                <div className="flex-1 border-t border-gray-200"></div>
              </div>
            </div>

            <div className="video-list">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {videoData.map((video, index) => (
                  <div
                    key={video.id}
                    className="transform hover:scale-105 transition-all duration-300"
                    style={{
                      animationDelay: `${index * 100}ms`,
                    }}
                  >
                    <VideoCard video={video} onClick={() => handleVideoClick(video)} />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 视频播放模态框 */}
      <VideoModal isOpen={isModalOpen} onClose={handleCloseModal} video={selectedVideo} />
    </div>
  );
};

export default LearningCenterPage;
