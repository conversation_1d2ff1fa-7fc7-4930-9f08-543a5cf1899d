"use client";

import React, { Suspense, useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { getButtonsData } from "@/api/iframe";
import { getQuestionByConditionId } from "@/api/markets/detail";
import ProgressBar from "@/components/events-components/ProgressBar";
import { siteConfig } from "@/configs/site";
import { base64DecodeByLanguage } from "@/utils";
import { Button, Image, Link } from "@heroui/react";

const IframeContent = () => {
  const searchParams = useSearchParams();
  const conditionId = searchParams.get("conditionid");
  const current_lunange = searchParams.get("lng") || "en";
  const [questionData, setQuestionData] = useState<any>({ question: "", icon: "" });
  const [groupItemTitle, setGroupItemTitle] = useState<string>("");
  const [currentSlug, setcurrentSlug] = useState<string>("");

  useEffect(() => {
    if (conditionId) {
      const fetchData = async () => {
        try {
          const res = await getQuestionByConditionId(conditionId as string);
          setQuestionData(res.data.question_market[0]);
        } catch (error) {
          console.error("Error fetching question data:", error);
        }
      };
      fetchData();
    }
  }, [conditionId]);

  const { clob_token_ids = [] } = questionData;
  const [currentBtnData, setCurrentBtnData] = useState<any[]>();
  const YesPrice = Math.round(currentBtnData?.[0]?.price * 100 || 0);
  const NoPrice = Math.round(currentBtnData?.[1]?.price * 100 || 0);

  useEffect(() => {
    if (clob_token_ids.length > 0) {
      getButtonsData([
        {
          token_id: clob_token_ids[0],
          side: "Sell",
        },
        {
          token_id: clob_token_ids[1],
          side: "Sell",
        },
      ]).then(res => {
        setCurrentBtnData(res.data);
      });
    }
  }, [clob_token_ids]);

  const { question, icon, event_markets } = questionData || {};
  const event_id = event_markets?.[0]?.event_id;
  const slug = event_markets?.[0]?.event?.slug;

  useEffect(() => {
    if (question) {
      setGroupItemTitle(base64DecodeByLanguage(question, current_lunange));
      setcurrentSlug(base64DecodeByLanguage(slug));
    }
  }, [question, slug, current_lunange]);

  return (
    <div className="flex flex-col flex-center bg-gradient-to-tr p-10 from-purple-600 to-blue-600">
      <div className={"bg-transparent rounded-xl mt-4"}>
        <Link href="/" className="flex items-center bg-white py-2 px-4 rounded-xl cursor-pointer">
          <Image className="size-12 rounded-full" src="/logo.png" alt="" width={48} height={48} />
          <div className="text-2xl ml-2 font-semibold bg-clip-text bg-gradient-to-br from-blue-500 to-purple-700 text-transparent">
            {siteConfig.name}
          </div>
        </Link>
      </div>
      <div className="flex flex-col justify-between w-[580px] h-[240px] bg-white shadow-md rounded-lg my-8 p-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Image
              className="size-16 object-cover rounded-md"
              src={icon ? icon : "/image_alt.jpg"}
              alt="/image_alt.jpg"
              width={64}
              height={64}
            />
            <Link
              href={`/markets/${currentSlug}?tid=${event_id}`}
              className="flex items-center min-h-10 group cursor-pointer"
            >
              <span className="h-full text-gray-900 text-xl font-semibold">{groupItemTitle}</span>
            </Link>
          </div>
          <div className="pt-2 pr-4">
            <ProgressBar percentage={YesPrice} scale={1.5} />
          </div>
        </div>
        <div className="text-xl font-bold text-blue-500 ml-2">{YesPrice + "% chance"}</div>
        <div className="flex flex-center w-full gap-4">
          <Button
            size="lg"
            radius="sm"
            className="flex-1 bg-green-600 text-white hover:opacity-80 text-lg font-semibold font-sans"
          >
            Yes {YesPrice}¢
          </Button>
          <Button
            size="lg"
            radius="sm"
            className="flex-1 bg-red-600 text-white hover:opacity-80 text-lg font-semibold font-sans"
          >
            No {NoPrice}¢
          </Button>
        </div>
      </div>
    </div>
  );
};

const IframePage = () => {
  return (
    <Suspense>
      <IframeContent />
    </Suspense>
  );
};

export default IframePage;
