"use client";

import React, { Suspense, useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { getQuestionByConditionId } from "@/api/markets/detail";
import { siteConfig } from "@/configs/site";
import { base64DecodeByLanguage } from "@/utils";
import { Image, Link } from "@heroui/react";
import { ArrowRight } from "lucide-react";
import { useGlobalState } from "~~/services/store/store";

const IframeContent = () => {
  const [questionData, setQuestionData] = useState<any>({ question: "", icon: "", best_ask: 0, best_bid: 0 });
  const { current_language } = useGlobalState().nativeCurrency;
  const searchParams = useSearchParams();
  const conditionId = searchParams.get("conditionid");

  const [groupItemTitle, setGroupItemTitle] = useState<string>("");
  const [currentSlug, setcurrentSlug] = useState<string>("");

  useEffect(() => {
    if (conditionId) {
      const fetchData = async () => {
        try {
          const res = await getQuestionByConditionId(conditionId as string);
          setQuestionData(res.data.question_market[0]);
        } catch (error) {
          console.error("Error fetching question data:", error);
        }
      };
      fetchData();
    }
  }, [conditionId]);

  const { question, icon, event_markets } = questionData || {};
  const event_id = event_markets?.[0]?.event_id;
  const slug = event_markets?.[0]?.event?.slug;

  useEffect(() => {
    if (question) {
      setGroupItemTitle(base64DecodeByLanguage(question));
      setcurrentSlug(base64DecodeByLanguage(slug));
    }
  }, [current_language, question, slug]);

  return (
    <div className="flex flex-col flex-center">
      <div className="flex flex-col justify-between w-[500px] h-[200px] bg-white shadow-md rounded-lg my-8 px-8 py-4">
        <div className="flex items-center gap-4">
          <Image
            className="size-16 object-cover rounded-md"
            src={icon ? icon : "/image_alt.jpg"}
            alt="/image_alt.jpg"
            width={64}
            height={64}
          />
          <Link
            href={`/event/${currentSlug}?tid=${event_id}`}
            className="flex items-center min-h-10 group cursor-pointer"
          >
            <span className="h-full text-gray-900 text-xl font-semibold">{groupItemTitle}</span>
          </Link>
        </div>
        <div className="flex items-center justify-between w-full">
          <div className={"bg-transparent cursor-pointer"}>
            <Link href="/" className="flex items-center bg-white p-1 rounded-xl cursor-pointer">
              <Image className="size-10 rounded-full" src="/logo.png" alt="" width={40} height={40} />
              <div className="text-xl ml-2 font-semibold bg-clip-text bg-gradient-to-br from-blue-500 to-purple-600 text-transparent">
                {siteConfig.name}
              </div>
            </Link>
          </div>
          <Link
            href={`/event/${currentSlug}?tid=${event_id}`}
            className="flex items-center min-h-10 group cursor-pointer"
          >
            <div className="flex h-10 flex-center bg-gray-200 text-black text-medium font-medium px-4 py-0 gap-2 rounded-lg cursor-pointer">
              {"View Market"}
              <ArrowRight className="size-4" />
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

const IframePage = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <IframeContent />
    </Suspense>
  );
};

export default IframePage;
