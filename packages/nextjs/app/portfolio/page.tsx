"use client";

import React, { useEffect, useState } from "react";
import PreBuyModal from "@/components/details/signature/PreBuyModal";
import HeaderContent from "@/components/portfolio/HeaderContent";
import SelectTable from "@/components/portfolio/SelectTable";
import PromotionTemporaryModal from "@/components/promotion/PromotionTemporaryModal";
import { useClobApiOptimized } from "@/hooks/secret/useClobApiOptimized";
import { usePromotionModal } from "@/hooks/usePromotionModal";
import { useUserAddress } from "@/hooks/useUserAddress";
import { getItem, getProxyAndCheckApprove } from "@/utils";

const PortfolioPage: React.FC = () => {
  const { address } = useUserAddress();
  const proxyWallet = getItem(`login_proxyWallet`);

  const { clobApis } = useClobApiOptimized();
  const { promotionVisible, setPromotionVisible } = usePromotionModal();
  const [isShowPreBuyModal, setIsShowPreBuyModal] = useState(false);
  const [isManuallyDismissed, setIsManuallyDismissed] = useState(false);

  useEffect(() => {
    if (address) {
      getProxyAndCheckApprove(address, setIsShowPreBuyModal);
    }
  }, [address]);

  useEffect(() => {
    if (clobApis) {
      setIsManuallyDismissed(false);
    }
  }, [clobApis]);

  const shouldShowPreBuyModal = (!clobApis || isShowPreBuyModal) && !isManuallyDismissed;

  const handleCloseModal = () => {
    setIsShowPreBuyModal(false);
    setIsManuallyDismissed(true);
  };

  return (
    <div className="flex justify-center pt-2 px-4 md:px-0">
      {clobApis && (
        <div className="w-full flex flex-col md:max-w-[1024px]">
          <HeaderContent proxyWallet={proxyWallet} clobApis={clobApis} />
          <SelectTable
            proxyWallet={proxyWallet}
            clobApis={clobApis}
            onShowPreBuyModal={() => {
              setIsShowPreBuyModal(true);
              setIsManuallyDismissed(false);
            }}
          />
        </div>
      )}

      {shouldShowPreBuyModal && address && (
        <PreBuyModal address={address} visible={shouldShowPreBuyModal} onClose={handleCloseModal} />
      )}
      <PromotionTemporaryModal visible={promotionVisible} onClose={() => setPromotionVisible(false)} />
    </div>
  );
};

export default PortfolioPage;
