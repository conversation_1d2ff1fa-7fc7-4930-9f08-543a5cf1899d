import fetchLogin from "@/api/login/login";
import { setItem } from "@/utils";
import { Magic } from "@magic-sdk/admin";
import NextAuth, { User } from "next-auth";
import { Session } from "next-auth";
import Cred<PERSON><PERSON>Provider from "next-auth/providers/credentials";
import { SiweMessage } from "siwe";

const magic = new Magic(process.env.MAGIC_SECRET_KEY);

declare module "next-auth" {
  interface Session {
    cookie: string;
    user?: {
      name?: string | null;
      email?: string | null;
      image?: string | null;
      issuer?: string;
    };
  }

  interface User {
    id: string;
    authHeaderBase64?: string;
    socialMediaDataBase64?: string;
    email?: string;
    issuer?: string;
  }
}

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        message: { label: "Message", type: "text", required: false },
        signature: { label: "Signature", type: "text", required: false },
        didToken: { label: "DID Token", type: "text", required: false },
        socialMediaData: { label: "Social Media Data", type: "text", required: false },
      },
      authorize: async credentials => {
        // 1. Magic OAuth 登录（Google/Twitter）
        if (credentials?.didToken && credentials?.message && credentials?.signature) {
          try {
            // 验证 Magic didToken
            await magic.token.validate(credentials.didToken);
            const metadata = await magic.users.getMetadataByToken(credentials.didToken);

            // 验证 SIWE 签名
            const siweMessage = new SiweMessage(credentials.message);
            const fields = await siweMessage.verify({ signature: credentials.signature });

            // 确保签名地址与 Magic 地址一致
            if (
              fields.success &&
              siweMessage.address?.toLowerCase() === metadata.publicAddress?.toLowerCase() &&
              metadata.issuer // ensure issuer is not null
            ) {
              console.log("Magic + SIWE login success:", metadata);

              // 构建标准的 SIWE authHeader（与钱包登录格式一致）
              const authHeader = `${JSON.stringify(siweMessage)}:::${credentials.signature}`;
              const authHeaderBase64 = Buffer.from(authHeader).toString("base64");
              const socialMediaDataBase64 = credentials.socialMediaData
                ? Buffer.from(credentials.socialMediaData).toString("base64")
                : undefined;

              return {
                id: metadata.issuer,
                email: metadata.email ?? undefined,
                issuer: metadata.issuer,
                authHeaderBase64,
                socialMediaDataBase64,
              };
            }
            return null;
          } catch (error) {
            console.error("Magic + SIWE 登录失败:", error);
            return null;
          }
        }

        // 3. 纯 SIWE 钱包登录（MetaMask）- 只有 message/signature
        if (credentials?.message && credentials?.signature && !credentials?.didToken) {
          try {
            const siweMessage = new SiweMessage(credentials.message);
            console.log("SIWE Message:", siweMessage);

            const fields = await siweMessage.verify({ signature: credentials.signature });
            if (fields.success && siweMessage.address) {
              const authHeader = `${JSON.stringify(siweMessage)}:::${credentials.signature}`;
              const authHeaderBase64 = Buffer.from(authHeader).toString("base64");
              return {
                id: siweMessage.address,
                authHeaderBase64,
              };
            }
            return null;
          } catch (error) {
            console.error("Error validating SIWE message:", error);
            return null;
          }
        }

        return null;
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }: { token: any; user?: User }) {
      if (user) {
        token.id = user.id;
        token.authHeaderBase64 = user.authHeaderBase64;
        token.socialMediaDataBase64 = user.socialMediaDataBase64;
        token.email = user.email;
        token.issuer = user.issuer;
      }

      if (token.authHeaderBase64) {
        try {
          const response = await fetchLogin(token.authHeaderBase64 as string, token.socialMediaDataBase64);
          const cookies = response.headers["set-cookie"];
          if (cookies) {
            token.cookie = cookies;
            user && setItem(`login_signed_${user.id}`, true);
          }
        } catch (error) {
          console.error("Error fetching login:", error);
        }
      } else if (token.issuer) {
        // OAuth 登录，直接标记为已登录
        user && setItem(`login_signed_${user.id}`, true);
      }

      return token;
    },

    async session({ session, token }: { session: Session; token: any }) {
      session.cookie = token.cookie[0];
      session.user = {
        ...session.user,
        email: token.email,
        issuer: token.issuer,
      };
      return session;
    },
  },
});

export { handler as GET, handler as POST };
