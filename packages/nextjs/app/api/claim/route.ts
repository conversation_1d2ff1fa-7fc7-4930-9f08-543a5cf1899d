import { NextRequest, NextResponse } from "next/server";
import { clearCookie } from "@/utils";
import { isTokenExpiredError } from "@/utils/auth/tokenErrorHandler";
import axios from "axios";

export async function POST(req: NextRequest) {
  try {
    const { data, from, nonce, proxyWallet, signature, signatureParams, to, type, cookie } = await req.json();
    const cleanedCookie = clearCookie(cookie);

    const response = await axios({
      url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/submit`,
      method: "post",
      headers: {
        "Content-Type": "application/json",
        Cookie: cleanedCookie,
      },
      data: {
        data: data,
        from: from,
        nonce: nonce,
        proxyWallet: proxyWallet,
        signature: signature,
        signatureParams: signatureParams,
        to: to,
        type: type,
      },
    });

    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error("Claim API error:", error);

    const errorMessage = error?.message || "Unknown error";

    // 检查是否是token过期错误
    if (isTokenExpiredError(error)) {
      console.error("🔐 Token expired in claim API");
      return NextResponse.json(
        {
          error: "Login session expired, please login again",
          isTokenExpired: true,
        },
        { status: 401 },
      );
    }

    // 其他错误
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
