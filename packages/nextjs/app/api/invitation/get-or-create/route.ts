import { NextRequest, NextResponse } from "next/server";

/**
 * 邀请码获取/创建 API 路由
 * 代理到后端服务
 */

const BACKEND_API_BASE = process.env.BACKEND_API_BASE || "http://localhost:5201";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { proxy_wallet } = body;

    if (!proxy_wallet) {
      return NextResponse.json({ success: false, message: "proxy_wallet is required" }, { status: 400 });
    }

    // 调用后端服务
    const response = await fetch(`${BACKEND_API_BASE}/api/invitation/get-or-create`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ proxy_wallet }),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Backend API error" },
        { status: response.status },
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in invitation API:", error);
    return NextResponse.json({ success: false, message: "Internal server error" }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ success: false, message: "Method not allowed" }, { status: 405 });
}
