import { NextRequest, NextResponse } from "next/server";
import { clearCookie } from "@/utils";
import axios from "axios";

export async function POST(req: NextRequest) {
  try {
    const { comment_id, cookie } = await req.json();

    if (!comment_id || !cookie) {
      return NextResponse.json({ error: "Missing required parameters: comment_id or cookie" }, { status: 400 });
    }

    const cleanedCookie = clearCookie(cookie);
    console.log("commentData.id", comment_id);
    console.log("session.cookie", cleanedCookie);

    const response = await axios({
      url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/delete_comment`,
      method: "post",
      headers: {
        "Content-Type": "application/json",
        Cookie: cleanedCookie,
      },
      data: {
        comment_id: String(comment_id),
      },
    });

    return NextResponse.json({
      success: true,
      data: response.data,
    });
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error("Axios error:", error.response?.data || error.message);
      return NextResponse.json(
        { error: error.response?.data || "Internal Server Error" },
        { status: error.response?.status || 500 },
      );
    } else {
      console.error("Unexpected error:", error);
      return NextResponse.json({ error: "Unexpected error occurred" }, { status: 500 });
    }
  }
}
