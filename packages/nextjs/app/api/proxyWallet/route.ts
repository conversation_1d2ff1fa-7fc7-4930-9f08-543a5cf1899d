import { NextRequest, NextResponse } from "next/server";
import { clearCookie } from "@/utils";
import axios from "axios";

export async function POST(req: NextRequest) {
  try {
    const { from, proxyWallet, signature, cookie, socialMedia, inviteCode } = await req.json();
    const cleanedCookie = clearCookie(cookie);

    const data: any = {
      from: from,
      to: process.env.NEXT_PUBLIC_FACTORY_ADDRESS,
      proxyWallet: proxyWallet,
      data: "0x",
      signature: signature,
      inviteCode: inviteCode,
      signatureParams: {
        payment: "0",
        paymentReceiver: "******************************************",
        paymentToken: "******************************************",
      },
      type: "SAFE-CREATE",
    };

    if (socialMedia) {
      data.socialMedia = socialMedia;
    }

    const response = await axios({
      url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/submit_wallet`,
      method: "post",
      headers: {
        "Content-Type": "application/json",
        Cookie: cleanedCookie,
      },
      data: data,
    });

    return NextResponse.json(response.data);
  } catch (error) {
    const errorMessage = (error as Error).message;
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
