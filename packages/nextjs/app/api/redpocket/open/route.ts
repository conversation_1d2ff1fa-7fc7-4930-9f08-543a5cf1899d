import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

export async function POST(req: NextRequest) {
  try {
    const { address, signature, timestamp, password } = await req.json();

    // 验证必需的参数
    if (!address || !signature || !timestamp || !password) {
      return NextResponse.json({ error: "Missing required parameters" }, { status: 400 });
    }

    // 转发请求到实际的红包API
    const response = await axios({
      url: `${process.env.NEXT_PUBLIC_LUCKY_ENDPOINT}/api/v1/open_red_pocket`,
      method: "post",
      headers: {
        "Content-Type": "application/json",
        "Pedone-address": address,
        "Pedone-signature": signature,
        "Pedone-timestamp": timestamp,
        "Pedone-nonce": "0",
      },
      data: {
        code: password,
      },
      timeout: 30000, // 30秒超时
    });

    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error("Red pocket API error:", error);

    // 处理axios错误
    if (error.response) {
      // 服务器返回了错误状态码
      return NextResponse.json(
        {
          error: error.response.data?.message || "Red pocket service error",
          details: error.response.data,
        },
        { status: error.response.status },
      );
    } else if (error.request) {
      // 请求发送了但没有收到响应
      return NextResponse.json({ error: "Red pocket service unavailable" }, { status: 503 });
    } else {
      // 其他错误
      return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
  }
}
