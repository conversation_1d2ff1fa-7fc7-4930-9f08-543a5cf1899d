import { NextRequest, NextResponse } from "next/server";
import { clearCookie } from "@/utils";
import axios from "axios";

export async function POST(req: NextRequest) {
  try {
    const { data, from, nonce, proxyWallet, signature, signatureParams, to, type, cookie } = await req.json();
    const cleanedCookie = clearCookie(cookie);

    const response = await axios({
      url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/submit`,
      method: "post",
      headers: {
        "Content-Type": "application/json",
        Cookie: cleanedCookie,
      },
      data: {
        data: data,
        from: from,
        nonce: nonce,
        proxyWallet: proxyWallet,
        signature: signature,
        signatureParams: signatureParams,
        to: to,
        type: type,
      },
    });
    return NextResponse.json(response.data);
  } catch (error) {
    const errorData = (error as any).response ? (error as any).response.data : null;
    return NextResponse.json({ error: errorData }, { status: 500 });
  }
}
