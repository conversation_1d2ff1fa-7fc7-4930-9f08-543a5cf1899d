"use client";

import React from "react";
import CreatorEventCardList from "@/components/creator/CreatorEventCardList";
import { Sparkles } from "lucide-react";
import { useTranslation } from "react-i18next";

const CreatorPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 overflow-hidden">
        <div className="relative max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-6 sm:pb-20 sm:pt-8">
          <div className="text-center">
            {/* Icon */}
            <div className="flex justify-center mb-6">
              <div className="bg-white/20 backdrop-blur-sm rounded-full p-4">
                <Sparkles className="h-8 w-8 text-white" />
              </div>
            </div>

            {/* Title */}
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 sm:mb-6">
              {t("Creator_Page_Title") || "Community Creator Events"}
            </h1>

            {/* Description */}
            <p className="text-lg sm:text-xl text-blue-100 max-w-3xl mx-auto px-2 sm:px-0 leading-relaxed">
              {t("Creator_Page_Description") ||
                "Discover and participate in community-created prediction markets. These events are created by our users and represent diverse perspectives from around the world."}
            </p>

            {/* Stats */}
            {/* <div className="grid grid-cols-3 gap-4 sm:gap-8 max-w-2xl mx-auto mt-8 sm:mt-12">
              <div className="text-center">
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 sm:p-4 mb-2">
                  <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white mx-auto" />
                </div>
                <div className="text-xl sm:text-2xl font-bold text-white">1000+</div>
                <div className="text-xs sm:text-sm text-blue-100">{t("Active_Creators") || "Active Creators"}</div>
              </div>
              <div className="text-center">
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 sm:p-4 mb-2">
                  <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-white mx-auto" />
                </div>
                <div className="text-xl sm:text-2xl font-bold text-white">50K+</div>
                <div className="text-xs sm:text-sm text-blue-100">{t("Total_Volume") || "Total Volume"}</div>
              </div>
              <div className="text-center">
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 sm:p-4 mb-2">
                  <Globe className="h-5 w-5 sm:h-6 sm:w-6 text-white mx-auto" />
                </div>
                <div className="text-xl sm:text-2xl font-bold text-white">25+</div>
                <div className="text-xs sm:text-sm text-blue-100">{t("Countries") || "Countries"}</div>
              </div>
            </div> */}
          </div>
        </div>

        {/* Wave Bottom */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg
            className="w-full h-6 sm:h-12 text-gray-50"
            fill="currentColor"
            viewBox="0 0 1200 120"
            preserveAspectRatio="none"
          >
            <path
              d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
              opacity=".25"
            ></path>
            <path
              d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
              opacity=".5"
            ></path>
            <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"></path>
          </svg>
        </div>
      </div>

      {/* Content Section */}
      <div className="relative">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-8 sm:py-12">
          {/* Events List */}
          <CreatorEventCardList />
        </div>
      </div>
    </div>
  );
};

export default CreatorPage;
