"use client";

import React, { useEffect, useState } from "react";
import CustomConnectModal from "@/components/login/CustomConnectModal";
import { useLoginModal } from "@/hooks/useLoginModal";
import { useMysteryBoxList } from "@/hooks/useMysteryBoxList";
import { useRewardSystem } from "@/hooks/useRewardSystem";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useMagicStore } from "@/services/store/magicStore";
import { But<PERSON>, Card, CardBody, Spinner } from "@heroui/react";
import { motion } from "framer-motion";
import { AlertTriangle, ArrowRight, Gift } from "lucide-react";
import { useTranslation } from "react-i18next";
import MysteryBoxHistory from "~~/components/mystery-box/MysteryBoxHistory";
import MysteryBoxSection from "~~/components/mystery-box/MysteryBoxSection";
import ReferralSection from "~~/components/mystery-box/ReferralSection";

const WelcomePage = () => {
  const { t } = useTranslation();
  const { isInitialized, initializeMagic } = useMagicStore();
  const { isConnected, address, isWagmi, isMagic } = useUserAddress();
  const { isLoginModalOpen, openLoginModal, closeLoginModal } = useLoginModal();
  const [isClient, setIsClient] = useState(false);

  // 盲盒列表管理 - 在页面级别统一管理
  const mysteryBoxData = useMysteryBoxList();
  const { fetchMysteryBoxes } = mysteryBoxData;

  // 奖励系统 - 自动检查Magic用户的奖励
  const rewardSystem = useRewardSystem(fetchMysteryBoxes);
  const { autoCheckRewards, isChecking: isCheckingRewards } = rewardSystem;

  useEffect(() => {
    setIsClient(true);
    if (!isInitialized) {
      initializeMagic();
    }
  }, [isInitialized, initializeMagic]);

  // Magic用户登录后自动检查奖励
  useEffect(() => {
    if (isClient && isConnected && isMagic && !isWagmi && address) {
      console.log("🎁 Magic用户已登录，自动检查奖励...");
      console.log("📊 当前盲盒数据:", {
        totalBoxes: mysteryBoxData.totalBoxes,
        readyBoxes: mysteryBoxData.readyBoxes.length,
        cooldownBoxes: mysteryBoxData.cooldownBoxes.length,
      });
      autoCheckRewards();
    }
  }, [isClient, isConnected, isMagic, isWagmi, address, autoCheckRewards, mysteryBoxData]);

  // 监听盲盒数据变化
  useEffect(() => {
    if (mysteryBoxData.totalBoxes > 0) {
      console.log("🔄 盲盒数据已更新:", {
        totalBoxes: mysteryBoxData.totalBoxes,
        readyBoxes: mysteryBoxData.readyBoxes.length,
        cooldownBoxes: mysteryBoxData.cooldownBoxes.length,
        loading: mysteryBoxData.loading,
      });
    }
  }, [
    mysteryBoxData.totalBoxes,
    mysteryBoxData.readyBoxes.length,
    mysteryBoxData.cooldownBoxes.length,
    mysteryBoxData.loading,
  ]);

  if (!isClient) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F0F8FF] to-[#E6F3FF]">
        <Spinner size="lg" color="primary" />
      </div>
    );
  }

  // MetaMask用户提示界面
  if (isConnected && isWagmi && !isMagic) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#F0F8FF] to-[#E6F3FF]">
        <div className="min-h-screen flex items-center justify-center px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-orange-200"
            >
              {/* 警告图标 */}
              <div className="w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <AlertTriangle className="w-10 h-10 text-white" />
              </div>

              {/* 标题 */}
              <h1 className="text-3xl font-bold text-gray-800 mb-4">{t("mysteryBox.metamaskNotSupported.title")}</h1>

              {/* 说明文字 */}
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                {t("mysteryBox.metamaskNotSupported.description")}
                <br />
                {t("mysteryBox.metamaskNotSupported.switchTip")}
              </p>

              {/* 建议操作 */}
              <div className="bg-orange-50 rounded-lg p-4 mb-6">
                <h3 className="text-lg font-semibold text-orange-800 mb-2">
                  {t("mysteryBox.metamaskNotSupported.suggestions")}
                </h3>
                <ul className="text-orange-700 text-left space-y-1">
                  <li>{t("mysteryBox.metamaskNotSupported.step1")}</li>
                  <li>{t("mysteryBox.metamaskNotSupported.step2")}</li>
                  <li>{t("mysteryBox.metamaskNotSupported.step3")}</li>
                </ul>
              </div>

              {/* 返回按钮 */}
              <Button
                size="lg"
                className="bg-gradient-to-r from-orange-400 to-orange-500 text-white font-bold px-8 py-3 text-lg hover:shadow-lg transition-all duration-300"
                onPress={() => window.history.back()}
              >
                {t("mysteryBox.metamaskNotSupported.goBack")}
              </Button>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F0F8FF] to-[#E6F3FF]">
      {/* 登录前的欢迎页面 */}
      {!isConnected && (
        <>
          {/* 活动介绍区域 */}
          <section className="relative min-h-[calc(100vh-64px)] flex items-center justify-center px-4 py-4">
            {/* 背景装饰 */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute top-20 left-10 w-20 h-20 bg-[#D0BFFF]/20 rounded-full blur-xl"></div>
              <div className="absolute top-40 right-20 w-32 h-32 bg-[#64B5F6]/20 rounded-full blur-xl"></div>
              <div className="absolute bottom-40 left-20 w-24 h-24 bg-[#81C784]/20 rounded-full blur-xl"></div>
              <div className="absolute bottom-20 right-10 w-16 h-16 bg-[#FFB74D]/20 rounded-full blur-xl"></div>
            </div>

            <div className="max-w-4xl mx-auto text-center relative z-10">
              {/* 品牌标题 */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="mb-8"
              >
                <div className="flex items-center justify-center gap-3 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-[#64B5F6] to-[#42A5F5] rounded-2xl flex items-center justify-center shadow-lg">
                    <Gift className="w-8 h-8 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold text-gray-800">{t("mysteryBox.welcome.brandTitle")}</h1>
                </div>
              </motion.div>

              {/* 主标题 */}
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-5xl md:text-6xl font-bold text-gray-800 mb-6 leading-tight"
              >
                {t("mysteryBox.welcome.mainTitle")}
                <span className="bg-gradient-to-r from-[#64B5F6] to-[#D0BFFF] bg-clip-text text-transparent">
                  {t("mysteryBox.welcome.mainTitleHighlight")}
                </span>
                {t("mysteryBox.welcome.mainTitleEnd")}
              </motion.h2>

              {/* 副标题 */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-xl md:text-2xl text-gray-600 mb-12 leading-relaxed max-w-3xl mx-auto"
              >
                {t("mysteryBox.welcome.subtitle")}
              </motion.p>

              {/* 登录按钮 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="mb-12"
              >
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-[#64B5F6] to-[#42A5F5] text-white font-bold px-12 py-6 text-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                  onPress={openLoginModal}
                  endContent={<ArrowRight className="w-6 h-6" />}
                >
                  {t("mysteryBox.welcome.startButton")}
                </Button>
              </motion.div>

              {/* 特色标签 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="flex flex-wrap gap-4 justify-center"
              >
                <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm rounded-full px-6 py-3 shadow-sm">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">{t("mysteryBox.welcome.features.freeJoin")}</span>
                </div>
                <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm rounded-full px-6 py-3 shadow-sm">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">
                    {t("mysteryBox.welcome.features.realRewards")}
                  </span>
                </div>
                <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm rounded-full px-6 py-3 shadow-sm">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">
                    {t("mysteryBox.welcome.features.dailyChances")}
                  </span>
                </div>
              </motion.div>
            </div>
          </section>

          {/* 操作流程介绍 */}
          <section className="py-16 px-4 bg-white/50">
            <div className="max-w-6xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center mb-12"
              >
                <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                  {t("mysteryBox.welcome.howItWorks.title")}
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">{t("mysteryBox.welcome.howItWorks.subtitle")}</p>
              </motion.div>

              <div className="grid md:grid-cols-3 gap-8">
                {/* 第一步：连接钱包 */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                >
                  <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/80 backdrop-blur-sm">
                    <CardBody className="p-8 text-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-[#64B5F6] to-[#42A5F5] rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <div className="text-2xl font-bold text-white">1</div>
                      </div>
                      <h3 className="text-xl font-bold text-gray-800 mb-4">
                        {t("mysteryBox.welcome.howItWorks.step1.title")}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {t("mysteryBox.welcome.howItWorks.step1.description")}
                      </p>
                    </CardBody>
                  </Card>
                </motion.div>

                {/* 第二步：开启盲盒 */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/80 backdrop-blur-sm">
                    <CardBody className="p-8 text-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-[#D0BFFF] to-[#B39DDB] rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <div className="text-2xl font-bold text-white">2</div>
                      </div>
                      <h3 className="text-xl font-bold text-gray-800 mb-4">
                        {t("mysteryBox.welcome.howItWorks.step2.title")}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {t("mysteryBox.welcome.howItWorks.step2.description")}
                      </p>
                    </CardBody>
                  </Card>
                </motion.div>

                {/* 第三步：邀请好友 */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/80 backdrop-blur-sm">
                    <CardBody className="p-8 text-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-[#81C784] to-[#66BB6A] rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <div className="text-2xl font-bold text-white">3</div>
                      </div>
                      <h3 className="text-xl font-bold text-gray-800 mb-4">
                        {t("mysteryBox.welcome.howItWorks.step3.title")}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {t("mysteryBox.welcome.howItWorks.step3.description")}
                      </p>
                    </CardBody>
                  </Card>
                </motion.div>
              </div>
            </div>
          </section>
        </>
      )}

      {/* 登录后的用户界面 - 数据看板和领奖组件 - 仅限Magic用户 */}
      {isConnected && isMagic && !isWagmi && (
        <div className="min-h-screen py-8 px-4">
          <div className="max-w-6xl mx-auto space-y-8">
            {/* 领奖组件 - 盲盒区域 */}
            <MysteryBoxSection mysteryBoxData={mysteryBoxData} isCheckingRewards={isCheckingRewards} />

            {/* 历史记录组件 */}
            <MysteryBoxHistory userAddress={address} mysteryBoxData={mysteryBoxData} />

            {/* 数据看板 - 邀请区域 */}
            <ReferralSection />
          </div>
        </div>
      )}

      {/* 登录模态框 */}
      <CustomConnectModal isOpen={isLoginModalOpen} onClose={closeLoginModal} showOnlyMagic={true} />
    </div>
  );
};

export default WelcomePage;
