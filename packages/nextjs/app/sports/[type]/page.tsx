"use client";

import React, { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { getSportGamesEventsListData } from "@/api/sports";
import LeftNavList from "@/components/sports/LeftNavList";
import GameContent from "@/components/sports/games/GameContent";
import PropsContent from "@/components/sports/props/PropsContent";
import { COMBINEDMOCKLEFTNAVLIST } from "@/utils/mock";
import { Button, Image } from "@heroui/react";

const SportsTypePage = () => {
  const pathname = usePathname();
  const [secondRouting, setSecondRouting] = useState<string>("");
  const [displayType, setDisplayType] = useState<"props" | "game">("props");
  const [selectedNavItem, setSelectedNavItem] = useState<any>(null);

  useEffect(() => {
    const segments = pathname.split("/");
    setSecondRouting(segments[2]);
  }, [pathname]);

  // 检查当前运动类型是否有Game和Props类型的事件
  useEffect(() => {
    const checkEventTypes = async () => {
      if (!secondRouting) return;

      const navItem = COMBINEDMOCKLEFTNAVLIST.find(item => item.key === secondRouting);
      if (!navItem) return;

      setSelectedNavItem(navItem);

      try {
        const requests = [];

        // 检查Props类型事件
        if (navItem.tag_id) {
          requests.push(
            getSportGamesEventsListData({
              active: true,
              closed: false,
              offset: 0,
              limit: 5,
              tag_id: navItem.tag_id,
            }),
          );
        }

        // 检查Game类型事件
        if (navItem.tag_game_id) {
          requests.push(
            getSportGamesEventsListData({
              active: true,
              closed: false,
              offset: 0,
              limit: 5,
              tag_id: navItem.tag_game_id,
            }),
          );
        }

        const responses = await Promise.all(requests);

        let propsEvents = [];
        let gameEvents = [];

        // 处理Props类型事件 (tag_id)
        if (navItem.tag_id && responses[0]) {
          propsEvents = responses[0].data.events || [];
        }

        // 处理Game类型事件 (tag_game_id)
        if (navItem.tag_game_id) {
          const gameResponseIndex = navItem.tag_id ? 1 : 0;
          if (responses[gameResponseIndex]) {
            gameEvents = responses[gameResponseIndex].data.events || [];
          }
        }

        // 如果只有Game类型事件，默认显示Game类型
        if (gameEvents.length > 0 && propsEvents.length === 0) {
          setDisplayType("game");
        } else {
          setDisplayType("props");
        }
      } catch (error) {
        console.error("Error checking event types:", error);
      }
    };

    checkEventTypes();
  }, [secondRouting]);

  const sport = COMBINEDMOCKLEFTNAVLIST.find(sport => sport.key === secondRouting);
  const name = sport ? sport.key.charAt(0).toUpperCase() + sport.key.slice(1) : "Sports";
  const icon = sport ? sport.icon : "";

  return (
    <div className="flex flex-col lg:flex-row justify-center w-full h-full">
      <div className="max-w-[1400px] px-4 w-full flex flex-col lg:flex-row relative gap-3">
        <LeftNavList secondRouting={secondRouting} className="w-full lg:w-52" />

        <div className="flex-1 w-full">
          <div className="flex flex-col lg:flex-row items-center mt-4 lg:mt-8 mb-4 lg:mb-6">
            <Image src={icon} alt={name} className="size-15 rounded-lg" width={60} height={60} />
            <div className="text-4xl font-bold lg:ml-4 mt-2 lg:mt-0">{name}</div>
          </div>

          {/* 显示类型切换按钮 */}
          {sport && (
            <div className="flex gap-2 mb-4">
              <Button
                variant={displayType === "props" ? "solid" : "flat"}
                color={displayType === "props" ? "primary" : "default"}
                onPress={() => setDisplayType("props")}
              >
                Props
              </Button>

              {/* 只有配置了tag_game_id的运动类型才显示Game按钮 */}
              {sport.tag_game_id && (
                <Button
                  variant={displayType === "game" ? "solid" : "flat"}
                  color={displayType === "game" ? "primary" : "default"}
                  onPress={() => setDisplayType("game")}
                >
                  Game
                </Button>
              )}
            </div>
          )}

          {/* 根据显示类型渲染不同的内容 */}
          {displayType === "props" ? (
            <PropsContent secondRouting={secondRouting} />
          ) : (
            <GameContent tagGameId={selectedNavItem?.tag_game_id} />
          )}
        </div>
      </div>
    </div>
  );
};

export default SportsTypePage;
