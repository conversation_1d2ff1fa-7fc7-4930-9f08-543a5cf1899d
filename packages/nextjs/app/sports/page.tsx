"use client";

import React from "react";
import AllSportsContent from "@/components/sports/AllSportsContent";
import LeftNavList from "@/components/sports/LeftNavList";
import { useTranslation } from "react-i18next";

const SportsPage = () => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col lg:flex-row justify-center w-full h-full">
      <div className="max-w-[1400px] px-4 w-full flex flex-col lg:flex-row relative gap-3">
        <LeftNavList className="w-full lg:w-52" />

        <div className="flex-1 w-full">
          <div className="flex flex-col lg:flex-row items-center mt-4 lg:mt-8 mb-4 lg:mb-6">
            <div className="text-4xl font-bold">{t("sports.all_sports")}</div>
          </div>
          <AllSportsContent />
        </div>
      </div>
    </div>
  );
};

export default SportsPage;
