"use client";

import React, { useCallback, useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { getCurrentEventData } from "@/api/markets/detail";
import ContentFooter from "@/components/details/ContentFooter";
import HotEventsComponent from "@/components/details/ContentFooter/HotEventsComponent";
import ContentHeader from "@/components/details/ContentHeader";
import IframeModal from "@/components/details/IframeModal";
import MarketsList from "@/components/details/MarketsList";
import ClaimCard from "@/components/details/claimCard/ClaimCard";
import EventChart from "@/components/details/lineChart/Chart";
import OrderCard from "@/components/details/orderCard/OrderCard";
import RulesItem from "@/components/details/rules";
import PreBuyModal from "@/components/details/signature/PreBuyModal";
import { useClobApiOptimized } from "@/hooks/secret/useClobApiOptimized";
import { useUserAddress } from "@/hooks/useUserAddress";
import { getExcludedTagIds, getProxyAndCheckApprove, sortAndFilterMarkets } from "@/utils";
import { useSession } from "next-auth/react";
import { useMediaQuery } from "react-responsive";

const EventPage: React.FC = () => {
  const { address } = useUserAddress();
  const { clobApis } = useClobApiOptimized();
  const [currentEvent, setCurrentEvent] = useState<any>({});
  const [chartDataList, setChartDataList] = useState<any[]>([]);
  const [marketsDataList, setMarketsDataList] = useState<any[]>([]);
  const [isSingleSidedTrading, setIsSingleSidedTrading] = useState<boolean>(false);

  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [currentOutcomePrices, setCurrentOutcomePrices] = useState<string[]>([]);
  const [currentSelectedItem, setCurrentSelectedItem] = useState<string>("Buy");
  const [selectedButtonType, setSelectedButtonType] = useState<"yes" | "no">("yes");
  const [isShowPreBuyModal, setIsShowPreBuyModal] = useState(false);
  const [isShowiFrameModal, setIsShowiFrameModal] = useState(false);
  const [isShowOrderCardMobile, setIsShowOrderCardMobile] = useState(false);

  const [btnPriceData, setBtnPriceData] = useState({ YesPrice: 0, NoPrice: 0 });

  const { data: session } = useSession();
  const searchParams = useSearchParams();
  const tid = searchParams.get("tid");
  const qid = searchParams.get("qid");

  const isMobile = useMediaQuery({ maxWidth: 767 });

  // 检测是否为单边交易事件（包含 simple_yes 等标签）
  const checkIsSingleSidedTrading = (event: any): boolean => {
    const EXCLUDED_TAG_IDS = getExcludedTagIds();

    if (!event?.event_tags || !Array.isArray(event.event_tags)) {
      return false;
    }

    return event.event_tags.some((eventTag: any) => {
      const tagId = eventTag?.tag_id;
      return tagId && EXCLUDED_TAG_IDS.includes(tagId);
    });
  };

  useEffect(() => {
    if (currentEvent?.event_markets) {
      const filterAndSortMarkets = sortAndFilterMarkets(currentEvent.event_markets);
      setChartDataList(filterAndSortMarkets);
      setMarketsDataList(sortAndFilterMarkets(currentEvent.event_markets));
    }
  }, [currentEvent]);

  useEffect(() => {
    if (tid) {
      getCurrentEventData(tid)
        .then(res => {
          const eventData = res.data.events[0];

          // 检测是否为单边交易事件
          const isSingleSided = checkIsSingleSidedTrading(eventData);
          setIsSingleSidedTrading(isSingleSided);

          setCurrentEvent(eventData);
        })
        .catch(error => {
          console.error("Error fetching event data:", error);
        });
    }
  }, [tid]);

  useEffect(() => {
    if (currentEvent?.event_markets) {
      if (qid) {
        setSelectedItemId(qid);
      } else {
        const filterAndSortMarkets = sortAndFilterMarkets(currentEvent.event_markets);
        setSelectedItemId(filterAndSortMarkets[0]?.question_market?.id);
      }
    }
  }, [currentEvent, qid]);

  useEffect(() => {
    const checkApproval = async () => {
      await getProxyAndCheckApprove(address, setIsShowPreBuyModal);
    };

    checkApproval();
  }, [address, session]);

  useEffect(() => {
    const selectedMarket = marketsDataList.find(market => market.question_market.id === Number(selectedItemId));
    if (selectedMarket) {
      setCurrentOutcomePrices(selectedMarket.question_market.outcome_prices || []);
    }
  }, [marketsDataList, selectedItemId]);

  const renderOrderCard = useCallback(() => {
    if (currentOutcomePrices && currentOutcomePrices?.length > 0) {
      return (
        <ClaimCard
          address={address}
          marketsDataList={marketsDataList}
          outcome_prices={currentOutcomePrices}
          selectedQuestionId={Number(selectedItemId)}
        />
      );
    }

    if (marketsDataList.length > 0) {
      return (
        <OrderCard
          clobApis={clobApis}
          cardType={isSingleSidedTrading ? "single-sided" : "bilateral"} // 根据交易类型设置 cardType
          key={selectedItemId}
          marketsDataList={marketsDataList}
          selectedItemId={Number(selectedItemId)}
          selectedButtonType={selectedButtonType}
          setSelectedButtonType={setSelectedButtonType}
          currentSelectedItem={currentSelectedItem}
          setCurrentSelectedItem={setCurrentSelectedItem}
          isShowPreBuyModal={isShowPreBuyModal}
          setIsShowPreBuyModal={setIsShowPreBuyModal}
          btnPriceData={btnPriceData}
        />
      );
    }

    return null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    address,
    currentOutcomePrices,
    marketsDataList,
    selectedItemId,
    selectedButtonType,
    isShowOrderCardMobile,
    isMobile,
    currentSelectedItem,
    isShowPreBuyModal,
    btnPriceData,
  ]);

  return (
    <div className="flex flex-col lg:flex-row justify-center gap-8 px-4 lg:px-6 pb-16 lg:py-0">
      <div className="w-full lg:max-w-[800px] md:py-4 md:px-2">
        <div className="w-full flex flex-col">
          <ContentHeader currentEvent={currentEvent} setIsShowiFrameModal={setIsShowiFrameModal} />

          {chartDataList.length > 0 && <EventChart chartDataList={chartDataList} />}

          {marketsDataList.length > 0 && (
            <MarketsList
              marketsDataList={marketsDataList}
              selectedItemId={Number(selectedItemId)}
              setSelectedItemId={setSelectedItemId}
              selectedButtonType={selectedButtonType}
              setSelectedButtonType={setSelectedButtonType}
              currentSelectedItem={currentSelectedItem}
              setIsShowOrderCardMobile={setIsShowOrderCardMobile}
              setBtnPriceData={setBtnPriceData}
              isSingleSidedTrading={isSingleSidedTrading}
            />
          )}

          {currentEvent && <RulesItem currentEvent={currentEvent} selectedItemId={selectedItemId} />}
          <ContentFooter currentEvent={currentEvent} />
        </div>
      </div>

      <div className="lg:min-w-[340px] max-w-[380px] lg:sticky mt-8 space-y-4">
        {renderOrderCard()}
        {!isMobile && <HotEventsComponent />}
      </div>

      {isShowPreBuyModal && (
        <PreBuyModal address={address} visible={isShowPreBuyModal} onClose={() => setIsShowPreBuyModal(false)} />
      )}

      {isShowiFrameModal && (
        <IframeModal
          visible={isShowiFrameModal}
          onClose={() => setIsShowiFrameModal(false)}
          marketsDataList={marketsDataList}
        />
      )}
    </div>
  );
};

export default EventPage;
