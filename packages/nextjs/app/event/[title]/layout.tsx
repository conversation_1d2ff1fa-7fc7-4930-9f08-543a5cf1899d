import { checkImageExists } from "@/api/other";
import { getMetadata } from "~~/utils/scaffold-eth/getMetadata";

type Props = {
  params: { title: string };
};

export const generateMetadata = async ({ params }: Props) => {
  const slug = decodeURIComponent(params.title);

  const result = slug
    .split("")
    .filter(char => /[a-zA-Z0-9\s-]/.test(char))
    .join("");

  const imageName = result.replace(/\s+/g, "-");
  const manualImageRelativePath = imageName
    ? `/predict-pictures/pics/${imageName}_manual.png`
    : "/predict-pictures/pics/Boss_titan.png";
  const autoImageRelativePath = imageName
    ? `/predict-pictures/pics/${imageName}.png`
    : "/predict-pictures/pics/Boss_titan.png";

  // 检查远程 manual 图片是否存在
  let imageRelativePath = autoImageRelativePath;
  if (imageName && (await checkImageExists(manualImageRelativePath))) {
    imageRelativePath = manualImageRelativePath;
  }

  const metadata = getMetadata({
    title: "events",
    description: "",
    imageRelativePath: imageRelativePath,
  });
  return metadata;
};

const EventPageLayout = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

export default EventPageLayout;
