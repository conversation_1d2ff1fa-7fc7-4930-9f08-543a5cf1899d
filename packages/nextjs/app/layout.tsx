import Script from "next/script";
import Providers from "./providers";
import "@rainbow-me/rainbowkit/styles.css";
import { ScaffoldEthAppWithProviders } from "~~/components/ScaffoldEthAppWithProviders";
import { ThemeProvider } from "~~/components/ThemeProvider";
import "~~/styles/globals.css";
import { getMetadata } from "~~/utils/scaffold-eth/getMetadata";

export const generateMetadata = () => {
  const metadata = getMetadata({
    title: "Predict One | Leading Prediction Market: No KYC, Instant Bets! |全球领先在线预测市场，无需KYC，即时下注",
    description: "Leading Prediction Market: No KYC, Instant Bets! |全球领先在线预测市场，无需KYC，即时下注",
    imageRelativePath: "/backend/homeTwitterCard.png",
  });

  return {
    ...metadata,
    keywords: [
      "prediction market",
      "predict one",
      "online prediction market",
      "no KYC",
      "instant bet",
      "在线预测",
      "预测市场",
      "匿名预测平台",
      "快速下注",
      "加密货币预测",
    ],
    robots: "index, follow",
    author: "Predict One Team",
    viewport: "width=device-width, initial-scale=1",
    openGraph: {
      ...metadata.openGraph,
      url: "https://predict.one",
      type: "website",
      description:
        "Predict One 是全球领先的在线预测市场，支持即时下注，无需KYC认证，轻松参与热门事件预测。加入数千用户，一起预测未来！",
      images: [
        {
          url: `${metadata.metadataBase}/backend/homeTwitterCard.png`,
          width: 1200,
          height: 628,
          alt: "Predict One | Leading Prediction Market: No KYC, Instant Bets! |全球领先在线预测市场，无需KYC，即时下注",
        },
      ],
    },
    twitter: {
      ...metadata.twitter,
      card: "summary_large_image",
      description:
        "Predict One 是全球领先的在线预测市场，支持即时下注，无需KYC认证，轻松参与热门事件预测。加入数千用户，一起预测未来！",
      images: [
        {
          url: `${metadata.metadataBase}/backend/homeTwitterCard.png`,
          width: 1200,
          height: 628,
          alt: "Predict One | Leading Prediction Market: No KYC, Instant Bets! |全球领先在线预测市场，无需KYC，即时下注",
        },
      ],
    },
    charset: "utf-8",
  };
};

const ScaffoldEthApp = async ({ children }: { children: React.ReactNode }) => {
  return (
    <html suppressHydrationWarning>
      <head>
        <>
          <Script async src="https://www.googletagmanager.com/gtag/js?id=G-69EQKMMRHK" strategy="afterInteractive" />
          <Script
            id="google-analytics"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', 'G-69EQKMMRHK');
                `,
            }}
          />
        </>
      </head>
      <body>
        <ThemeProvider enableSystem forcedTheme="light">
          <ScaffoldEthAppWithProviders>
            <Providers>{children}</Providers>
          </ScaffoldEthAppWithProviders>
        </ThemeProvider>
      </body>
    </html>
  );
};

export default ScaffoldEthApp;
