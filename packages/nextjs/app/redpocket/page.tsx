"use client";

import React, { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { getWebUsersByProxyWallet } from "@/api/user";
import { getNextRount } from "@/app/api/redpocket/countdown";
import FailedView from "@/components/redpocket/FailedView";
import InputView from "@/components/redpocket/InputView";
import SuccessView from "@/components/redpocket/SuccessView";
import computeProxyAddress from "@/contracts/computeProxyAddress";
import { useLoginModal } from "@/hooks/useLoginModal";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useMagicStore } from "@/services/store/magicStore";
import { handleCreateRedPocketSignatureWithMagic } from "@/utils/signature/redpocketSignature";
import { useTranslation } from "react-i18next";

// 活动状态枚举
enum EventStatus {
  LOADING = "loading",
  ACTIVE = "active",
  NOT_STARTED = "not_started",
  ENDED = "ended",
  LASTROUNT = "last_round",
}

// 活动状态检查结果接口
interface EventStatusResult {
  status: EventStatus;
  nextRoundTimestamp: string;
}

// 红包领取结果处理工具函数
const processClaimResult = (
  claimResponse: any,
): { success: true; amount: number; credit: number } | { success: false; error: string; details: any } => {
  if (claimResponse.success) {
    // 处理成功结果
    const rawAmount = parseFloat(claimResponse.amount || claimResponse.data?.amount || "0");
    const formattedAmount = parseFloat((rawAmount / 1000000).toFixed(2)); // 除以10^6并保留两位小数

    const rawCredit = parseFloat(claimResponse.credit || claimResponse.data?.credit || "0");

    return {
      success: true,
      amount: formattedAmount,
      credit: rawCredit,
    };
  } else {
    // 处理错误结果
    const errorMessage = claimResponse.error || claimResponse.message || "Unknown error";
    return {
      success: false,
      error: errorMessage,
      details: claimResponse.details || claimResponse,
    };
  }
};

// 活动状态检查工具函数
const checkEventStatus = async (): Promise<EventStatusResult> => {
  try {
    const response = await getNextRount();
    const data = response.data;

    // 检查活动是否结束
    if (data?.next_round_timestamp === "") {
      return {
        status: EventStatus.ENDED,
        nextRoundTimestamp: "",
      };
    }

    // 检查活动是否未开始
    if (data?.error_message === "not started yet") {
      return {
        status: EventStatus.NOT_STARTED,
        nextRoundTimestamp: data?.next_round_timestamp || "",
      };
    }

    // 活动正常进行中
    return {
      status: EventStatus.ACTIVE,
      nextRoundTimestamp: data?.next_round_timestamp || "",
    };
  } catch (error) {
    console.error("检查活动状态失败:", error);
    return {
      status: EventStatus.ENDED,
      nextRoundTimestamp: "",
    };
  }
};

// 红包领取后的状态检查工具函数
const checkEventStatusAfterClaim = async (): Promise<EventStatusResult> => {
  try {
    const response = await getNextRount();
    const data = response.data;

    // 检查是否是最后一轮 - next_round_timestamp为"0001-01-01T00:00:00Z"
    if (data?.next_round_timestamp === "0001-01-01T00:00:00Z") {
      return {
        status: EventStatus.LASTROUNT,
        nextRoundTimestamp: "0001-01-01T00:00:00Z",
      };
    }

    // 检查活动是否结束 - next_round_timestamp为空字符串
    if (data?.next_round_timestamp === "") {
      return {
        status: EventStatus.ENDED,
        nextRoundTimestamp: "",
      };
    }

    // 有倒计时时间，需要显示倒计时
    if (data?.next_round_timestamp) {
      return {
        status: EventStatus.NOT_STARTED,
        nextRoundTimestamp: data.next_round_timestamp,
      };
    }

    // 默认返回活动结束
    return {
      status: EventStatus.ENDED,
      nextRoundTimestamp: "",
    };
  } catch (error) {
    console.error("检查红包领取后状态失败:", error);
    return {
      status: EventStatus.ENDED,
      nextRoundTimestamp: "",
    };
  }
};

// 状态重置工具函数
const createStateResetter = (
  setIsRedpocketFinished: (value: boolean) => void,
  setShowCountdownInError: (value: boolean) => void,
  setErrorInfo: (value: any) => void,
  setIsEventEnded: (value: boolean) => void,
  setIsEventNotStarted: (value: boolean) => void,
  setNextRoundTimestamp: (value: string) => void,
) => {
  return {
    resetToInput: () => {
      setIsRedpocketFinished(false);
      setShowCountdownInError(false);
      setErrorInfo(null);
      setIsEventEnded(false);
      setIsEventNotStarted(false);
    },
    resetToEventEnded: () => {
      setIsEventEnded(true);
      setIsRedpocketFinished(false);
      setShowCountdownInError(false);
      setErrorInfo(null);
    },
    resetToEventNotStarted: (timestamp: string) => {
      setIsEventNotStarted(true);
      setIsRedpocketFinished(false);
      setShowCountdownInError(false);
      setErrorInfo(null);
      setNextRoundTimestamp(timestamp);
    },
  };
};

const RedpocketPage: React.FC = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { magic } = useMagicStore();
  const { address, isConnected } = useUserAddress();
  const { isLoginModalOpen, openLoginModal, closeLoginModal } = useLoginModal();

  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [claimedAmount, setClaimedAmount] = useState<number>(0);
  const [creditAmount, setCreditAmount] = useState<number>(0);
  const [isRedpocketFinished, setIsRedpocketFinished] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [isEventEnded, setIsEventEnded] = useState(false);
  const [isEventNotStarted, setIsEventNotStarted] = useState(false);
  const [nextRoundTimestamp, setNextRoundTimestamp] = useState<string>("");
  const [showCountdownInError, setShowCountdownInError] = useState(false);
  const [errorInfo, setErrorInfo] = useState<{
    error: string;
    details?: any;
  } | null>(null);

  // 创建状态重置器
  const stateResetter = createStateResetter(
    setIsRedpocketFinished,
    setShowCountdownInError,
    setErrorInfo,
    setIsEventEnded,
    setIsEventNotStarted,
    setNextRoundTimestamp,
  );

  // 统一的倒计时处理函数
  const handleCountdownComplete = useCallback(async () => {
    const result = await checkEventStatus();

    switch (result.status) {
      case EventStatus.ENDED:
        stateResetter.resetToEventEnded();
        break;
      case EventStatus.NOT_STARTED:
        stateResetter.resetToEventNotStarted(result.nextRoundTimestamp);
        break;
      case EventStatus.ACTIVE:
        stateResetter.resetToInput();
        setNextRoundTimestamp(result.nextRoundTimestamp);
        break;
    }
  }, [stateResetter]);

  // 失败页面倒计时完成处理函数 - 直接跳转到红包首页重新刷新
  const handleFailedPageCountdownComplete = useCallback(() => {
    window.location.reload();
  }, []);

  // 页面加载控制
  useEffect(() => {
    const initializeEventStatus = async () => {
      const result = await checkEventStatus();

      switch (result.status) {
        case EventStatus.ENDED:
          setIsEventEnded(true);
          break;
        case EventStatus.NOT_STARTED:
          setIsEventNotStarted(true);
          break;
        default:
          break;
      }

      setNextRoundTimestamp(result.nextRoundTimestamp);

      setTimeout(() => {
        setIsPageLoading(false);
      }, 1500);
    };

    initializeEventStatus();
  }, []);

  // 处理确定按钮点击
  const handleConfirm = useCallback(async () => {
    if (!password.trim() || !magic) {
      return;
    }

    // 检查活动是否未开始
    if (isEventNotStarted) {
      return;
    }

    // 检查是否已登录
    if (!isConnected || !address) {
      openLoginModal();
      return;
    }

    setIsLoading(true);

    try {
      // 获取代理钱包地址
      const proxyWallet = await computeProxyAddress(address);
      if (!proxyWallet) {
        setIsRedpocketFinished(true);
        setIsLoading(false);
        return;
      }

      // 调用 backGetWebUser 接口检查用户信息
      const userResponse = await getWebUsersByProxyWallet(proxyWallet);

      if (!userResponse?.data?.web_users || userResponse.data.web_users.length === 0) {
        setIsRedpocketFinished(true);
        setIsLoading(false);
        return;
      }

      const webUser = userResponse.data.web_users[0];

      // 检查是否为 Google 登录
      if (webUser.social_media !== "google") {
        setIsRedpocketFinished(true);
        setIsLoading(false);
        return;
      }

      // 调用红包发放接口
      const claimResponse = await handleCreateRedPocketSignatureWithMagic(magic, password.trim());
      const result = processClaimResult(claimResponse);

      if (result.success) {
        setClaimedAmount(result.amount);
        setCreditAmount(result.credit);
        setShowSuccess(true);
      } else {
        // 红包领取失败后，立即调用getNextRount接口检查下轮状态
        const nextRoundResult = await checkEventStatusAfterClaim();

        setErrorInfo({
          error: result.error,
          details: result.details,
        });

        // 根据下轮状态决定是否显示倒计时
        if (nextRoundResult.status === EventStatus.LASTROUNT) {
          // 最后一轮，不显示倒计时，停在错误页
          setShowCountdownInError(false);
        } else if (
          nextRoundResult.nextRoundTimestamp &&
          nextRoundResult.nextRoundTimestamp !== "" &&
          nextRoundResult.nextRoundTimestamp !== "0001-01-01T00:00:00Z"
        ) {
          // 有有效的倒计时时间，显示倒计时
          setShowCountdownInError(true);
          setNextRoundTimestamp(nextRoundResult.nextRoundTimestamp);
        } else {
          // 没有倒计时，不显示倒计时
          setShowCountdownInError(false);
        }

        setIsRedpocketFinished(true);
      }
    } catch (error: any) {
      setIsRedpocketFinished(true);
    } finally {
      setIsLoading(false);
    }
  }, [password, isConnected, address, openLoginModal, magic, isEventNotStarted]);

  // 跳转到首页
  const handleGoHome = () => {
    router.push("/");
  };

  // 回退到红包输入页面
  const handleGoBack = () => {
    setIsRedpocketFinished(false);
    setShowSuccess(false);
    setPassword("");
    setIsLoading(false);
    setErrorInfo(null);
    setClaimedAmount(0);
    setCreditAmount(0);
    setShowCountdownInError(false);
  };

  // 页面加载状态 - 等待address和isConnected加载完成
  if (isPageLoading) {
    return (
      <div className="relative h-[calc(100vh-128px)] flex flex-col items-center justify-center bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50">
        <div className="flex flex-col items-center space-y-4">
          {/* 加载动画 */}
          <div className="w-16 h-16 border-4 border-amber-200 border-t-amber-500 rounded-full animate-spin"></div>

          {/* 加载文字 */}
          <div className="text-amber-700 font-semibold text-lg">{t("redpocket.initializing")}</div>

          {/* 装饰元素 */}
          <div className="absolute top-20 left-20 w-12 h-12 bg-amber-300 rounded-full blur-xl animate-pulse opacity-30"></div>
          <div className="absolute bottom-32 right-16 w-16 h-16 bg-yellow-300 rounded-full blur-xl animate-pulse delay-1000 opacity-30"></div>
        </div>
      </div>
    );
  }

  // 活动结束页面
  if (isEventEnded) {
    return (
      <FailedView
        onGoHome={handleGoHome}
        onGoBack={handleGoBack}
        errorInfo={{ error: "event_ended" }}
        isEventEnded={true}
      />
    );
  }

  // 成功页面
  if (showSuccess) {
    return <SuccessView claimedAmount={claimedAmount} creditAmount={creditAmount} onGoHome={handleGoHome} />;
  }

  // 红包已领完页面
  if (isRedpocketFinished) {
    return (
      <FailedView
        onGoHome={handleGoHome}
        onGoBack={handleGoBack}
        errorInfo={errorInfo}
        showCountdown={showCountdownInError}
        nextRoundTimestamp={nextRoundTimestamp}
        onCountdownComplete={handleFailedPageCountdownComplete}
      />
    );
  }

  // 主页面
  return (
    <InputView
      password={password}
      isLoading={isLoading}
      isLoginModalOpen={isLoginModalOpen}
      onPasswordChange={setPassword}
      onConfirm={handleConfirm}
      onCloseLoginModal={closeLoginModal}
      isEventNotStarted={isEventNotStarted}
      nextRoundTimestamp={nextRoundTimestamp}
      onCountdownComplete={handleCountdownComplete}
    />
  );
};

export default RedpocketPage;
