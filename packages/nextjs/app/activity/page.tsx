"use client";

import React from "react";

// import RecentActivityItem from "@/components/other/RecentActivityItem";
// import { recentActivityMockList } from "@/components/other/mock";
// import { Select, SelectItem } from "@heroui/react";

// const minAmountOptions = ["None", "$10", "$100", "$1,000", "$10,000", "$100,000"];

const Activity: React.FC = () => {
  // const [recentActivityList, setRecentActivityList] = useState<any[]>([]);
  // const [selectedMinAmount, setSelectedMinAmount] = useState<string>("None");

  // const containerRef = useRef<HTMLDivElement>(null);
  // 10s 查询一次，更新数据
  // https://data-api.polymarket.com/trades?takerOnly=true&limit=50&offset=0&filterType=CASH&filterAmount=1

  // loadmore 逻辑
  // https://data-api.polymarket.com/trades?takerOnly=true&limit=50&offset=100&filterType=CASH&filterAmount=1

  // useEffect(() => {
  //   setRecentActivityList(recentActivityMockList.slice(0, 20));

  //   // 模拟 WebSocket 连接
  //   const ws = new WebSocket("wss://example.com/socket");

  //   ws.onmessage = event => {
  //     const newActivity = JSON.parse(event.data);
  //     setRecentActivityList(prevList => [newActivity, ...prevList].slice(0, 40));
  //   };

  //   return () => {
  //     ws.close();
  //   };
  // }, []);

  // const loadMore = () => {
  //   setRecentActivityList(prevList => {
  //     const newList = recentActivityMockList;
  //     const combinedList = [...prevList, ...newList];
  //     return combinedList.slice(0, 40); // 保持列表长度不超过40条
  //   });
  // };

  // const handleScroll = () => {
  //   if (containerRef.current) {
  //     const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
  //     if (scrollTop + clientHeight >= scrollHeight) {
  //       loadMore();
  //     }
  //   }
  // };

  // const handleSelectChange = (value: string) => {
  //   setSelectedMinAmount(value);
  // };

  return (
    <div className="flex flex-col items-center w-full">
      <div className="flex items-center justify-between w-full px-4 sm:px-0">
        <p className="text-3xl font-bold">{"Activity"}</p>

        {/* <Select
          placeholder="Min amount"
          value={selectedMinAmount}
          className="w-40"
          onChange={e => handleSelectChange(e.target.value)}
        >
          {minAmountOptions.map(amount => (
            <SelectItem key={amount}>{amount}</SelectItem>
          ))}
        </Select> */}
      </div>

      <div className="flex-center h-full w-full px-4 sm:px-0">
        <div className="flex flex-col w-full h-full p-2">
          <p className="text-lg text-gray-500 font-semibold">under construction...</p>
        </div>
      </div>
    </div>
  );
};

export default Activity;
