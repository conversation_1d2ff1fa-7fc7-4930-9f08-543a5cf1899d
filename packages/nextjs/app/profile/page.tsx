"use client";

import React from "react";

// import ProfileContent from "@/components/profile/ProfileContent";
// import ProfileHeader from "@/components/profile/ProfileHeader";
// import { useAccount } from "wagmi";

const ProfilePage: React.FC = () => {
  // const { address } = useAccount();

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="max-w-5xl w-full p-4">
        <div className="text-2xl font-bold text-center text-gray-500 mt-16"> under development...</div>

        {/* <ProfileHeader address={address} />
        <ProfileContent /> */}
      </div>
    </div>
  );
};

export default ProfilePage;
