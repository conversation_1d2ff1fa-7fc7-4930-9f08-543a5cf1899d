"use client";

import React from "react";

// import SideBar from "@/components/profile/setting-components/SideBar";
// import { Button, Image, Input, Textarea } from "@heroui/react";
// import { Camera } from "lucide-react";
// import { getAddress } from "viem";
// import { useAccount } from "wagmi";
// import { BlockieAvatar } from "~~/components/scaffold-eth";

const SettingPage = () => {
  // const { address } = useAccount();
  // const checkSumAddress = address && getAddress(address);
  // const [email, setEmail] = useState("<EMAIL>");
  // const [username, setUsername] = useState("superxiaojie");
  // const [bio, setBio] = useState("web3 dev");
  // const [photo, setPhoto] = useState<string | null>(null);

  // const handlePhotoChange = (url: string) => {
  //   setPhoto(url);
  // };

  // // const handleSaveChanges = () => {
  // //   // 保存更改的逻辑
  // // };

  // const sidebarItems = ["Profile", "Settings", "Logout"];
  // const handleSelect = (item: string) => {
  //   console.log("Selected item:", item);
  // };

  return (
    <div>
      <div className="text-2xl font-bold text-center text-gray-500 mt-16"> under development...</div>
      {/* <div className="flex items-start justify-center mx-6 px-6 pt-10">
        <SideBar items={sidebarItems} onSelect={handleSelect} />

        <div className="max-w-4xl w-full p-4">
          <div className="text-xl font-bold mb-4">Profile Settings</div>
          <div className="mb-4">
            <div className="flex items-center space-x-4">
              {photo ? (
                <Image className=" rounded-full" alt="Profile image" width={72} height={72} src={photo} />
              ) : (
                checkSumAddress && <BlockieAvatar address={checkSumAddress} size={72} />
              )}
              <Button
                type="button"
                size="sm"
                radius="full"
                onPress={() => document.getElementById("photoUpload")?.click()}
                className="bg-gray-200 font-semibold p-4"
              >
                <Camera className="size-5" />
                Upload
              </Button>
              <input
                type="file"
                id="photoUpload"
                style={{ display: "none" }}
                onChange={e => {
                  const files = e.target.files;
                  if (files && files[0]) {
                    const reader = new FileReader();
                    reader.onloadend = () => {
                      handlePhotoChange(reader.result as string);
                    };
                    reader.readAsDataURL(files[0]);
                  }
                }}
              />
            </div>
          </div>
          <form>
            <div className="mb-4">
              <div className="mb-2 font-semibold">Email</div>
              <Input
                variant="flat"
                size="lg"
                placeholder=""
                className="border rounded-md"
                onChange={e => setEmail(e.target.value)}
                value={email}
                classNames={{
                  inputWrapper: ["bg-transparent"],
                }}
                type="text"
              />
              <p className="text-sm text-gray-500">
                Not verified <span className="text-blue-500 cursor-pointer">Resend</span>
              </p>
            </div>
            <div className="mb-4">
              <div className="mb-2 font-semibold">Username</div>
              <Input
                variant="flat"
                size="lg"
                placeholder="Name"
                className="border rounded-md"
                onChange={e => setUsername(e.target.value)}
                value={username}
                classNames={{
                  inputWrapper: ["bg-transparent"],
                }}
                type="text"
              />
            </div>
            <div className="mb-4">
              <div className="mb-2 font-semibold">Bio</div>
              <Textarea
                variant="flat"
                size="lg"
                placeholder="Write your bio..."
                className="border rounded-md"
                onChange={e => setBio(e.target.value)}
                value={bio}
                classNames={{
                  inputWrapper: ["bg-transparent"],
                }}
              />
            </div>
            <Button
              type="button"
              size="lg"
              radius="lg"
              // onPress={}
              className="font-semibold p-4 bg-blue-500 text-white"
            >
              Save changes
            </Button>
          </form>
        </div>
      </div> */}
    </div>
  );
};

export default SettingPage;
