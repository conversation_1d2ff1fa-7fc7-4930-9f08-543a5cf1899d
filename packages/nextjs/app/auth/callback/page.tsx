"use client";

import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useMagicAuth } from "@/hooks/useMagicAuth";
import { Modal, ModalBody, ModalContent, ModalHeader } from "@heroui/react";

export default function AuthCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState("initializing");
  const [error, setError] = useState("");
  const [currentStep, setCurrentStep] = useState("");

  const hasExecuted = useRef(false);
  const isProcessing = useRef(false);

  const { processMagicAuth } = useMagicAuth({
    setStatus,
    setError,
    setCurrentStep,
    router,
  });

  useEffect(() => {
    // Executed only once in initialization state
    if (hasExecuted.current || isProcessing.current || status !== "initializing") {
      return;
    }

    const handleMagicCallback = async () => {
      isProcessing.current = true;
      hasExecuted.current = true;

      try {
        // 添加更详细的调试信息
        console.log("=== Auth Callback Debug ===");
        console.log("Current URL:", window.location.href);
        console.log("All search params:", Object.fromEntries(searchParams.entries()));

        // 检查所有可能的 OAuth 2.0 参数
        const allParams = {
          provider: searchParams.get("provider"),
          magic_credential: searchParams.get("magic_credential"),
          state: searchParams.get("state"),
          code: searchParams.get("code"),
          oauth_token: searchParams.get("oauth_token"),
          oauth_verifier: searchParams.get("oauth_verifier"),
          error: searchParams.get("error"),
          error_description: searchParams.get("error_description"),
        };

        console.log("OAuth params:", allParams);

        // 检查是否有错误
        if (allParams.error) {
          console.error("OAuth error:", allParams.error, allParams.error_description);
          setStatus("error");
          setError(`OAuth Error: ${allParams.error} - ${allParams.error_description}`);
          return;
        }

        // 检查是否有 Magic OAuth 相关参数
        const hasOAuthParams = !!(
          allParams.magic_credential ||
          allParams.state ||
          allParams.code ||
          allParams.oauth_token
        );

        console.log("Has OAuth params:", hasOAuthParams);

        if (!hasOAuthParams) {
          console.log("No valid OAuth params found, redirecting to home");
          router.replace("/");
          return;
        }

        console.log(`Processing ${allParams.provider || "unknown"} OAuth callback`);
        setCurrentStep(`Processing ${allParams.provider || "unknown"} login...`);

        // Handle the Magic certification process
        await processMagicAuth();
      } catch (error) {
        console.error("Magic OAuth callback error:", error);
        setStatus("error");
        setError(error instanceof Error ? error.message : "未知错误");
      } finally {
        isProcessing.current = false;
      }
    };

    handleMagicCallback();
  }, [router, searchParams, status, processMagicAuth]);

  const handleClose = () => {
    if (window.opener) {
      window.opener.postMessage({ type: "MAGIC_LOGIN_ERROR", error }, window.location.origin);
      window.close();
    } else {
      router.replace("/");
    }
  };

  return (
    <Modal
      isOpen={true}
      onClose={handleClose}
      isDismissable={status !== "loading"}
      hideCloseButton={status === "loading"}
      size="md"
      classNames={{
        backdrop: "bg-black/50",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex-center gap-4">
          <div className="py-2 text-lg font-semibold">Magic</div>
        </ModalHeader>

        <ModalBody className="px-8 pb-8">
          <div className="w-full flex-center flex-col gap-2">
            <div className="relative flex items-center justify-center mb-4">
              <div className="relative w-24 h-24 flex items-center justify-center">
                <div className="absolute inset-0 border-4 border-gray-200 rounded-full"></div>
                {status === "loading" && (
                  <div className="absolute inset-0 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
                )}
                <div className="relative z-10 bg-white rounded-full p-2 flex items-center justify-center">
                  <Image
                    src="/magic_color_logo.png"
                    alt="Magic"
                    width={32}
                    height={32}
                    className="w-16 h-16 object-contain"
                    priority
                  />
                </div>
              </div>
            </div>

            {status === "loading" && (
              <>
                <div className="text-lg font-semibold text-gray-800">Processing...</div>
                <div className="text-sm text-gray-600 text-center min-h-[20px]">{currentStep}</div>
                <div className="text-sm text-gray-600 text-center min-h-[20px]">
                  Redirecting... Please keep this window open. This will only take a few seconds.
                </div>
              </>
            )}

            {status === "success" && (
              <>
                <div className="text-lg font-semibold text-green-600">Success!</div>
                <div className="text-sm text-gray-600 text-center">Login successfully, skipping...</div>
              </>
            )}

            {status === "error" && (
              <>
                <div className="text-lg font-semibold text-red-600">Error</div>
                <div className="text-sm text-red-500 text-center">{error}</div>
              </>
            )}

            {status === "initializing" && (
              <>
                <div className="text-lg font-semibold text-gray-800">Redirecting...</div>
                <div className="text-sm text-gray-400 text-center">Please wait while we redirect you.</div>
              </>
            )}
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
