"use client";

import React from "react";
import dynamic from "next/dynamic";

const FileViewer = dynamic(() => import("react-file-viewer"), { ssr: false });

const ToePage: React.FC = () => {
  const filePath = "/toe.docx";
  const fileType = "docx";

  return (
    <div className="flex justify-center pt-2 px-4 md:px-0">
      <div className="w-full flex flex-col md:max-w-[1024px] italic-text">
        <FileViewer fileType={fileType} filePath={filePath} />
      </div>
    </div>
  );
};

export default ToePage;
