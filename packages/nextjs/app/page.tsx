"use client";

import React, { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import RegionWaringModal from "@/components/other/RegionWaringModal";
import PromotionTemporaryModal from "@/components/promotion/PromotionTemporaryModal";
import FloatingRedPacketIcon from "@/components/redpocket/FloatingRedPacketIcon";
import { usePromotionModal } from "@/hooks/usePromotionModal";
import { checkGeolocation, getItem, setItem } from "@/utils/other/storage";
import type { NextPage } from "next";
import Banner from "~~/components/events-components/Banner";
import EventCardList from "~~/components/events-components/EventCardList";

const ActivityBanner = dynamic(() => import("~~/components/events-components/ActivityBanner"), {
  ssr: false,
});

const Home: NextPage = () => {
  const [isClient, setIsClient] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const { promotionVisible, setPromotionVisible } = usePromotionModal();

  useEffect(() => {
    setIsClient(true);
    fetchGeoInfo();
    fetchInvitationCode();
  }, []);

  const fetchGeoInfo = async (): Promise<void> => {
    try {
      const regionAllowed = await checkGeolocation();
      if (regionAllowed) return;

      const lastShown = getItem("lastRegionWarningShown");
      const now = new Date().getTime();
      const oneDay = 24 * 60 * 60 * 1000;

      if (!regionAllowed && !lastShown && now - lastShown > oneDay) {
        setModalVisible(true);
        setItem("lastRegionWarningShown", now);
      }
    } catch (error) {
      console.error("Failed to fetch geo info:", error);
    }
  };

  const fetchInvitationCode = (): void => {
    const params = new URLSearchParams(window.location.search);
    const invitationCode = params.get("invitationCode");
    if (invitationCode) {
      setItem("invitationCode", invitationCode);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className="flex flex-col justify-center items-center md:px-16 px-4 py-4">
      <ActivityBanner />
      <Banner />
      <div className="w-full max-w-[1450px] flex flex-col items-center">
        <EventCardList />
      </div>
      <PromotionTemporaryModal visible={promotionVisible} onClose={() => setPromotionVisible(false)} />
      <RegionWaringModal visible={modalVisible} onClose={() => setModalVisible(false)} />

      {/* 浮动红包图标 */}
      <FloatingRedPacketIcon />
    </div>
  );
};

export default Home;
