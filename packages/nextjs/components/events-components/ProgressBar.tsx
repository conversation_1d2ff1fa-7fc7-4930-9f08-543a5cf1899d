import React from "react";

interface ProgressBarProps {
  percentage: number;
  scale?: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ percentage, scale = 1 }) => {
  const radius = 25;
  const circumference = Math.PI * radius; // 半圆的周长

  const validPercentage = isFinite(percentage) ? Math.round(percentage) : 0;

  const offset = circumference - (validPercentage / 100) * circumference;
  const strokeColor = validPercentage > 50 ? "rgb(34 197 94)" : "#E64800";

  return (
    <div
      className="flex relative w-20 h-14 items-center justify-center"
      style={{ transform: `scale(${scale})` }} // 使用 transform: scale 控制缩放
    >
      <svg width="70" height="40" viewBox="0 0 70 40" className="block flex-center -mt-4">
        <path d="M 10 35 A 25 25 0 0 1 60 35" fill="none" stroke="#E0E0E0" strokeWidth="4" />
        <path
          d="M 10 35 A 25 25 0 0 1 60 35"
          fill="none"
          stroke={strokeColor}
          strokeOpacity="0.6"
          strokeWidth="4"
          strokeDasharray={circumference}
          strokeDashoffset={offset}
        />
      </svg>
      <div className="absolute text-center top-5">
        <div className="text-gray-900 font-bold text-sm">{validPercentage}%</div>
        <div className="text-gray-500 font-medium text-xs -mt-1">chance</div>
      </div>
    </div>
  );
};

export default ProgressBar;
