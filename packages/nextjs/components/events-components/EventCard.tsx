import React, { useEffect, useState } from "react";
import Image from "next/image";
import ProgressBar from "./ProgressBar";
import { EventCardProps, Market } from "./type";
import {
  base64DecodeByEn,
  base64DecodeByLanguage,
  formatLargeAmountWithCommas,
  formatLasttradeprice,
  getTotalVolumeClob,
  hasExcludedTags,
  isWithinOneDays,
  sortEventMarkets,
} from "@/utils";
import { Link } from "@heroui/link";
import { CheckCircle2, SquareCheck } from "lucide-react";
import {
  ChatBubbleOvalLeftIcon,
  ChevronDoubleDownIcon,
  ChevronDoubleUpIcon,
  GiftIcon,
  StarIcon,
} from "@heroicons/react/24/outline";
import { useGlobalState } from "~~/services/store/store";

// 文本处理函数：将中划线或下划线转换为空格并首字母大写
const formatDisplayText = (text: string): string => {
  if (!text) return text;

  return text
    .replace(/[-_]/g, " ") // 将中划线和下划线替换为空格
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // 每个单词首字母大写
    .join(" ");
};

function getOutcomeResult(outcome_prices?: string[]): [boolean, string] {
  if (
    Array.isArray(outcome_prices) &&
    outcome_prices.length === 2 &&
    (outcome_prices[0] === "1" || outcome_prices[1] === "1")
  ) {
    if (outcome_prices[0] === "1" && outcome_prices[1] === "0") {
      return [true, "Yes"];
    } else if (outcome_prices[0] === "0" && outcome_prices[1] === "1") {
      return [true, "No"];
    } else {
      return [true, "Unknown"];
    }
  }
  return [false, ""];
}

const ResultStatic: React.FC<{ resultText: string }> = ({ resultText }) => (
  <div className="flex-1 flex items-center justify-center">
    <div className="flex flex-center h-10 w-full text-sm bg-blue-100 text-blue-700 rounded-lg px-3 font-semibold">
      <CheckCircle2 className="w-4 h-4 text-blue-600 mr-2" />
      {resultText}
    </div>
  </div>
);

const EventCardVote: React.FC<any> = props => {
  const { slug, tid, timeStamp, defaultConditionId, current_language, eventCardData } = props;

  const questionMarket = props?.currentData?.question_market;
  if (!questionMarket) {
    return null;
  }

  const { id, question: groupItemTitle, lasttradeprice, outcome_prices } = questionMarket;
  const [isResult, resultText] = getOutcomeResult(outcome_prices);

  // 判断是否为单边事件
  const isSingleSidedEvent = hasExcludedTags(eventCardData);

  return (
    <div className="flex items-center justify-between py-1 px-1 rounded-md hover:bg-gray-50 transition-colors duration-200 min-w-0">
      <Link
        href={`/event/${slug}?tid=${tid}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}&qid=${id}`}
        className="text-sm font-medium text-gray-800 hover:text-blue-600 hover:underline transition-colors duration-200 flex-1 mr-2 min-w-0"
      >
        <span className="block truncate">{formatDisplayText(base64DecodeByLanguage(groupItemTitle))}</span>
      </Link>
      <div className="flex items-center gap-1 flex-shrink-0">
        <div className="text-xs font-bold text-gray-700 bg-gray-100 px-2 py-1 rounded-md min-w-[2.5rem] text-center">
          {formatLasttradeprice(lasttradeprice)}
        </div>
        {isResult ? (
          <div className="flex items-center text-xs bg-blue-100 text-blue-700 rounded-lg px-2 py-1 cursor-default">
            <CheckCircle2 className="w-3 h-3 text-blue-600 mr-1" />
            {resultText}
          </div>
        ) : isSingleSidedEvent ? (
          // 单边事件只显示 Yes 按钮
          <button className="text-xs font-semibold bg-green-100 text-green-600 rounded-lg px-2 py-1 hover:bg-green-200 transition-all duration-200 flex-shrink-0">
            Yes
          </button>
        ) : (
          // 双边事件显示 Yes 和 No 按钮
          <div className="flex gap-1 flex-shrink-0">
            <button className="text-xs font-semibold bg-green-100 text-green-600 rounded-lg px-1.5 py-1 hover:bg-green-200 transition-all duration-200">
              Yes
            </button>
            <button className="text-xs font-semibold bg-red-100 text-red-600 rounded-lg px-1.5 py-1 hover:bg-red-200 transition-all duration-200">
              No
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

const EventCardOperation: React.FC<any> = props => {
  const currentItemData = props?.currentItemData;
  if (!currentItemData) {
    return null;
  }

  const { event_markets = [], commentCount = 0, createdAt } = currentItemData;
  const volumeclob = getTotalVolumeClob(event_markets);

  return (
    <div className="h-8 flex items-center justify-between px-1">
      <div className="flex items-center">
        {isWithinOneDays(createdAt) ? (
          <span className="h-6 px-2 flex items-center gap-1 text-yellow-700 bg-yellow-100 text-xs font-semibold rounded-full">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="size-3 text-yellow-600"
            >
              <path
                fillRule="evenodd"
                d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z"
                clipRule="evenodd"
              />
            </svg>
            NEW
          </span>
        ) : (
          <span className="text-gray-600 text-xs font-medium px-2 py-1 rounded-md">
            ${formatLargeAmountWithCommas(volumeclob)} Vol.
          </span>
        )}
      </div>
      <div className="flex items-center gap-3">
        <GiftIcon className="h-4 w-4 text-gray-500 hover:text-gray-700 transition-colors cursor-pointer" />
        <div className="flex items-center gap-1 text-gray-500 hover:text-gray-700 transition-colors cursor-pointer">
          <ChatBubbleOvalLeftIcon className="h-4 w-4" />
          <span className="text-xs font-medium">{commentCount || 0}</span>
        </div>
        <StarIcon className="h-4 w-4 text-gray-500 hover:text-yellow-500 transition-colors cursor-pointer" />
      </div>
    </div>
  );
};

const EventCard: React.FC<EventCardProps> = props => {
  const { eventCardData, timeStamp } = props;

  const [title, setTitle] = useState(formatDisplayText(base64DecodeByLanguage(eventCardData.title)));
  const [slug, setSlug] = useState(base64DecodeByEn(eventCardData.slug));
  const [defaultConditionId, setDefaultConditionId] = useState<number | null>(null);
  const isSingleQuestion = eventCardData.event_markets.length === 1;

  // 判断是否为单边事件
  const isSingleSidedEvent = hasExcludedTags(eventCardData);

  const { current_language } = useGlobalState().nativeCurrency;
  const sortedEventMarkets = sortEventMarkets(eventCardData.event_markets);

  const firstEventMarket = eventCardData?.event_markets?.[0];
  const questionMarket = firstEventMarket?.question_market;
  const lasttradeprice = questionMarket?.lasttradeprice;
  const percentageNum = lasttradeprice ? Math.round(lasttradeprice * 100) : 0;

  useEffect(() => {
    setTitle(formatDisplayText(base64DecodeByLanguage(eventCardData.title)));
    const newSlug = base64DecodeByEn(eventCardData.slug);
    setSlug(newSlug + "-" + current_language);

    const conditionId = eventCardData.event_markets[0]?.condition_id;
    setDefaultConditionId(conditionId);
  }, [current_language, eventCardData]);

  const onClickBtn = () => {
    const url = `/event/${slug}?tid=${eventCardData.id}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}`;
    window.location.href = url;
  };

  return (
    <div className="h-[180px] flex-shrink-0 rounded-lg border border-gray-200 bg-white hover:border-gray-300 transition-colors duration-200 overflow-hidden">
      <div className="flex flex-col justify-between h-full relative">
        {/* Header Section */}
        <div className="flex h-14 items-center justify-between px-3 pt-2 flex-shrink-0">
          <div className="flex items-center justify-start gap-2 flex-1">
            <div className="relative">
              <Image
                className="h-9 w-9 object-cover rounded-lg border border-gray-100"
                src={eventCardData.image || "/image_alt.jpg"}
                alt=""
                width={36}
                height={36}
                onError={e => {
                  e.currentTarget.src = "/image_alt.jpg";
                }}
              />
            </div>
            <Link
              href={`/event/${slug}?tid=${eventCardData.id}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}`}
              className="text-sm font-semibold text-gray-800 hover:text-blue-600 hover:underline transition-colors duration-200 flex-1"
            >
              <span className="line-clamp-2 leading-tight">{title}</span>
            </Link>
          </div>

          {/* Progress/Status Indicator */}
          {(() => {
            const outcome_prices = firstEventMarket?.question_market?.outcome_prices;
            const [isResult] = getOutcomeResult(outcome_prices);
            if (isSingleQuestion) {
              if (isResult) {
                return (
                  <div className="flex flex-col items-center justify-center mr-2">
                    <div className="bg-blue-100 p-[6px] rounded-lg">
                      <SquareCheck className="size-5" color="#2563eb" />
                    </div>
                    <span className="text-xs font-semibold text-blue-600">Done</span>
                  </div>
                );
              }
              if (lasttradeprice) {
                return <ProgressBar percentage={Number(percentageNum)} />;
              }
            }
            return null;
          })()}
        </div>

        {/* Content Section */}
        <div className="flex flex-col w-full px-3 flex-1 min-h-0">
          {eventCardData.event_markets.length > 1 ? (
            <div className="flex flex-col gap-0.5 flex-1 overflow-y-auto no-scrollbar rounded-lg p-1 min-h-0">
              {sortedEventMarkets.map((item: Market, index: number) => (
                <EventCardVote
                  key={`${item.condition_id}_${index}`}
                  currentData={item}
                  slug={slug}
                  tid={eventCardData.id}
                  current_language={current_language}
                  defaultConditionId={defaultConditionId}
                  timeStamp={timeStamp}
                  eventCardData={eventCardData}
                />
              ))}
            </div>
          ) : (
            <div className="flex w-full flex-1 items-end pb-2">
              {(() => {
                const outcome_prices = firstEventMarket?.question_market?.outcome_prices;
                const [isResult, resultText] = getOutcomeResult(outcome_prices);
                if (isResult) {
                  return <ResultStatic resultText={resultText} />;
                }

                if (isSingleSidedEvent) {
                  // 单边事件只显示 Yes 按钮
                  return (
                    <button
                      onClick={onClickBtn}
                      className="h-10 flex-1 flex items-center justify-center text-sm font-semibold bg-green-100 text-green-600 rounded-lg px-4 py-2 hover:bg-green-200 transform hover:scale-[1.02] transition-all duration-200 active:scale-[0.98]"
                    >
                      <span>Buy Yes</span>
                      <ChevronDoubleUpIcon className="size-4 ml-2 text-green-600" />
                    </button>
                  );
                }

                // 双边事件显示 Yes 和 No 按钮
                return (
                  <div className="flex w-full gap-2">
                    <button
                      onClick={onClickBtn}
                      className="h-10 flex-1 flex items-center justify-center text-sm font-semibold bg-green-100 text-green-600 rounded-lg px-3 py-2 hover:bg-green-200 transform hover:scale-[1.02] transition-all duration-200 active:scale-[0.98]"
                    >
                      <span>Buy Yes</span>
                      <ChevronDoubleUpIcon className="size-4 ml-1 text-green-600" />
                    </button>
                    <button
                      onClick={onClickBtn}
                      className="h-10 flex-1 flex items-center justify-center text-sm font-semibold bg-red-100 text-red-600 rounded-lg px-3 py-2 hover:bg-red-200 transform hover:scale-[1.02] transition-all duration-200 active:scale-[0.98]"
                    >
                      <span>Buy No</span>
                      <ChevronDoubleDownIcon className="size-4 ml-1 text-red-600" />
                    </button>
                  </div>
                );
              })()}
            </div>
          )}
        </div>

        {/* Footer Section */}
        <div className="w-full flex-shrink-0">
          <EventCardOperation currentItemData={eventCardData} />
        </div>
      </div>
    </div>
  );
};

export default EventCard;
