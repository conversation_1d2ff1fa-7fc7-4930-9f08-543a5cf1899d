export interface EventCardProps {
  eventCardData: any;
  timeStamp: number;
}

export interface Market {
  id: string;
  groupItemTitle: string;
  currentOutcomePrices: string;
  slug: string;
  condition_id: string;
}

export interface BannerData {
  title: string;
  sub_title?: string;
  image_url: string;
  event_id: string;
  feature: boolean;
}

export interface BannerItemProps {
  itemData: BannerData;
}
