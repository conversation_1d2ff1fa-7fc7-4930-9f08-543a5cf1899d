import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { BannerData, BannerItemProps } from "./type";
import { getBannerByRegion } from "@/api/markets";
import Glide from "@glidejs/glide";
import { Card, CardFooter, Link } from "@heroui/react";
import { useGlobalState } from "~~/services/store/store";

const BannerItem: React.FC<BannerItemProps> = ({ itemData }) => {
  const { title, image_url, sub_title, event_id } = itemData;
  const slut = title.replace(/\?/g, "").replace(/ /g, "-");
  return (
    <Card isFooterBlurred className="w-full h-[300px] col-span-12 sm:col-span-7">
      <Image
        alt=""
        className="z-0 w-full h-full object-cover"
        src={image_url}
        width={512}
        height={512}
        onError={e => {
          e.currentTarget.src = "/image_alt.jpg";
        }}
      />
      <CardFooter className="absolute flex gap-2 bg-black/40 bottom-0 z-10 border-t-1 border-default-600 dark:border-default-100">
        <div className="flex flex-grow gap-2 items-center">
          <div className="flex flex-col justify-center text-white w-full">
            <div className="text-lg font-semibold">{title}</div>
            {sub_title && <div className="text-sm flex-grow min-h-[20px] mt-2">{sub_title}</div>}
          </div>
        </div>
        <Link
          href={`/event/${slut}?tid=${event_id}`}
          className="px-4 py-2 text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 no-underline relative z-20"
        >
          Trade
        </Link>
      </CardFooter>
    </Card>
  );
};

const Banner: React.FC = () => {
  const currentPath = usePathname() || "";
  const { current_language } = useGlobalState().nativeCurrency;
  const [bannerList, setBannerList] = useState<BannerData[]>([]);
  const maxBannerWidth = currentPath.includes("markets") ? "max-w-[1920px]" : "max-w-[1450px]";
  const glideRef = useRef<HTMLDivElement>(null);
  const glideInstance = useRef<any>(null);

  useEffect(() => {
    const fetchBannerData = async () => {
      try {
        const response = await getBannerByRegion(current_language);
        const filteredBanners = response.data.banner.filter((item: BannerData) => item.feature);
        setBannerList(filteredBanners);
      } catch (error) {
        console.error("Failed to fetch banner data:", error);
      }
    };

    fetchBannerData();
  }, [current_language]);

  useEffect(() => {
    const updateGlide = () => {
      if (glideRef.current && bannerList.length > 0) {
        if (glideInstance.current) {
          glideInstance.current.destroy();
          glideInstance.current = null;
        }
        const perView = window.innerWidth < 640 ? 1 : Math.max(1, Math.min(4, Math.floor(window.innerWidth / 300)));
        glideInstance.current = new Glide(glideRef.current, {
          type: "carousel",
          startAt: 0,
          perView: perView,
          gap: 16,
          // autoplay: 5000,
          touchRatio: 0.5,
          dragThreshold: 80,
          swipeThreshold: 80,
        });
        glideInstance.current.mount();
      }
    };

    updateGlide();
    window.addEventListener("resize", updateGlide);

    return () => {
      if (glideInstance.current) {
        glideInstance.current.destroy();
        glideInstance.current = null;
      }
      window.removeEventListener("resize", updateGlide);
    };
  }, [bannerList]);

  return (
    <div className={`w-full relative flex flex-center ${maxBannerWidth}`}>
      {bannerList.length > 0 && (
        <div className="w-full relative" ref={glideRef}>
          <div className="glide__track" data-glide-el="track">
            <ul className="glide__slides bg-white">
              {bannerList.map((item, index) => (
                <li key={index} className="glide__slide bg-transparent">
                  <BannerItem itemData={item} />
                </li>
              ))}
            </ul>
          </div>
          <div className="absolute inset-y-0 left-0 flex items-center" data-glide-el="controls">
            <button className="glide__arrow glide__arrow--left z-10" data-glide-dir="<">
              {"<"}
            </button>
          </div>
          <div className="absolute inset-y-0 right-0 flex items-center" data-glide-el="controls">
            <button className="glide__arrow glide__arrow--right z-10" data-glide-dir=">">
              {">"}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Banner;
