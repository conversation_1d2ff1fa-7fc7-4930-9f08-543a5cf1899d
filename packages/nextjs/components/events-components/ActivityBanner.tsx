import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { getActivityBannerList } from "@/api/events";
import { getInformationListDataByQuestionId } from "@/api/order";
import { base64DecodeByEn, base64DecodeByLanguage, calculateTimeToNow, getCurrentTimestamp } from "@/utils";
import { Image, Link } from "@heroui/react";
import { useTranslation } from "react-i18next";
import { BlockieAvatar } from "~~/components/scaffold-eth";
import { useGlobalState } from "~~/services/store/store";

interface LastTrade {
  tokenId: string;
  maker: string;
  signer: string;
  maker_amt: string;
  taker_amt: string;
  maker_amount_original: string;
  taker_amount_original: string;
  filled_timestamp: string;
  question_market_id: {
    Int64: number;
    Valid: boolean;
  };
}

interface MarketInfo {
  id: number;
  slug: string;
  question: string;
  clob_token_ids: string[];
  icon: string;
  image: string;
  outcome_prices: any;
  event_markets: Array<{
    event_id: number;
    event: {
      slug: string;
    };
    condition_id: string;
  }>;
}

interface ComputedTradeData {
  marketId: number;
  questionText: string;
  slugText: string;
  currentPrice: number;
  timeAgo: string;
  makerAvatar: `0x${string}`;
  isYes: boolean;
  isBuy: boolean;
  eventId: number;
  conditionId: string | null;
  questionId: number | null;
}

const generateRandomAvatar = (address: string, timestamp: string): `0x${string}` => {
  const createHash = (str: string) => {
    if (!str || typeof str !== "string") {
      return 0;
    }

    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = ((hash << 5) - hash + str.charCodeAt(i)) & 0xffffffff;
    }
    return hash;
  };

  // 确保 address 和 timestamp 不为空
  const safeAddress = address || "0x0000000000000000000000000000000000000000";
  const safeTimestamp = timestamp || "0";

  const combinedHash = (createHash(safeAddress) + createHash(safeTimestamp)) & 0xffffffff;
  const randomSuffix = Math.abs(combinedHash) % 99999;
  const baseHex = safeAddress.slice(2, 10);
  const randomHex = randomSuffix.toString(16).padStart(5, "0");

  return `0x${baseHex}${randomHex}${"0".repeat(25)}`.slice(0, 42) as `0x${string}`;
};

const shuffleArray = <T,>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

const calculateTradePrice = (makerAmt: string, takerAmt: string): number => {
  const maker = Number(makerAmt);
  const taker = Number(takerAmt);

  if (maker === 0) return 0;

  const price = taker / (maker + taker);
  return Math.round(price * 100);
};

const isBuyTrade = (makerAmt: string, takerAmt: string): boolean => {
  return Number(takerAmt) > Number(makerAmt);
};

const LoadingSkeleton: React.FC = () => (
  <div className="w-full h-16 bg-white/10 backdrop-blur-md border-y mb-4 border-white/20 relative max-w-[1450px]">
    <div className="flex gap-4 animate-pulse overflow-hidden">
      {Array.from({ length: 4 }, (_, i) => (
        <div key={i} className="flex-shrink-0 min-w-[330px]">
          <div className="flex items-center gap-3 px-4 py-2 bg-gray-200 border border-gray-200 rounded-lg h-12">
            <div className="w-10 h-10 rounded-full bg-gray-300" />
            <div className="flex-1">
              <div className="h-3 bg-gray-300 rounded mb-2 w-3/4" />
              <div className="flex gap-2">
                <div className="h-2 bg-gray-300 rounded w-16" />
                <div className="h-2 bg-gray-300 rounded w-12" />
                <div className="h-2 bg-gray-300 rounded w-8" />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

const useScrollAnimation = (
  activityData: LastTrade[],
  isPaused: boolean,
  scrollRef: React.RefObject<HTMLDivElement>,
) => {
  const animationRef = useRef<number>();

  useEffect(() => {
    // 添加延迟，确保 DOM 已渲染
    const startAnimation = () => {
      if (activityData.length === 0 || isPaused) return;

      const scrollContainer = scrollRef.current;
      if (!scrollContainer) return;

      let scrollPosition = scrollContainer.scrollLeft;
      const scrollSpeed = 0.4;
      const itemWidth = 350;
      const singleSetWidth = (activityData.length / 2) * itemWidth;
      let lastTime = 0;

      const animate = (currentTime: number) => {
        if (isPaused) return;

        if (currentTime - lastTime < 16.67) {
          animationRef.current = requestAnimationFrame(animate);
          return;
        }

        lastTime = currentTime;
        scrollPosition += scrollSpeed;

        if (scrollPosition >= singleSetWidth) {
          scrollPosition = scrollPosition % singleSetWidth;
        }

        if (scrollContainer) {
          scrollContainer.scrollLeft = scrollPosition;
        }

        animationRef.current = requestAnimationFrame(animate);
      };

      animationRef.current = requestAnimationFrame(animate);
    };

    const timer = setTimeout(startAnimation, 100);

    return () => {
      clearTimeout(timer);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [activityData, isPaused, scrollRef]);

  return () => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  };
};

const ActivityBannerItem: React.FC<{
  itemData: LastTrade;
  marketInfo: MarketInfo | null;
  timeStamp: number;
  t: any;
  current_language: string;
}> = React.memo(({ itemData, marketInfo, timeStamp, t, current_language }) => {
  const { tokenId, maker, filled_timestamp, maker_amt, taker_amt, question_market_id } = itemData;

  const computedData: ComputedTradeData = useMemo(() => {
    const baseData = {
      marketId: question_market_id.Int64,
      currentPrice: calculateTradePrice(maker_amt, taker_amt),
      timeAgo: calculateTimeToNow(filled_timestamp, true),
      makerAvatar: generateRandomAvatar(maker, filled_timestamp),
      isBuy: isBuyTrade(maker_amt, taker_amt),
    };

    if (!marketInfo) {
      return {
        ...baseData,
        questionText: `Market ${question_market_id.Int64}`,
        slugText: `market-${question_market_id.Int64}`,
        isYes: false,
        eventId: question_market_id.Int64,
        conditionId: null,
        questionId: null,
      };
    }

    return {
      ...baseData,
      questionText: base64DecodeByLanguage(marketInfo.question),
      slugText: marketInfo.event_markets?.[0]?.event?.slug
        ? `${base64DecodeByEn(marketInfo.event_markets[0].event.slug)}-${current_language}`
        : `market-${baseData.marketId}`,
      isYes: marketInfo.clob_token_ids?.[0] === tokenId,
      eventId: marketInfo.event_markets?.[0]?.event_id || baseData.marketId,
      conditionId: marketInfo.event_markets?.[0]?.condition_id || null,
      questionId: marketInfo.id,
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemData, marketInfo, maker_amt, taker_amt, tokenId, maker, filled_timestamp, current_language]);

  const href = useMemo(() => {
    const baseUrl = `/event/${computedData.slugText}`;
    const params = new URLSearchParams({
      tid: computedData.eventId.toString(),
      lng: current_language,
      timeStamp: timeStamp.toString(),
    });

    if (computedData.conditionId) {
      params.set("conditionid", computedData.conditionId);
    }
    if (computedData.questionId) {
      params.set("qid", computedData.questionId.toString());
    }

    return `${baseUrl}?${params.toString()}`;
  }, [computedData, current_language, timeStamp]);

  return (
    <Link href={href} className="text-sm text-inherit hover:underline">
      <div className="flex items-center gap-3 px-4 py-2 bg-white border border-gray-200 rounded-lg min-w-[280px] sm:min-w-[330px] shadow-sm hover:shadow-md transition-shadow">
        <div className="w-10 h-10 rounded-full flex items-center justify-center shadow-sm">
          <Image
            src={marketInfo?.icon || "/image_alt.jpg"}
            alt={`${computedData.isBuy ? "Buy" : "Sell"} Icon`}
            width={36}
            height={36}
            loading="lazy"
          />
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between gap-2 text-sm">
            <span className="text-gray-800 truncate max-w-[180px] font-semibold">{computedData.questionText}</span>
            <span className="text-gray-500 text-xs">{computedData.timeAgo}</span>
          </div>

          <div className="flex items-center gap-2 mt-1">
            <div className="flex items-center text-gray-600 text-xs gap-1">
              <BlockieAvatar address={computedData.makerAvatar} size={16} />
              {t("activityBanner.anonymous")}
            </div>

            <span className="text-xs px-2 py-0.5 rounded-full text-gray-500">
              {t("activityBanner.priceAction", {
                price: computedData.currentPrice,
                action: computedData.isBuy ? t("activityBanner.buy") : t("activityBanner.sell"),
              })}
            </span>

            <span
              className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                computedData.isYes ? "text-green-600 bg-green-50" : "text-red-600 bg-red-50"
              }`}
            >
              {computedData.isYes ? "Yes" : "No"}
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
});

ActivityBannerItem.displayName = "ActivityBannerItem";

const useActivityData = (current_language: string) => {
  const [originalData, setOriginalData] = useState<LastTrade[]>([]);
  const [marketInfoMap, setMarketInfoMap] = useState<Map<number, MarketInfo>>(new Map());
  const [isLoading, setIsLoading] = useState(true);

  const fetchMarketInfo = useCallback(async (marketIds: number[]): Promise<Map<number, MarketInfo>> => {
    const marketInfos = new Map<number, MarketInfo>();

    try {
      const response = await getInformationListDataByQuestionId(marketIds);
      if (response?.data?.question_market && Array.isArray(response.data.question_market)) {
        response.data.question_market.forEach((marketInfo: MarketInfo) => {
          if (marketInfo.id) {
            marketInfos.set(marketInfo.id, marketInfo);
          }
        });
      }
    } catch (error) {
      console.error("Error fetching market information:", error);
    }

    return marketInfos;
  }, []);

  const parseTradeData = useCallback((data: any): LastTrade[] => {
    const parseTradeString = (tradeStr: string): LastTrade | null => {
      try {
        return JSON.parse(tradeStr);
      } catch (parseError) {
        console.error("Error parsing trade data:", tradeStr, parseError);
        return null;
      }
    };

    let trades: LastTrade[] = [];

    if (data?.["last-trades"] && Array.isArray(data["last-trades"])) {
      trades = data["last-trades"].map(parseTradeString).filter((t): t is LastTrade => t !== null);
    } else if (Array.isArray(data)) {
      trades = data.map(parseTradeString).filter((t): t is LastTrade => t !== null);
    } else if (data?.data && Array.isArray(data.data)) {
      trades = data.data.map(parseTradeString).filter(Boolean);
    }

    return trades;
  }, []);

  const fetchActivityData = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await getActivityBannerList(20);
      const trades = parseTradeData(response?.data || response);

      if (trades.length === 0) {
        console.log("No trades found in response:", response);
        return;
      }

      setOriginalData(trades);

      const uniqueMarketIds = Array.from(new Set(trades.map(trade => trade.question_market_id.Int64)));
      if (uniqueMarketIds.length > 0) {
        const marketInfos = await fetchMarketInfo(uniqueMarketIds);
        setMarketInfoMap(marketInfos);
      }
    } catch (error) {
      console.error("Failed to fetch activity data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchMarketInfo, parseTradeData]);

  useEffect(() => {
    const loadData = () => {
      if ("requestIdleCallback" in window) {
        requestIdleCallback(fetchActivityData);
      } else {
        setTimeout(fetchActivityData, 0);
      }
    };

    loadData();
  }, [current_language, fetchActivityData]);

  return { originalData, marketInfoMap, isLoading };
};

const ActivityBanner: React.FC = () => {
  const { current_language } = useGlobalState().nativeCurrency;
  const { t } = useTranslation();
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [animationReady, setAnimationReady] = useState(false);
  const timeStamp = getCurrentTimestamp();

  const { originalData, marketInfoMap, isLoading } = useActivityData(current_language);

  const activityData = useMemo(() => {
    if (originalData.length === 0) return [];
    const shuffledOrders = shuffleArray([...originalData]);
    return [...shuffledOrders, ...shuffledOrders];
  }, [originalData]);

  // 当数据准备好时，启用动画
  useEffect(() => {
    if (activityData.length > 0 && !isLoading) {
      // 稍微延迟以确保 DOM 完全渲染
      const timer = setTimeout(() => {
        setAnimationReady(true);
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [activityData.length, isLoading]);

  const stopAnimation = useScrollAnimation(animationReady ? activityData : [], isPaused, scrollRef);

  const handleMouseEnter = useCallback(() => {
    setIsPaused(true);
    stopAnimation();
  }, [stopAnimation]);

  const handleMouseLeave = useCallback(() => {
    setIsPaused(false);
  }, []);

  if (isLoading || activityData.length === 0) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="w-full overflow-hidden bg-white/10 backdrop-blur-md border-y border-white/20 mb-4 relative max-w-[1450px]">
      <div
        ref={scrollRef}
        className="flex gap-4 overflow-x-hidden"
        style={{ scrollBehavior: "auto" }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {activityData.map((item, index) => {
          const marketInfo = marketInfoMap.get(item.question_market_id.Int64) || null;
          return (
            <div key={`${item.tokenId}-${item.filled_timestamp}-${index}`} className="flex-shrink-0">
              <ActivityBannerItem
                itemData={item}
                marketInfo={marketInfo}
                timeStamp={timeStamp}
                t={t}
                current_language={current_language}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ActivityBanner;
