import React, { useEffect, useRef, useState } from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

// 文本处理函数：将中划线或下划线转换为空格并首字母大写
const formatTabLabel = (text: string): string => {
  if (!text) return text;

  return text
    .replace(/[-_]/g, " ") // 将中划线和下划线替换为空格
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // 每个单词首字母大写
    .join(" ");
};

const ScrollTab = (props: any) => {
  const { tagListData, eventParams, setEventParams } = props;
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const onClickTagHandler = (tab: any) => {
    setEventParams({
      ...eventParams,
      tag_id: tab.id,
    });
  };

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -400, behavior: "smooth" });
      setTimeout(updateScrollButtons, 300); // 确保在滚动完成后更新按钮状态
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 400, behavior: "smooth" });
      setTimeout(updateScrollButtons, 300); // 确保在滚动完成后更新按钮状态
    }
  };

  const updateScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft + clientWidth < scrollWidth - 50);
    }
  };

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    updateScrollButtons();
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", updateScrollButtons);
    }
    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener("scroll", updateScrollButtons);
      }
    };
  }, []);

  return (
    <div className="relative w-full py-4">
      {tagListData?.length > 0 && (
        <div
          role="group"
          aria-label="Market tabs"
          className="flex gap-3 overflow-x-auto whitespace-nowrap no-scrollbar items-center scroll-smooth"
          tabIndex={0}
          ref={scrollContainerRef}
        >
          {canScrollLeft && (
            <div className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-gradient-to-r from-white via-white to-transparent pr-4">
              <button
                onClick={scrollLeft}
                className="flex items-center justify-center w-8 h-8 bg-white border border-gray-200 hover:border-gray-300 hover:bg-gray-50 rounded-full shadow-sm transition-all duration-200"
                aria-label="Scroll left"
              >
                <ChevronLeftIcon className="w-4 h-4 text-gray-600" />
              </button>
            </div>
          )}
          {}
          {tagListData?.map((tab: any) => (
            <button
              key={tab.id}
              type="button"
              aria-label={tab.label}
              className={`h-10 flex items-center px-4 py-2 cursor-pointer text-sm font-semibold rounded-lg transition-all duration-200 whitespace-nowrap ${
                eventParams.tag_id === tab.id
                  ? "bg-blue-600 text-white shadow-md hover:bg-blue-700 transform hover:scale-105"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900 border border-gray-200 hover:border-gray-300"
              }`}
              onClick={() => {
                onClickTagHandler(tab);
              }}
            >
              {tab.icon && <div className="mr-2 flex items-center">{tab.icon}</div>}
              <span className="select-none">{formatTabLabel(tab.label)}</span>
            </button>
          ))}

          {canScrollRight && (
            <div className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-gradient-to-l from-white via-white to-transparent pl-4">
              <button
                onClick={scrollRight}
                className="flex items-center justify-center w-8 h-8 bg-white border border-gray-200 hover:border-gray-300 hover:bg-gray-50 rounded-full shadow-sm transition-all duration-200"
                aria-label="Scroll right"
              >
                <ChevronRightIcon className="w-4 h-4 text-gray-600" />
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ScrollTab;
