"use client";

import React, { useEffect, useState } from "react";
import MysteryBoxModal from "./MysteryBoxModal";
import { useMysteryBoxList } from "@/hooks/useMysteryBoxList";
import { <PERSON><PERSON>, Card, CardBody, Chip, Spinner, Tooltip } from "@heroui/react";
import { motion } from "framer-motion";
import { Gift, Sparkles } from "lucide-react";
import { useTranslation } from "react-i18next";

interface MysteryBoxSectionProps {
  mysteryBoxData: ReturnType<typeof useMysteryBoxList>;
  isCheckingRewards?: boolean;
}

interface MysteryBoxCardProps {
  box: any;
  isReady: boolean;
  isOpening: boolean;
  isAnyBoxOpening: boolean;
  onOpen: (boxId: number) => void;
  formatCooldownTime: (seconds: number) => string;
  getRemainingCooldown: (box: any) => number;
  checkAndUpdateExpiredBoxes: () => void;
}

const MysteryBoxCard: React.FC<MysteryBoxCardProps> = ({
  box,
  isReady,
  isOpening,
  isAnyBoxOpening,
  onOpen,
  formatCooldownTime,
  getRemainingCooldown,
  checkAndUpdateExpiredBoxes,
}) => {
  const { t } = useTranslation();
  const [cooldownTime, setCooldownTime] = useState(0);

  useEffect(() => {
    if (!box.timestamp_next_ready || isReady) {
      setCooldownTime(0);
      return;
    }

    const updateTime = () => {
      const remaining = getRemainingCooldown(box);
      setCooldownTime(remaining);

      // 当倒计时结束时，触发状态检查和更新
      if (remaining <= 0 && !isReady) {
        console.log(`🔄 盲盒 ${box.id} 倒计时结束，触发状态更新`);
        checkAndUpdateExpiredBoxes();
      }
    };

    updateTime();
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, [box, isReady, getRemainingCooldown, checkAndUpdateExpiredBoxes]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.3 }}
      className={`relative p-6 rounded-xl border-2 transition-all duration-300 ${
        isReady
          ? "bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200 hover:shadow-xl hover:border-blue-300"
          : "bg-orange-50 border-orange-200 hover:shadow-lg"
      }`}
    >
      <div className="absolute top-3 right-3">
        {isReady ? (
          <Chip size="sm" color="success" variant="flat">
            {t("mysteryBox.card.ready")}
          </Chip>
        ) : (
          <Chip size="sm" color="warning" variant="flat">
            {t("mysteryBox.card.cooldown")}
          </Chip>
        )}
      </div>

      <div className="text-center mb-6">
        <motion.div
          className={`w-24 h-24 mx-auto rounded-2xl flex items-center justify-center shadow-lg ${
            isReady
              ? "bg-gradient-to-br from-blue-500 to-purple-600"
              : "bg-gradient-to-br from-orange-400 to-orange-600"
          }`}
          animate={
            isReady && !isOpening
              ? {
                  y: [0, -3, 0],
                  boxShadow: [
                    "0 10px 25px rgba(0,0,0,0.1)",
                    "0 15px 35px rgba(0,0,0,0.15)",
                    "0 10px 25px rgba(0,0,0,0.1)",
                  ],
                }
              : {}
          }
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          {isOpening ? (
            <motion.div
              animate={{ rotate: 360, scale: [1, 1.1, 1] }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <Sparkles className="w-12 h-12 text-white" />
            </motion.div>
          ) : (
            <motion.div
              key={`gift-${isReady ? "ready" : "cooldown"}`} // 添加key来强制重新渲染
              initial={{ rotate: 0 }} // 确保初始状态为0度
              animate={isReady ? { rotate: [0, -5, 5, 0] } : { rotate: 0 }}
              transition={{ duration: 3, repeat: isReady ? Infinity : 0, ease: "easeInOut" }}
            >
              <Gift className="w-12 h-12 text-white" />
            </motion.div>
          )}
        </motion.div>
      </div>

      <div className="text-center mb-4">
        <div className="font-bold text-xl text-gray-800 mb-1">{t("mysteryBox.section.mysteryBoxTitle")}</div>
      </div>

      <div className="text-center">
        {isReady ? (
          isAnyBoxOpening && !isOpening ? (
            <Tooltip content={t("mysteryBox.card.waitForOtherBox")} placement="top" className="text-sm">
              <div className="w-full">
                <Button
                  color="primary"
                  size="md"
                  isDisabled={true}
                  className="w-full text-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-2 rounded-lg opacity-50 cursor-not-allowed"
                >
                  {t("mysteryBox.card.openBox")}
                </Button>
              </div>
            </Tooltip>
          ) : (
            <Button
              color="primary"
              size="md"
              onPress={() => onOpen(box.id)}
              isLoading={isOpening}
              className="w-full text-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-2 rounded-lg"
            >
              {isOpening ? t("mysteryBox.card.opening") : t("mysteryBox.card.openBox")}
            </Button>
          )
        ) : (
          <div className="text-center">
            <div className="inline-block text-medium font-semibold text-gray-500 bg-gray-200 px-8 py-1 rounded-lg">
              {formatCooldownTime(cooldownTime)}
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

const MysteryBoxSection: React.FC<MysteryBoxSectionProps> = ({ mysteryBoxData, isCheckingRewards = false }) => {
  const { t } = useTranslation();
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [lastOpenedReward, setLastOpenedReward] = useState<number>(0);

  const {
    readyBoxes,
    cooldownBoxes,
    loading,
    opening,
    error,
    totalBoxes,
    hasBoxes,
    totalReady,
    totalCooldown,
    isAnyBoxOpening,
    initializeData,
    openBox,
    formatCooldownTime,
    getRemainingCooldown,
    checkAndUpdateExpiredBoxes,
  } = mysteryBoxData;

  // 手动刷新函数
  const handleManualRefresh = () => {
    initializeData(true); // 强制刷新
  };

  const handleOpenBox = async (boxId: number) => {
    try {
      const result = await openBox(boxId);
      if (result?.success) {
        setLastOpenedReward(result.data.reward);
        setShowRewardModal(true);
      }
    } catch (error) {
      console.error("Failed to open mystery box:", error);
    }
  };

  return (
    <section className="mb-8 pt-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-6"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">{t("mysteryBox.section.title")}</h2>
        <p className="text-xl md:text-2xl text-gray-600 mx-auto">{t("mysteryBox.section.subtitle")}</p>
      </motion.div>

      <div className="max-w-4xl mx-auto mb-6">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          <Card className="text-center">
            <CardBody className="py-4">
              <div className="text-2xl font-bold text-blue-600">{totalReady}</div>
              <div className="text-medium text-gray-600">{t("mysteryBox.section.readyCount")}</div>
            </CardBody>
          </Card>
          <Card className="text-center">
            <CardBody className="py-4">
              <div className="text-2xl font-bold text-orange-600">{totalCooldown}</div>
              <div className="text-medium text-gray-600">{t("mysteryBox.section.cooldownCount")}</div>
            </CardBody>
          </Card>
          <Card className="text-center">
            <CardBody className="py-4">
              <div className="text-2xl font-bold text-purple-600">{totalBoxes}</div>
              <div className="text-medium text-gray-600">{t("mysteryBox.section.totalCount")}</div>
            </CardBody>
          </Card>
        </div>
      </div>

      {/* 盲盒网格 */}
      <div className="max-w-6xl mx-auto">
        {loading || isCheckingRewards ? (
          <div className="text-center py-8">
            <Spinner size="lg" />
            <div className="mt-4 text-gray-600">
              {isCheckingRewards ? t("mysteryBox.section.checkingRewards") : t("mysteryBox.section.loading")}
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className="text-red-500 mb-4">{t("mysteryBox.section.loadingFailed", { error })}</div>
            <Button color="primary" onPress={handleManualRefresh}>
              {t("mysteryBox.section.retry")}
            </Button>
          </div>
        ) : !hasBoxes ? (
          <div className="text-center py-12">
            <Gift className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <div className="text-xl text-gray-600 mb-2">{t("mysteryBox.section.noBoxes")}</div>
            <div className="text-gray-500 mb-4">{t("mysteryBox.section.noBoxesDesc")}</div>
            <Button color="primary" onPress={handleManualRefresh}>
              {t("mysteryBox.section.checkRewards")}
            </Button>
          </div>
        ) : (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold">{t("mysteryBox.section.myBoxes")}</h3>
              <div className="text-sm text-gray-500">{t("mysteryBox.section.totalBoxes", { count: totalBoxes })}</div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {[...readyBoxes, ...cooldownBoxes]
                .sort((a, b) => a.id - b.id)
                .map(box => {
                  const isReady = readyBoxes.some(readyBox => readyBox.id === box.id);

                  return (
                    <MysteryBoxCard
                      key={box.id}
                      box={box}
                      isReady={isReady}
                      isOpening={opening[box.id] || false}
                      isAnyBoxOpening={isAnyBoxOpening}
                      onOpen={handleOpenBox}
                      formatCooldownTime={formatCooldownTime}
                      getRemainingCooldown={getRemainingCooldown}
                      checkAndUpdateExpiredBoxes={checkAndUpdateExpiredBoxes}
                    />
                  );
                })}
            </div>
          </div>
        )}
      </div>

      <MysteryBoxModal isOpen={showRewardModal} onClose={() => setShowRewardModal(false)} reward={lastOpenedReward} />
    </section>
  );
};

export default MysteryBoxSection;
