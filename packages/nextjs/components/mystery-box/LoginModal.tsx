"use client";

import React, { useState } from "react";
import { useMagicStore } from "@/services/store/magicStore";
import { Button, Card, CardBody, Divider, Image, Modal, ModalBody, ModalContent, ModalHeader } from "@heroui/react";
import { useConnectModal } from "@rainbow-me/rainbowkit";
import { motion } from "framer-motion";
import { Mail, Sparkles, Wallet } from "lucide-react";
import { toast } from "react-hot-toast";

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingType, setLoadingType] = useState<"google" | "wallet" | null>(null);
  const { magic, initializeMagic, isInitialized } = useMagicStore();
  const { openConnectModal } = useConnectModal();

  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true);
      setLoadingType("google");

      if (!isInitialized || !magic) {
        await initializeMagic();
      }

      if (!magic) {
        throw new Error("Magic not initialized");
      }

      // 打开 Google OAuth 登录
      await magic.oauth.loginWithRedirect({
        provider: "google",
        redirectURI: `${window.location.origin}/auth/callback`,
      });
    } catch (error) {
      console.error("Google login error:", error);
      toast.error("Google login failed. Please try again.");
      setIsLoading(false);
      setLoadingType(null);
    }
  };

  const handleWalletConnect = () => {
    try {
      setIsLoading(true);
      setLoadingType("wallet");

      if (openConnectModal) {
        openConnectModal();
      } else {
        throw new Error("Wallet connection not available");
      }
    } catch (error) {
      console.error("Wallet connect error:", error);
      toast.error("Wallet connection failed. Please try again.");
    } finally {
      setIsLoading(false);
      setLoadingType(null);
    }
  };

  const handleEmailLogin = () => {
    // 这里可以实现邮箱登录逻辑
    toast("Email login coming soon!", {
      icon: "ℹ️",
    });
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="2xl"
      classNames={{
        backdrop: "bg-gradient-to-t from-zinc-900/50 to-zinc-900/10 backdrop-opacity-20",
        base: "border-none bg-gradient-to-br from-white to-gray-50",
        header: "border-b-[1px] border-gray-200",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-[#64B5F6] to-[#42A5F5] rounded-xl flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Join MysteryUSDC</h2>
                <p className="text-sm text-gray-600">Get your first mystery box free!</p>
              </div>
            </div>
          </div>
        </ModalHeader>

        <ModalBody>
          <div className="space-y-6">
            {/* 欢迎信息 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center py-4"
            >
              <h3 className="text-xl font-bold text-gray-800 mb-2">Choose Your Sign-In Method</h3>
              <p className="text-gray-600">Sign up now and get your first mystery box with USDC rewards!</p>
            </motion.div>

            {/* Google 登录 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card className="border border-gray-200 hover:shadow-md transition-all duration-300">
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-600 rounded-xl flex items-center justify-center shadow-md">
                        <Image src="/google_logo.svg" alt="Google" width={24} height={24} className="rounded" />
                      </div>
                      <div>
                        <div className="font-bold text-gray-800">Continue with Google</div>
                        <div className="text-sm text-gray-600">Quick and secure sign-in</div>
                      </div>
                    </div>
                  </div>
                  <Button
                    className="w-full bg-gradient-to-r from-red-500 to-orange-600 text-white font-bold hover:shadow-lg transition-all duration-300"
                    size="lg"
                    onPress={handleGoogleLogin}
                    isDisabled={isLoading}
                    isLoading={loadingType === "google"}
                    startContent={!isLoading && <Image src="/google_logo.svg" alt="Google" width={20} height={20} />}
                  >
                    {loadingType === "google" ? "Connecting..." : "Sign in with Google"}
                  </Button>
                </CardBody>
              </Card>
            </motion.div>

            <Divider className="my-4" />

            {/* 钱包连接选项 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-2 gap-4"
            >
              {/* MetaMask */}
              <Card
                className="border border-gray-200 hover:shadow-md transition-all duration-300 cursor-pointer"
                isPressable
                onPress={handleWalletConnect}
                isDisabled={isLoading}
              >
                <CardBody className="p-4 text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-yellow-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-md">
                    <Image src="/metaMask_icon.svg" alt="MetaMask" width={24} height={24} />
                  </div>
                  <div className="font-bold text-gray-800 mb-1">MetaMask</div>
                  <div className="text-xs text-gray-600">Connect wallet</div>
                </CardBody>
              </Card>

              {/* 其他钱包 */}
              <Card
                className="border border-gray-200 hover:shadow-md transition-all duration-300 cursor-pointer"
                isPressable
                onPress={handleWalletConnect}
                isDisabled={isLoading}
              >
                <CardBody className="p-4 text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-md">
                    <Wallet className="w-6 h-6 text-white" />
                  </div>
                  <div className="font-bold text-gray-800 mb-1">Other Wallets</div>
                  <div className="text-xs text-gray-600">WalletConnect</div>
                </CardBody>
              </Card>
            </motion.div>

            {/* 邮箱登录选项 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card className="border border-gray-200 hover:shadow-md transition-all duration-300">
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-gray-500 to-slate-600 rounded-xl flex items-center justify-center shadow-md">
                        <Mail className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <div className="font-bold text-gray-800">Email Sign-In</div>
                        <div className="text-sm text-gray-600">Traditional email & password</div>
                      </div>
                    </div>
                  </div>
                  <Button
                    className="w-full bg-gradient-to-r from-gray-500 to-slate-600 text-white font-bold hover:shadow-lg transition-all duration-300"
                    size="lg"
                    onPress={handleEmailLogin}
                    isDisabled={true}
                    startContent={<Mail className="w-5 h-5" />}
                  >
                    Coming Soon
                  </Button>
                </CardBody>
              </Card>
            </motion.div>

            {/* 奖励提醒 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="bg-gradient-to-r from-[#64B5F6]/10 to-[#D0BFFF]/10 rounded-xl p-4 border border-[#64B5F6]/20"
            >
              <div className="flex items-center gap-3 mb-2">
                <Sparkles className="w-5 h-5 text-[#64B5F6]" />
                <span className="font-bold text-gray-800">New User Bonus</span>
              </div>
              <p className="text-sm text-gray-700">
                🎁 Get your <strong>first mystery box free</strong> when you sign up!
                <br />
                💰 Win USDC credits ranging from <strong>$0.01 to $10.00</strong>
                <br />
                🔄 New mystery boxes available every <strong>24-72 hours</strong>
              </p>
            </motion.div>

            {/* 服务条款 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="text-center"
            >
              <p className="text-xs text-gray-500">
                By signing up, you agree to our{" "}
                <a href="/terms" className="text-[#64B5F6] hover:underline">
                  Terms of Service
                </a>{" "}
                and{" "}
                <a href="/privacy" className="text-[#64B5F6] hover:underline">
                  Privacy Policy
                </a>
              </p>
            </motion.div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default LoginModal;
