"use client";

import React, { useC<PERSON>back, useEffect, useState } from "react";
import { getInvitedUsersByCode, getOrCreateInvitationCode } from "@/api/user";
import { checkIfContract } from "@/contracts/checkIfContract";
import computeProxyAddress from "@/contracts/computeProxyAddress";
import { useMysteryBoxList } from "@/hooks/useMysteryBoxList";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useMagicStore } from "@/services/store/magicStore";
import { handleCreateProxyWalletWithMagic } from "@/utils/signature/createProxy";
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Input,
  Spinner,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { motion } from "framer-motion";
import { Copy, Gift, Users } from "lucide-react";
import { useSession } from "next-auth/react";
import { useTranslation } from "react-i18next";

interface InvitedUser {
  username: string;
  proxy_wallet: string;
  invitation_code: string;
  invited_by: string;
}

const ReferralSection: React.FC = () => {
  const { t } = useTranslation();
  const { address, isConnected } = useUserAddress();
  const { magic: magicInstance } = useMagicStore();
  const { data: session } = useSession();
  const { initializeData: refreshMysteryBoxes } = useMysteryBoxList();

  const [proxyWallet, setProxyWallet] = useState<string>("");
  const [invitationCode, setInvitationCode] = useState<string>("");
  const [invitationLink, setInvitationLink] = useState<string>("");
  const [invitedUsers, setInvitedUsers] = useState<InvitedUser[]>([]);
  const [isActiveWallet, setIsActiveWallet] = useState<boolean>(false);
  const [isCreatedProxy, setIsCreatedProxy] = useState<boolean>(false); // eslint-disable-line @typescript-eslint/no-unused-vars
  const [isBtnLoading, setIsBtnLoading] = useState<boolean>(false);
  const [showTooltip, setShowTooltip] = useState<boolean>(false); // eslint-disable-line @typescript-eslint/no-unused-vars
  const [loading, setLoading] = useState<boolean>(false);

  // 计算代理钱包地址
  const initializeProxyWallet = useCallback(async () => {
    if (!address) return;

    try {
      const proxyAddress = await computeProxyAddress(address);
      setProxyWallet(proxyAddress);
    } catch (error) {
      console.error("Failed to compute proxy address:", error);
    }
  }, [address]);

  // 检查代理钱包状态
  const checkProxyWalletStatus = useCallback(async () => {
    if (!proxyWallet) {
      setIsActiveWallet(false);
      return false;
    }

    try {
      const isContract = await checkIfContract(proxyWallet);
      setIsActiveWallet(isContract);
      return isContract;
    } catch (error) {
      console.error("Error checking proxy wallet status:", error);
      setIsActiveWallet(false);
      return false;
    }
  }, [proxyWallet]);

  // 处理激活账户（仅Magic用户）
  const handleActivateAccount = useCallback(async () => {
    if (!session || !proxyWallet) return;

    try {
      setIsBtnLoading(true);
      await handleCreateProxyWalletWithMagic(
        session,
        proxyWallet,
        setIsCreatedProxy,
        setIsBtnLoading,
        setIsActiveWallet,
        setShowTooltip,
        magicInstance,
      );

      // 激活成功后重新加载用户数据和盲盒数据
      await loadUserData();

      // 刷新盲盒数据
      console.log("🎁 激活账户成功，刷新盲盒数据");
      await refreshMysteryBoxes();
    } catch (error) {
      console.error("Failed to activate account:", error);
      setIsBtnLoading(false);
    }
  }, [session, proxyWallet, magicInstance, isActiveWallet]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (isConnected && address) {
      initializeProxyWallet();
    }
  }, [isConnected, address, initializeProxyWallet]);

  useEffect(() => {
    if (proxyWallet) {
      checkProxyWalletStatus();
    }
  }, [proxyWallet, checkProxyWalletStatus]);

  useEffect(() => {
    if (proxyWallet && isActiveWallet) {
      loadUserData();
    }
  }, [proxyWallet, isActiveWallet]); // eslint-disable-line react-hooks/exhaustive-deps

  // 获取或创建邀请码
  const handleGetOrCreateInvitationCode = useCallback(async () => {
    try {
      const result = await getOrCreateInvitationCode({ proxy_wallet: proxyWallet });

      if (result.success) {
        const invitationCode = result.data.invitation_code;
        setInvitationCode(invitationCode);
        return invitationCode;
      } else {
        throw new Error(result.message || "获取邀请码失败");
      }
    } catch (error) {
      console.error("获取/创建邀请码失败:", error);
      return null;
    }
  }, [proxyWallet]);

  // 加载用户数据和邀请数据
  const loadUserData = useCallback(async () => {
    if (!proxyWallet || !isActiveWallet) return;

    try {
      setLoading(true);
      // 获取或创建邀请码
      const invitationCode = await handleGetOrCreateInvitationCode();

      if (invitationCode) {
        // 获取被邀请的用户列表
        try {
          await loadInvitedUsers(invitationCode);
        } catch (error) {
          console.error("加载邀请用户列表失败:", error);
          // 继续执行，不阻断流程
        }
      }
    } catch (error) {
      console.error("加载邀请数据失败:", error);

      // 检查是否是用户不存在的错误
      if (error instanceof Error && error.message.includes("指定的proxy_wallet对应的用户不存在")) {
        console.log("用户还未激活账户，需要先激活");
        // 这种情况下不需要做任何处理，UI 会显示激活账户按钮
      }
    } finally {
      setLoading(false);
    }
  }, [proxyWallet, isActiveWallet, handleGetOrCreateInvitationCode]);

  // 加载被邀请的用户列表
  const loadInvitedUsers = async (invitationCode: string) => {
    try {
      const invitedResponse = await getInvitedUsersByCode(invitationCode);
      const invitedUsers = invitedResponse?.data?.web_users || [];

      setInvitedUsers(invitedUsers);
    } catch (error) {
      console.error("加载被邀请用户失败:", error);
      // 设置空数据
      setInvitedUsers([]);
    }
  };

  // 生成邀请链接
  useEffect(() => {
    if (invitationCode) {
      const link = `${window.location.origin}?invitationCode=${invitationCode}`;
      setInvitationLink(link);
    }
  }, [invitationCode]);

  // 复制邀请链接
  const copyInvitationLink = async () => {
    try {
      await navigator.clipboard.writeText(invitationLink);
      console.log("邀请链接已复制到剪贴板");
    } catch (error) {
      console.error("复制失败:", error);
    }
  };

  // 盲盒功能仅限Magic用户使用（在页面级别已经过滤）

  return (
    <section className="mb-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-8"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">{t("mysteryBox.referral.title")}</h2>
        <p className="text-lg text-gray-600">{t("mysteryBox.referral.startInviting")}</p>
      </motion.div>

      <div className="grid lg:grid-cols-2 gap-8 items-start">
        {/* 左侧：邀请链接和统计区域 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="space-y-6"
        >
          {/* 邀请码卡片 */}
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50">
            <CardBody className="p-6">
              <div className="text-center mb-2">
                <div className="w-12 h-12 bg-gradient-to-br from-[#81C784] to-[#66BB6A] rounded-xl flex items-center justify-center mx-auto mb-3">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-1">{t("mysteryBox.referral.inviteCode")}</h3>
                <div className="text-lg text-gray-600 my-2">{t("mysteryBox.referral.startInviting")}</div>
              </div>

              {/* 邀请码显示或激活账户 */}
              <div className="mb-2">
                {!isActiveWallet ? (
                  // 显示激活账户按钮
                  <div className="text-center py-6">
                    <div className="text-lg text-gray-600 mb-4">{t("mysteryBox.referral.noInvites")}</div>
                    <Button
                      color="primary"
                      size="lg"
                      onPress={handleActivateAccount}
                      isLoading={isBtnLoading}
                      className="bg-gradient-to-r from-[#81C784] to-[#66BB6A] text-white font-semibold"
                    >
                      {isBtnLoading ? t("mysteryBox.referral.activating") : t("mysteryBox.referral.activateAccount")}
                    </Button>
                  </div>
                ) : loading ? (
                  // 加载状态
                  <div className="bg-gradient-to-r from-[#81C784] to-[#66BB6A] text-white text-center py-3 px-4 rounded-xl mb-4">
                    <div className="text-lg opacity-90 mb-1">{t("mysteryBox.referral.inviteCode")}</div>
                    <div className="text-2xl font-bold tracking-wider">
                      <Spinner size="sm" color="white" />
                    </div>
                  </div>
                ) : (
                  // 显示邀请码
                  <div className="bg-gradient-to-r from-[#81C784] to-[#66BB6A] text-white text-center py-3 px-4 rounded-xl mb-4">
                    <div className="text-lg opacity-90 mb-1">{t("mysteryBox.referral.inviteCode")}</div>
                    <div className="text-2xl font-bold tracking-wider">
                      {invitationCode || t("mysteryBox.section.loading")}
                    </div>
                  </div>
                )}
              </div>

              {/* 邀请链接 */}
              {isActiveWallet && invitationLink && (
                <div className="mb-2">
                  <label className="block text-md font-medium text-gray-700 mb-2">
                    {t("mysteryBox.referral.inviteLink")}
                  </label>
                  <div className="flex gap-2">
                    <Input value={invitationLink} readOnly className="flex-1" size="md" />
                    <Button
                      isIconOnly
                      size="md"
                      className="bg-[#64B5F6] text-white hover:bg-[#42A5F5]"
                      onPress={copyInvitationLink}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </CardBody>
          </Card>
          {/* 详细统计区域 */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-4"
          >
            {/* 奖励说明 - 紧凑版 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="relative p-4 bg-gradient-to-r from-amber-50 via-orange-50 to-yellow-50 rounded-xl border border-amber-200/50 shadow-md"
            >
              <div className="flex items-center gap-2 mb-2">
                <div className="w-8 h-8 bg-gradient-to-br from-amber-500 to-orange-500 rounded-md flex items-center justify-center">
                  <Gift className="w-6 h-6 text-white" />
                </div>
                <span className="text-lg font-bold text-amber-800">{t("mysteryBox.referral.rewardTitle")}</span>
              </div>
              <div className="text-md text-amber-700 leading-relaxed">
                {t("mysteryBox.referral.rewardDescription", { count: 1 })}
              </div>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* 右侧：邀请记录列表 - 等高设计 */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="h-full"
        >
          <Card className="border-0 shadow-xl bg-gradient-to-br from-slate-50 to-gray-50 h-full flex flex-col">
            <CardHeader className="pb-4 bg-gradient-to-r from-slate-100 to-gray-100 rounded-t-xl flex-shrink-0">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-slate-500 to-gray-600 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-800">{t("mysteryBox.referral.invitedUsers")}</h2>
              </div>
            </CardHeader>
            <CardBody className="p-0 flex-1 flex flex-col min-h-0">
              {invitedUsers.length > 0 ? (
                <div className="flex-1 overflow-auto">
                  <Table aria-label="Referral table" className="min-w-full">
                    <TableHeader className="sticky top-0 z-10">
                      <TableColumn className="bg-gray-50 text-gray-700 font-semibold">
                        {t("mysteryBox.referral.username")}
                      </TableColumn>
                      <TableColumn className="bg-gray-50 text-gray-700 font-semibold">
                        {t("mysteryBox.referral.wallet")}
                      </TableColumn>
                    </TableHeader>
                    <TableBody>
                      {invitedUsers.map((user, index) => (
                        <TableRow key={user.proxy_wallet || index} className="hover:bg-gray-50/50 transition-colors">
                          <TableCell className="py-3 px-4">
                            <div className="font-medium text-sm">
                              {user.username || t("mysteryBox.referral.notSet")}
                            </div>
                          </TableCell>
                          <TableCell className="py-3 px-4">
                            <div className="font-medium font-mono text-sm bg-gray-100 px-3 py-2 rounded-lg inline-block">
                              {user.proxy_wallet
                                ? `${user.proxy_wallet.slice(0, 6)}...${user.proxy_wallet.slice(-4)}`
                                : "N/A"}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="flex-1 flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-gray-400" />
                    </div>
                    <p className="text-gray-500 text-lg font-medium mb-2">{t("mysteryBox.referral.noInvites")}</p>
                    <p className="text-gray-400">{t("mysteryBox.referral.startInviting")}</p>
                  </div>
                </div>
              )}
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* 邀请规则说明 - 美化版 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.7 }}
        className="mt-12"
      >
        <Card className="border-0 shadow-xl bg-gradient-to-br from-indigo-50 via-blue-50 to-cyan-50">
          <CardBody className="p-10">
            <div className="text-center mb-10">
              <h3 className="text-2xl font-bold text-gray-800 mb-3">{t("mysteryBox.referral.processTitle")}</h3>
              <p className="text-gray-600 text-lg">{t("mysteryBox.referral.processSubtitle")}</p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <span className="text-white font-bold text-xl">1</span>
                </div>
                <h4 className="font-bold text-gray-800 mb-3 text-lg">{t("mysteryBox.referral.step1Title")}</h4>
                <p className="text-gray-600 leading-relaxed">{t("mysteryBox.referral.step1Description")}</p>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.9 }}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <span className="text-white font-bold text-xl">2</span>
                </div>
                <h4 className="font-bold text-gray-800 mb-3 text-lg">{t("mysteryBox.referral.step2Title")}</h4>
                <p className="text-gray-600 leading-relaxed">{t("mysteryBox.referral.step2Description")}</p>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.0 }}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <span className="text-white font-bold text-xl">3</span>
                </div>
                <h4 className="font-bold text-gray-800 mb-3 text-lg">{t("mysteryBox.referral.step3Title")}</h4>
                <p className="text-gray-600 leading-relaxed">{t("mysteryBox.referral.step3Description")}</p>
              </motion.div>
            </div>
          </CardBody>
        </Card>
      </motion.div>
    </section>
  );
};

export default ReferralSection;
