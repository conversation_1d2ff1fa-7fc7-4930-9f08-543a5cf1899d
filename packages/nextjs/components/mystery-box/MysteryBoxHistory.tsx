"use client";

import React, { useEffect, useState } from "react";
import { MysteryBoxHistory as HistoryItem } from "@/api/mystery/mystery-box";
import { useMysteryBoxList } from "@/hooks/useMysteryBoxList";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Chip,
  Spinner,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { motion } from "framer-motion";
import { ExternalLink, History, RefreshCw } from "lucide-react";
import { useTranslation } from "react-i18next";

interface MysteryBoxHistoryProps {
  userAddress?: string;
  mysteryBoxData?: ReturnType<typeof useMysteryBoxList>;
}

const MysteryBoxHistory: React.FC<MysteryBoxHistoryProps> = ({ userAddress, mysteryBoxData }) => {
  const { t } = useTranslation();
  // 优先使用传入的 mysteryBoxData，否则使用自己的 hook 实例
  const fallbackData = useMysteryBoxList();
  const { history, historyLoading, getHistory } = mysteryBoxData || fallbackData;
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    if (userAddress) {
      getHistory(1, 20); // 获取最近20条记录
    }
  }, [userAddress, getHistory]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await getHistory(1, 20);
    } finally {
      setIsRefreshing(false);
    }
  };

  const formatDate = (dateString: string) => {
    // 根据当前语言设置格式化日期
    const locale = t("mysteryBox.history.locale", { defaultValue: "en-US" });
    return new Date(dateString).toLocaleString(locale, {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatTxHash = (txHash: string) => {
    if (!txHash) return "N/A";
    return `${txHash.slice(0, 6)}...${txHash.slice(-4)}`;
  };

  const getExplorerUrl = (txHash: string) => {
    // 根据网络返回对应的区块浏览器链接
    const baseUrl = process.env.NEXT_PUBLIC_EXPLORER_URL;
    return `${baseUrl}${txHash}`;
  };

  if (!userAddress) {
    return (
      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardBody className="p-8 text-center">
          <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">{t("mysteryBox.history.connectWallet")}</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader className="flex justify-between items-center pb-4">
          <div className="flex items-center gap-2">
            <History className="size-6 text-[#64B5F6]" />
            <div className="text-xl font-bold text-gray-800">{t("mysteryBox.history.title")}</div>
          </div>
          <Button
            size="sm"
            variant="light"
            isIconOnly
            onPress={handleRefresh}
            isLoading={isRefreshing}
            className="text-gray-500 hover:text-[#64B5F6]"
          >
            <RefreshCw className="size-5" />
          </Button>
        </CardHeader>
        <CardBody className="pt-0">
          {historyLoading ? (
            <div className="flex justify-center py-8">
              <Spinner size="lg" color="primary" />
            </div>
          ) : history.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <History className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-gray-600 mb-2">{t("mysteryBox.history.noRecords")}</p>
              <p className="text-base text-gray-500">{t("mysteryBox.history.firstBox")}</p>
            </div>
          ) : (
            <Table aria-label="Mystery box history" className="min-h-[200px]">
              <TableHeader>
                <TableColumn className="text-base font-medium">{t("mysteryBox.history.time")}</TableColumn>
                <TableColumn className="text-base font-medium">{t("mysteryBox.history.reward")}</TableColumn>
                <TableColumn className="text-base font-medium">{t("mysteryBox.history.txHash")}</TableColumn>
                <TableColumn className="text-base font-medium">{t("mysteryBox.history.status")}</TableColumn>
              </TableHeader>
              <TableBody>
                {history.map((item: HistoryItem) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="text-base">{formatDate(item.timestamp)}</div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-green-600 text-base">${item.amount.toFixed(2)}</span>
                        <span className="text-base text-gray-500">USDC</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {item.transaction_hash ? (
                        <div className="flex items-center gap-2">
                          <code className="text-base bg-gray-100 px-2 py-1 rounded">
                            {formatTxHash(item.transaction_hash)}
                          </code>
                          <Button
                            size="sm"
                            variant="light"
                            isIconOnly
                            className="min-w-unit-6 w-6 h-6"
                            onPress={() => window.open(getExplorerUrl(item.transaction_hash), "_blank")}
                          >
                            <ExternalLink className="w-3 h-3" />
                          </Button>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-base">{t("mysteryBox.history.processing")}</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip size="sm" color={item.transaction_hash ? "success" : "warning"} variant="flat">
                        {item.transaction_hash ? t("mysteryBox.history.completed") : t("mysteryBox.history.processing")}
                      </Chip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {/* 统计信息 */}
          {history.length > 0 && (
            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-2 md:grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-[#64B5F6]">{history.length}</div>
                  <div className="text-base text-gray-600">{t("mysteryBox.history.totalOpened")}</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    ${history.reduce((sum: number, item: HistoryItem) => sum + item.amount, 0).toFixed(2)}
                  </div>
                  <div className="text-base text-gray-600">{t("mysteryBox.history.totalRewards")}</div>
                </div>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default MysteryBoxHistory;
