"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@heroui/react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";

interface MysteryBoxModalProps {
  isOpen: boolean;
  onClose: () => void;
  reward: number;
}

const MysteryBoxModal: React.FC<MysteryBoxModalProps> = ({ isOpen, onClose, reward }) => {
  const { t } = useTranslation();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      classNames={{
        backdrop: "bg-gradient-to-t from-zinc-900/50 to-zinc-900/10 backdrop-blur-sm",
        base: "border-none bg-gradient-to-br from-white to-gray-50 shadow-2xl",
        header: "border-b border-gray-200",
        body: "py-8",
        closeButton: "hover:bg-gray-100 transition-colors",
      }}
    >
      <ModalContent>
        <ModalHeader className="text-center pb-1">
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{
              type: "spring",
              stiffness: 200,
              damping: 15,
              delay: 0.1,
            }}
            className="text-3xl mr-2 mb-1"
          >
            🎉
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
          >
            {t("mysteryBox.modal.congratulations")}
          </motion.div>
        </ModalHeader>
        <ModalBody className="text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{
              delay: 0.4,
              type: "spring",
              stiffness: 150,
              damping: 10,
            }}
            className="space-y-6"
          >
            {/* 奖励金额 */}
            <div className="relative">
              <motion.div
                animate={{
                  boxShadow: [
                    "0 0 0 0 rgba(34, 197, 94, 0.4)",
                    "0 0 0 20px rgba(34, 197, 94, 0)",
                    "0 0 0 0 rgba(34, 197, 94, 0)",
                  ],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="inline-block rounded-full bg-gradient-to-br from-green-400 to-emerald-600 p-6 shadow-lg"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.6, type: "spring", stiffness: 200 }}
                  className="text-5xl md:text-6xl font-black text-white"
                >
                  {reward}
                </motion.div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="mt-2 text-lg font-semibold text-gray-600"
              >
                USDC
              </motion.div>
            </div>

            {/* 成功信息 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.0 }}
              className="space-y-3"
            >
              <div className="flex items-center justify-center space-x-2 text-lg font-medium text-green-600">
                <motion.div animate={{ rotate: [0, 360] }} transition={{ duration: 2, ease: "easeInOut" }}>
                  ✅
                </motion.div>
                <span>{t("mysteryBox.modal.rewardSent")}</span>
              </div>

              <div className="text-gray-500 max-w-sm mx-auto">{t("mysteryBox.modal.thankYou")}</div>
            </motion.div>

            {/* 装饰性元素 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2 }}
              className="flex justify-center space-x-1 text-2xl"
            >
              {["🎊", "💰", "🎉", "💎", "⭐"].map((emoji, index) => (
                <motion.span
                  key={index}
                  animate={{
                    y: [0, -10, 0],
                    rotate: [0, 10, -10, 0],
                  }}
                  transition={{
                    delay: 1.3 + index * 0.1,
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                >
                  {emoji}
                </motion.span>
              ))}
            </motion.div>
          </motion.div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default MysteryBoxModal;
