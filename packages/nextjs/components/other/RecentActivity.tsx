import React, { useEffect, useState } from "react";
import Link from "next/link";
import RecentActivityItem from "./RecentActivityItem";
import { recentActivityMockList } from "./mock";
import { RecentActivityItemType } from "./types";
import Autoplay from "embla-carousel-autoplay";
import useEmblaCarousel from "embla-carousel-react";
import { useTranslation } from "react-i18next";

const RecentActivity: React.FC = () => {
  const [recentActivityList, setRecentActivityList] = useState<RecentActivityItemType[]>([]);
  const [emblaRef, emblaApi] = useEmblaCarousel({ axis: "y" }, [Autoplay({ delay: 2000 })]);
  const { t } = useTranslation();

  useEffect(() => {
    // getRecentActivityList().then(res => {
    //   setRecentActivityList(res.data);
    // });
    setRecentActivityList(recentActivityMockList);
  }, []);

  useEffect(() => {
    if (emblaApi) {
      emblaApi.on("select", () => {
        if (emblaApi.canScrollNext() === false) {
          // getRecentActivityList().then(res => {
          //   setRecentActivityList(prevList => {
          //     const newList = res.data;
          //     const combinedList = [...prevList, ...newList];
          //     return combinedList.slice(-15);
          //   });
          //   emblaApi.scrollNext();
          // });
          setRecentActivityList(prevList => {
            const newList = recentActivityMockList;
            const combinedList = [...prevList, ...newList];
            return combinedList.slice(-15);
          });
          emblaApi.scrollNext();
        }
      });
    }
  }, [emblaApi]);

  return (
    <div className="flex-1 mr-0 md:mr-5 max-h-[410px] overflow-hidden">
      <div className="h-9 flex justify-between items-center">
        <p className="text-xl font-bold">{t("Home_Recent_Activity")}</p>
        <Link href="/activity">
          <div className="text-md font-semibold border-2 py-1 px-3 rounded-lg hover:bg-gray-200">
            {t("Normal_See_all")}
          </div>
        </Link>
      </div>

      <div className="overflow-hidden h-full">
        <div className="h-[484px] md:h-[410px] rotate-180" ref={emblaRef}>
          <div className="flex flex-col h-full">
            {recentActivityList.map((item, index) => (
              <div className="flex-[0_0_20%] rotate-180" key={`${item.transactionHash}-${index}`}>
                <RecentActivityItem item={item} />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecentActivity;
