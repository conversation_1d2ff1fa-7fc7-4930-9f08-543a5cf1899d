import React, { useEffect, useState } from "react";
import { Card } from "@heroui/react";
import { AlertTriangle, Circle<PERSON>heck, X } from "lucide-react";

interface NotificationCardProps {
  title: string; // {side} {outcome} place || Orders cancelled
  content?: string; // question
  footer?: string; // {shares} shares @ {price}¢ || 2 orders were cancelled
  setShowNotification: any;
  notiStatus?: "success" | "failure";
}

const NotificationCard: React.FC<NotificationCardProps> = ({
  title,
  content,
  footer,
  setShowNotification,
  notiStatus,
}) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false);
      setShowNotification(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, [setShowNotification]);

  if (!visible) return null;
  const onClickHandler = () => {
    setVisible(false);
    setShowNotification(false);
  };

  return (
    <div className="fixed bottom-5 left-5 z-50">
      <Card shadow="lg" radius="sm" className="w-[320px] p-4 relative bg-white gap-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center font-medium text-lg">
            {notiStatus ? (
              notiStatus === "success" ? (
                <CircleCheck color="#43A047" strokeWidth={2} className="mr-2" />
              ) : (
                <AlertTriangle color="#E64800" strokeWidth={2} className="mr-2" />
              )
            ) : (
              <CircleCheck color="#43A047" strokeWidth={2} className="mr-2" />
            )}
            {title}
          </div>
          <X color="black" strokeWidth={2.5} className="size-4 cursor-pointer" onClick={onClickHandler} />
        </div>
        {content && <div>{content}</div>}
        {footer && <div className="flex items-center text-gray-600 text-sm">{footer}</div>}
      </Card>
    </div>
  );
};

export default NotificationCard;
