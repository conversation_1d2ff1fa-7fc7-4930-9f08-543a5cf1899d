import React, { useEffect } from "react";
import { EmblaOptionsType } from "embla-carousel";
import Autoplay from "embla-carousel-autoplay";
import useEmblaCarousel from "embla-carousel-react";

type PropType = {
  slides: React.ReactNode[];
  options?: EmblaOptionsType;
};

const EmblaCarousel: React.FC<PropType> = ({ slides, options = { loop: true, axis: "y" } }) => {
  const [emblaRef, emblaApi] = useEmblaCarousel(options, [Autoplay({ playOnInit: true, delay: 2000 })]);

  useEffect(() => {
    if (emblaApi) {
      emblaApi.reInit();
    }
  }, [emblaApi, slides]);

  return (
    <div className="embla h-96 overflow-hidden">
      <div className="embla__viewport h-full" ref={emblaRef}>
        <div className="embla__container flex flex-col h-full">
          {slides.map((slide, index) => (
            <div className="embla__slide flex-[0_0_100%] flex justify-center items-center" key={index}>
              {slide}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EmblaCarousel;
