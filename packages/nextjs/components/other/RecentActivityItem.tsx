import React from "react";
import Image from "next/image";
import { RecentActivityItemProps } from "./types";
import { calculateTimeToNow } from "@/utils";

const RecentActivityItem: React.FC<RecentActivityItemProps> = ({ item }) => {
  const isoString = item.timestamp ? new Date(item.timestamp * 1000).toISOString() : "";

  return (
    <div
      key={item.transactionHash}
      className="flex flex-col md:flex-row items-center justify-between py-3 px-1 hover:bg-slate-200 rounded-lg"
    >
      <div className="flex justify-start items-center w-full md:w-auto">
        <div className="w-12 h-12 mr-3">
          <Image className="size-12 rounded-md object-cover" src={item.icon} alt="" width={48} height={48} />
        </div>
        <div className="flex flex-col max-w-full">
          <div className="block max-w-md text-sm font-normal text-gray-500 truncate hover:underline">{item.title}</div>
          <div className="flex flex-wrap items-center space-x-1 text-sm">
            <div className="font-semibold max-w-sm truncate hover:underline">{item.name}</div>
            <span>{item.side === "BUY" ? "bought" : "sold"}</span>
            <span className={`font-semibold ${item.outcome === "Yes" ? "text-green-500" : "text-red-500"}`}>
              {item.outcome}
            </span>
            <span>at</span>
            <span className="font-sans">{(item.price * 100).toFixed(2) + "¢"}</span>
            <span className="text-gray-500">{`($ ${(item.size * item.price).toFixed(2)})`}</span>
          </div>
        </div>
      </div>

      <div className="flex text-sm text-gray-500 flex-shrink-0 mt-2 md:mt-0">
        {isoString && `${calculateTimeToNow(isoString)}`}
      </div>
    </div>
  );
};

export default RecentActivityItem;
