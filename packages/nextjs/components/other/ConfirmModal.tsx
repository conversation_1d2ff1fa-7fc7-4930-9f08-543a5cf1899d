import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from "@heroui/react";
import { useTranslation } from "react-i18next";

interface ConfirmModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({ visible, onClose, onConfirm }) => {
  const { t } = useTranslation();

  return (
    <Modal isOpen={visible} onClose={onClose}>
      <ModalContent>
        <ModalHeader>
          <h3>{t("OpenOrder_Confirm_Delete_Title")}</h3>
        </ModalHeader>
        <ModalBody>
          <p>{t("OpenOrder_Confirm_Delete_Content")}</p>
        </ModalBody>
        <ModalFooter>
          <Button onPress={onClose}>{t("OpenOrder_Confirm_Delete_Cancel")}</Button>
          <Button color="danger" onPress={onConfirm}>
            {t("OpenOrder_Confirm_Delete_Confirm")}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ConfirmModal;
