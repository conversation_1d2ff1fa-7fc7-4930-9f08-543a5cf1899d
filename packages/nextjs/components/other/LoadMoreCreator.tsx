"use client";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getInitEventsListData } from "@/api/markets";
import { filterCreatorEventsByTag, filterEventsByLanguage } from "@/utils";
import { useGlobalState } from "~~/services/store/store";

interface QueryParams {
  limit: number;
  offset: number;
  tag_id: string;
}

const useInfiniteScrollCreator = (initialParams: QueryParams, fetchData: (params: QueryParams) => Promise<any>) => {
  const [data, setData] = useState<any[]>([]);
  const [queryParams, setQueryParams] = useState<QueryParams>(initialParams);
  const [hasMore, setHasMore] = useState(true);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const [initialLoad, setInitialLoad] = useState(true);
  const { current_language } = useGlobalState().nativeCurrency;

  const observer = useMemo(() => {
    if (typeof window === "undefined") return null;

    return new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !initialLoad) {
          setQueryParams(prevParams => ({
            ...prevParams,
            offset: prevParams.offset + prevParams.limit,
          }));
        }
      },
      { threshold: 0.1 },
    );
  }, [hasMore, initialLoad]);

  useEffect(() => {
    if (!observer || !loadMoreRef.current) return;

    observer.observe(loadMoreRef.current);
    return () => observer.disconnect();
  }, [observer]);

  useEffect(() => {
    const fetchDataAsync = async () => {
      try {
        let response = [];

        // 始终获取所有事件，然后进行双重过滤
        const res = await getInitEventsListData({ ...queryParams });
        const filteredByLanguage = filterEventsByLanguage(current_language, res.data.events);

        // 使用新的过滤函数：既要包含creator标签，又要符合选中的标签筛选
        const filteredCreatorEvents = filterCreatorEventsByTag(filteredByLanguage, queryParams.tag_id);
        response = filteredCreatorEvents;
        if (queryParams.offset === 0) {
          setData(response);
        } else {
          setData(prevData => [...prevData, ...response]);
        }
        if (response.length < queryParams.limit) {
          setHasMore(false);
        }
        setInitialLoad(false);
      } catch (error) {
        console.error("Error fetching creator data:", error);
      }
    };

    if (queryParams.tag_id) {
      fetchDataAsync();
    }
  }, [queryParams, fetchData, current_language]);

  const setQueryParamsCallback = useCallback((newParams: QueryParams) => {
    setQueryParams(newParams);
  }, []);

  const setHasMoreCallback = useCallback((value: boolean) => {
    setHasMore(value);
  }, []);

  return {
    data,
    hasMore,
    loadMoreRef,
    setQueryParams: setQueryParamsCallback,
    setHasMore: setHasMoreCallback,
  };
};

export default useInfiniteScrollCreator;
