"use client";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getAllHomeEvents, getHomeEventsByTag } from "@/api/events";
import { filterEventsByLanguage, filterOnlyCreatorEvents } from "@/utils";
import { useGlobalState } from "~~/services/store/store";

// 过滤只包含指定标签的事件
const filterEventsByAvailableTags = (events: any[], availableTagIds: string[]): any[] => {
  if (!availableTagIds || availableTagIds.length === 0) {
    return events;
  }

  return events.filter((event: any) => {
    if (!event?.event_tags || !Array.isArray(event.event_tags)) {
      return false;
    }

    // 检查事件是否包含任何一个可用的标签
    return event.event_tags.some((eventTag: any) => {
      const tagId = eventTag?.tag?.id?.toString() || eventTag?.tag_id?.toString();
      return tagId && availableTagIds.includes(tagId);
    });
  });
};

interface QueryParams {
  limit: number;
  offset: number;
  tag_id: string | number | null;
}

const useInfiniteScrollCreator = (initialParams: any, availableTagIds?: string[]) => {
  const [data, setData] = useState<any[]>([]);
  const [queryParams, setQueryParams] = useState<QueryParams>(initialParams);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const [initialLoad, setInitialLoad] = useState(true);
  const { current_language } = useGlobalState().nativeCurrency;

  const loadMore = useCallback(() => {
    if (isLoading) {
      return;
    }
    setQueryParams(prevParams => ({
      ...prevParams,
      offset: prevParams.offset + prevParams.limit,
    }));
  }, [isLoading]);

  const observer = useMemo(() => {
    if (typeof window === "undefined") return null;
    return new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !initialLoad) {
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: "20px",
        threshold: 1.0,
      },
    );
  }, [hasMore, loadMore, initialLoad]);

  // 监听语言变化，重置数据
  useEffect(() => {
    setData([]);
    setHasMore(true);
    setInitialLoad(true);
    // 重置查询参数的offset
    setQueryParams(prev => ({
      ...prev,
      offset: 0,
    }));
  }, [current_language]);

  useEffect(() => {
    const fetchDataAsync = async () => {
      if (isLoading) {
        return;
      }

      setIsLoading(true);
      try {
        // 根据tag_id选择不同的API
        const apiParams = {
          limit: queryParams.limit,
          offset: queryParams.offset,
          active: true,
          closed: false,
        };

        let res;
        if (queryParams.tag_id === "all" || !queryParams.tag_id) {
          res = await getAllHomeEvents(apiParams);
        } else {
          res = await getHomeEventsByTag({
            ...apiParams,
            tag_id: Number(queryParams.tag_id),
          });
        }
        const allEvents = res.data.events || [];

        // 前端过滤：先按语言过滤，再只保留包含creator的事件
        const filteredByLanguage = filterEventsByLanguage(current_language, allEvents);
        let filteredCreatorEvents = filterOnlyCreatorEvents(filteredByLanguage);

        // 如果是"all"模式，需要根据可用标签列表过滤事件
        if (queryParams.tag_id === "all" && availableTagIds !== undefined) {
          if (availableTagIds.length > 0) {
            // 有可用标签时，只显示包含这些标签的事件
            filteredCreatorEvents = filterEventsByAvailableTags(filteredCreatorEvents, availableTagIds);
          } else {
            // 没有可用标签时，不显示任何事件
            filteredCreatorEvents = [];
          }
        }

        if (queryParams.offset === 0) {
          setData(filteredCreatorEvents);
          setHasMore(true); // 重置hasMore状态
        } else {
          setData(prevData => [...prevData, ...filteredCreatorEvents]);
        }

        // 基于原始API数据判断是否还有更多，而不是过滤后的数据
        if (allEvents.length < queryParams.limit) {
          setHasMore(false);
        }
        setInitialLoad(false);
      } catch (error) {
        console.error("Error fetching creator events:", error);
        setHasMore(false);
      } finally {
        setIsLoading(false);
      }
    };

    // 确保有语言信息时才请求数据
    if (current_language) {
      fetchDataAsync();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams, current_language, availableTagIds]); // 添加availableTagIds依赖

  useEffect(() => {
    if (!observer || !loadMoreRef.current) return;

    observer.observe(loadMoreRef.current);
    return () => observer.disconnect();
  }, [observer]);

  return {
    data,
    hasMore,
    isLoading,
    loadMoreRef,
    setQueryParams,
    setHasMore,
  };
};

export default useInfiniteScrollCreator;
