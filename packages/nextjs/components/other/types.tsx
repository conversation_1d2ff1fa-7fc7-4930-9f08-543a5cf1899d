export interface RecentActivityItemProps {
  item: {
    transactionHash: string;
    icon: string;
    title: string;
    pseudonym?: string;
    name: string;
    side: string;
    outcome: string;
    price: number;
    size: number;
    timestamp?: number;
  };
}

export interface RecentActivityItemType {
  proxyWallet: string;
  side: string;
  asset: string;
  conditionId: string;
  size: number;
  price: number;
  timestamp: number;
  title: string;
  slug: string;
  icon: string;
  eventSlug: string;
  outcome: string;
  outcomeIndex: number;
  name: string;
  pseudonym: string;
  bio: string;
  profileImage: string;
  profileImageOptimized: string;
  transactionHash: string;
}
