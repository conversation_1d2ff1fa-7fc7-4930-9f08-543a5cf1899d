import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { topVolumeMockList } from "./mock";
// import { getTopVolumeThisWeekList } from "@/api/volume";
import { formatAmount } from "@/utils";
import { useTranslation } from "react-i18next";
import { BlockieAvatar } from "~~/components/scaffold-eth";

interface VolumeItem {
  proxyWallet: string;
  pseudonym: string;
  amount: number;
  name: string;
  bio: string;
  profileImage: string;
  profileImageOptimized: string;
}

interface UserItemProps {
  item: VolumeItem;
  indexNum: number;
}

const UserItem: React.FC<UserItemProps> = props => {
  const { item, indexNum } = props;
  return (
    <div key={item.name} className="flex items-center space-x-2 py-3 px-2 hover:bg-slate-200 rounded-lg">
      <div className="relative">
        {item.profileImage ? (
          <Image
            alt=""
            loading="lazy"
            width="48"
            height="48"
            className="w-12 h-12 object-cover rounded-full"
            src={item.profileImage}
          />
        ) : (
          <BlockieAvatar size={48} address={item.proxyWallet} />
        )}
        <div className="absolute flex-center size-5 top-0 left-8 bg-gray-900 text-white text-xs rounded-full border border-white">
          {indexNum}
        </div>
      </div>

      <div className="flex flex-col items-start justify-center">
        <a href={item.name} className="block text-sm font-semibold text-gray-700">
          {item.name}
        </a>
        {item.amount > 0 && <div className="text-sm text-gray-500">{"$" + formatAmount(item.amount)}</div>}
      </div>
    </div>
  );
};

const TopVolume = () => {
  const { t } = useTranslation();
  const [topVolumeList, setTopVolumeList] = useState<VolumeItem[]>([]);

  useEffect(() => {
    // getTopVolumeThisWeekList().then(res => {
    //   setTopVolumeList(res.data);
    // });
    setTopVolumeList(topVolumeMockList);
  }, []);
  const leftColumnData = topVolumeList.slice(0, 5);
  const rightColumnData = topVolumeList.slice(5, 10);

  return (
    <div className="flex-1">
      <div className="h-9 flex justify-between items-center">
        <p className="text-xl font-bold"> {t("Home_Top_Volume_This_Week")}</p>

        <Link href="/leaderboard">
          <div className="text-md font-semibold border-2 py-1 px-3 rounded-lg hover:bg-gray-200">
            {t("Normal_See_all")}
          </div>
        </Link>
      </div>

      <div className="grid grid-cols-2 gap-4 md:gap-0">
        <div className="space-y-1">
          {leftColumnData.map((item, index) => (
            <UserItem key={item.name} item={item} indexNum={index + 1} />
          ))}
        </div>
        <div className="space-y-1">
          {rightColumnData.map((item, index) => (
            <UserItem key={item.name} item={item} indexNum={index + 6} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default TopVolume;
