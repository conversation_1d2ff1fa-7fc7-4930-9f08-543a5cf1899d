"use client";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getInitEventsListData } from "@/api/markets";
import { filterEventsByLanguage, filterOutCreatorEvents } from "@/utils";
import { useGlobalState } from "~~/services/store/store";

interface QueryParams {
  limit: number;
  offset: number;
  tag_id: string;
}

const useInfiniteScroll = (initialParams: any, fetchData: (params: QueryParams) => Promise<any>) => {
  const [data, setData] = useState<any[]>([]);
  const [queryParams, setQueryParams] = useState<QueryParams>(initialParams);
  const [hasMore, setHasMore] = useState(true);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const [initialLoad, setInitialLoad] = useState(true); // 添加初始加载标志
  const { current_language } = useGlobalState().nativeCurrency;

  const loadMore = useCallback(() => {
    setQueryParams(prevParams => ({
      ...prevParams,
      offset: prevParams.offset + prevParams.limit,
    }));
  }, [setQueryParams]);

  const observer = useMemo(() => {
    if (typeof window === "undefined") return null; // 确保只在客户端渲染时运行
    return new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !initialLoad) {
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: "20px",
        threshold: 1.0,
      },
    );
  }, [hasMore, loadMore, initialLoad]);

  useEffect(() => {
    const fetchDataAsync = async () => {
      try {
        let res;
        let response = [];

        if (queryParams.tag_id === "all") {
          res = await getInitEventsListData({ ...queryParams });
          const filteredByLanguage = filterEventsByLanguage(current_language, res.data.events);
          const filteredOutCreator = filterOutCreatorEvents(filteredByLanguage);
          response = filteredOutCreator;
        } else {
          res = await fetchData({ ...queryParams });
          const filteredByLanguage = filterEventsByLanguage(current_language, res.data.events);
          const filteredOutCreator = filterOutCreatorEvents(filteredByLanguage);
          response = filteredOutCreator;
        }
        if (queryParams.offset === 0) {
          setData(response);
        } else {
          setData(prevData => [...prevData, ...response]);
        }
        if (response.length < queryParams.limit) {
          setHasMore(false);
        }
        setInitialLoad(false);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    if (queryParams.tag_id) {
      fetchDataAsync();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams]);

  useEffect(() => {
    if (!observer) return; // 确保只在客户端渲染时运行
    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [observer]);

  return { data, hasMore, loadMoreRef, setQueryParams, setHasMore };
};

export default useInfiniteScroll;
