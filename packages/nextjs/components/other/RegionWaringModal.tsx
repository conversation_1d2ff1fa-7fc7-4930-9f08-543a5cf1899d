import { <PERSON>ton, Image, Link, <PERSON>dal, Modal<PERSON>ody, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON>eader } from "@heroui/react";
import { useTranslation } from "react-i18next";

export default function RegionWaringModal({ visible, onClose }: { visible: boolean; onClose: () => void }) {
  const { t } = useTranslation();

  return (
    <Modal isOpen={visible} onClose={onClose}>
      <ModalContent>
        <ModalHeader className="w-full flex-center text-medium">{"View-only mode"}</ModalHeader>
        <ModalBody className="px-6 pb-4">
          <div className="flex-center flex-col gap-2 text-medium">
            <Image
              width={100}
              height={100}
              className="flex-center flex-col gap-2"
              src="./Earth.svg"
              alt="Earth illustration"
            />
            <div className="text-gray-600 ">{t("Normal_Zone_Warning") + t("Normal_Zone_Warning_Terms")}</div>
            <Link href="/toe" className="flex items-center bg-white py-2 px-4 rounded-xl cursor-pointer">
              <Button radius="sm" onPress={onClose} className="mt-4 w-full text-medium px-4 bg-blue-600 text-white">
                {t("Got it")}
              </Button>
            </Link>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
