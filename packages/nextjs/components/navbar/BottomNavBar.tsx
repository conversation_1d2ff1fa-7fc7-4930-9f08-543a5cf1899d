import React, { useEffect, useState } from "react";
import <PERSON> from "next/link";
import VideoModal from "../learning-center/VideoModal";
import MoreNav from "./MoreNav";
import { getSubHeaderListData } from "@/api/events";
import MysteryBoxIcon from "@/components/icons/MysteryBoxIcon";
import { useUserAddress } from "@/hooks/useUserAddress";
import { setItem } from "@/utils";
import { useTranslation } from "react-i18next";
import { EllipsisHorizontalIcon, HomeIcon, Squares2X2Icon } from "@heroicons/react/24/outline";
import { useGlobalState } from "~~/services/store/store";

export const BottomNavBar = () => {
  const { i18n, t } = useTranslation();
  const { current_language } = useGlobalState().nativeCurrency;
  const { isConnected } = useUserAddress();

  const [navItems, setNavItems] = useState<any[]>([]);
  const [isMoreNavOpen, setIsMoreNavOpen] = useState(false);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [isHowItWorksVisible, setIsHowItWorksVisible] = useState(true);

  const targetMenu = { id: "1", label: "Global", slug: "Global", region: "en", forceShow: true };
  const currentNavItem = navItems.find(item => item.region == current_language);
  const setCurrentLanguage = useGlobalState(state => state.setCurrentLanguage);

  // How it works 视频数据
  const howItWorksVideo = {
    id: "UmVHNQ7QDcU",
    title: "How PredictOne Works",
    description: "Learn how to use PredictOne prediction market platform in this quick tutorial.",
    embedUrl: "https://www.youtube.com/embed/UmVHNQ7QDcU",
  };

  useEffect(() => {
    const fetchData = async () => {
      const res = await getSubHeaderListData();
      setNavItems(res.data.sub_header_nav);
    };

    fetchData();
  }, []);

  const handleClick = () => {
    setItem("currentNavItem", targetMenu);
    setItem("current_language", targetMenu.region);
    i18n.changeLanguage(targetMenu.region.toString());
    setCurrentLanguage(targetMenu.region);
  };

  const toggleMoreNav = () => {
    setIsMoreNavOpen(!isMoreNavOpen);
  };

  const handleHowItWorksClick = () => {
    setIsVideoModalOpen(true);
  };

  const handleCloseVideoModal = () => {
    setIsVideoModalOpen(false);
  };

  const handleCloseHowItWorks = () => {
    setIsHowItWorksVisible(false);
  };

  return (
    <>
      {!isConnected && isHowItWorksVisible && (
        <div className="fixed bottom-16 left-0 right-0 bg-white border-t border-gray-200 md:hidden z-40 shadow-lg max-w-full overflow-hidden">
          <div className="flex items-center justify-between py-3 px-4 max-w-full">
            <button
              onClick={handleHowItWorksClick}
              className="flex items-center space-x-2 text-blue-600 font-semibold text-sm hover:text-blue-700 transition-colors duration-200 flex-shrink-0"
            >
              <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8 5v10l8-5-8-5z" />
              </svg>
              <span className="whitespace-nowrap">How it works</span>
            </button>
            <button
              onClick={handleCloseHowItWorks}
              className="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 flex-shrink-0 ml-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      <div className="btm-nav btm-nav-md fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden z-50">
        <Link href="/" className="flex flex-col items-center justify-center w-full p-2">
          <HomeIcon className="h-6 w-6" />
          <span className="text-xs">{t("nav.home")}</span>
        </Link>
        {currentNavItem && (
          <Link
            href={`/markets/${currentNavItem.slug}`}
            onClick={handleClick}
            className="flex flex-col items-center justify-center w-full p-2"
          >
            <Squares2X2Icon className="h-6 w-6" />
            <span className="text-xs">{t("nav.markets")}</span>
          </Link>
        )}

        <Link href="/mystery" className="flex flex-col items-center justify-center w-full p-2 relative">
          <MysteryBoxIcon size={24} />
          <span className="text-xs">{t("nav.mystery")}</span>
          {/* 新功能标识 */}
          <div className="absolute top-2 right-6 w-2 h-2 bg-red-500 rounded-full animate-pulse" />
        </Link>

        <button onClick={toggleMoreNav} className="flex flex-col items-center justify-center w-full p-2">
          <EllipsisHorizontalIcon className="h-6 w-6" />
          <span className="text-xs">{t("nav.more")}</span>
        </button>
      </div>

      <MoreNav isOpen={isMoreNavOpen} onClose={toggleMoreNav} />

      {/* How it works 视频模态框 */}
      <VideoModal isOpen={isVideoModalOpen} onClose={handleCloseVideoModal} video={howItWorksVideo} />
    </>
  );
};
