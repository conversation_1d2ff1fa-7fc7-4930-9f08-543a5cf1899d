import React, { useCallback, useEffect, useState } from "react";
import Link from "next/link";
import CustomConnectModal from "@/components/login/CustomConnectModal";
import computeProxyAddress from "@/contracts/computeProxyAddress";
import { useLoginActions } from "@/hooks/useLoginActions";
import { useLoginModal } from "@/hooks/useLoginModal";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useMagicStore } from "@/services/store/magicStore";
import { getItem } from "@/utils";
import { Button, Card, CardBody, Divider, Modal, ModalBody, ModalContent } from "@heroui/react";
import { useTranslation } from "react-i18next";
import { getAddress } from "viem";
import {
  ArrowLeftEndOnRectangleIcon,
  ArrowRightEndOnRectangleIcon,
  CheckIcon,
  DocumentDuplicateIcon,
} from "@heroicons/react/24/outline";
import { BlockieAvatar } from "~~/components/scaffold-eth";

interface MoreNavProps {
  isOpen: boolean;
  onClose: () => void;
}

const MoreNav: React.FC<MoreNavProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const { address: userAddress, isMagic, isConnected } = useUserAddress();
  const { magicUser } = useMagicStore();
  const { logout } = useLoginActions();
  const { isLoginModalOpen, openLoginModal, closeLoginModal } = useLoginModal();

  const [isLogoutLoading, setIsLogoutLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [addressCopied, setAddressCopied] = useState(false);
  const [proxyWallet, setProxyWallet] = useState("");

  const baseNavigationList = [
    {
      label: t("nav.portfolio"),
      href: "/portfolio",
    },
    {
      label: t("nav.rank"),
      href: "/leaderboard",
    },
  ];

  const authNavigationList = [
    {
      label: t("nav.addFunds"),
      href: "/wallet",
    },
  ];

  // 根据登录状态组合导航列表
  const navigationList = isConnected
    ? [...baseNavigationList.slice(0, 1), ...authNavigationList, ...baseNavigationList.slice(1)]
    : baseNavigationList;

  const getProxyWallet = useCallback(async () => {
    if (!userAddress) return;

    try {
      const proxyAddress = await computeProxyAddress(userAddress);
      if (proxyAddress) {
        setProxyWallet(proxyAddress);
      }
    } catch (error) {
      console.error(t("nav.errors.getProxyWalletFailed"), error);
    }
  }, [userAddress, t]);

  useEffect(() => {
    if (userAddress) {
      const localProxyWallet = getItem(`login_proxyWallet`);
      if (localProxyWallet) {
        setProxyWallet(localProxyWallet);
      } else {
        getProxyWallet();
      }
    } else {
      setProxyWallet("");
    }
  }, [userAddress, getProxyWallet]);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [isOpen]);

  const handleClose = useCallback(() => {
    setIsVisible(false);
    setTimeout(onClose, 200);
  }, [onClose]);

  const handleCopyAddress = useCallback(async () => {
    if (!proxyWallet) return;

    try {
      await navigator.clipboard.writeText(proxyWallet);
      setAddressCopied(true);
      setTimeout(() => {
        setAddressCopied(false);
      }, 2000);
    } catch (error) {
      console.error(t("nav.errors.copyFailed"), error);
    }
  }, [proxyWallet, t]);

  const handleLogin = useCallback(() => {
    openLoginModal();
  }, [openLoginModal]);

  useEffect(() => {
    if (isConnected && isLoginModalOpen) {
      closeLoginModal();
      setTimeout(() => {
        handleClose();
      }, 300);
    }
  }, [isConnected, isLoginModalOpen, closeLoginModal, handleClose]);

  const handleLogout = useCallback(async () => {
    setIsLogoutLoading(true);
    try {
      await logout();
      handleClose();
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      setIsLogoutLoading(false);
    }
  }, [logout, handleClose]);

  const handleLoginModalClose = useCallback(() => {
    closeLoginModal();
  }, [closeLoginModal]);

  // 修复逻辑：只使用 proxy wallet 地址用于显示
  const checkSumAddress = proxyWallet ? getAddress(proxyWallet) : "";

  if (!isOpen) return null;

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        placement="top-center"
        hideCloseButton
        className="m-0 h-full"
        classNames={{
          wrapper: "items-start justify-start",
          base: "m-0 h-full max-h-full w-80 rounded-none",
          body: "p-0",
        }}
      >
        <ModalContent>
          <ModalBody>
            <div
              className={`bg-white w-full h-full shadow-lg transform transition-transform duration-300 flex flex-col ${
                isVisible ? "translate-x-0" : "-translate-x-full"
              }`}
            >
              {/* Header */}
              <div className="flex justify-between items-center p-4 border-b">
                <h2 className="text-lg font-semibold">{t("nav.more")}</h2>
              </div>

              {/* User Info - 只在登录时显示 */}
              {isConnected && userAddress && (
                <div className="p-4 border-b">
                  <Card className="bg-gray-50">
                    <CardBody className="p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <BlockieAvatar
                          address={checkSumAddress}
                          size={40}
                          ensImage={magicUser?.email ? undefined : undefined}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-900 mb-1">
                            {isMagic && magicUser?.email ? magicUser.email : t("nav.walletUser")}
                          </div>
                          <div className="text-xs text-gray-500">{t("nav.proxyWallet")}</div>
                        </div>
                      </div>

                      {/* 只显示 proxy wallet 地址 */}
                      <div
                        className="flex items-center justify-between p-2 bg-gray-100 rounded-lg cursor-pointer hover:bg-gray-200 transition-colors"
                        onClick={handleCopyAddress}
                      >
                        <span className="text-xs font-mono text-gray-700">
                          {checkSumAddress
                            ? `${checkSumAddress.slice(0, 6)}...${checkSumAddress.slice(-4)}`
                            : t("nav.loading")}
                        </span>
                        <div className="flex items-center">
                          {addressCopied ? (
                            <CheckIcon className="h-4 w-4 text-green-500" />
                          ) : (
                            <DocumentDuplicateIcon className="h-4 w-4 text-gray-500" />
                          )}
                        </div>
                      </div>

                      {addressCopied && (
                        <div className="text-xs text-green-600 mt-1 text-center">{t("nav.copied")}</div>
                      )}
                    </CardBody>
                  </Card>
                </div>
              )}

              <Divider className="bg-gray-100" />
              <nav className="flex-1">
                <div className="space-y-2">
                  {navigationList.map(item => (
                    <Link key={item.href} href={item.href} onClick={handleClose} className="block">
                      <div className="w-full p-3 text-left hover:bg-gray-100 rounded-lg cursor-pointer transition-colors">
                        <span className="text-lg font-semibold text-gray-700">{item.label}</span>
                      </div>
                    </Link>
                  ))}
                </div>
                <div className="flex items-center gap-4 p-2">
                  <Link
                    href="mailto:<EMAIL>"
                    className="flex justify-center items-center w-12 h-12 rounded-full bg-white border border-gray-200 transition-all duration-200 cursor-pointer hover:bg-gray-50 hover:border-gray-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 20 20"
                      fill="none"
                      className="w-5 h-5 text-gray-600"
                    >
                      <g clipPath="url(#clip0_3518_21)">
                        <path
                          d="M0.76001 5.85791L9.38444 10.6156C9.76806 10.8271 10.232 10.8271 10.6156 10.6156L19.24 5.85791"
                          stroke="currentColor"
                          strokeWidth="1.8"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M3.30897 17.3284L16.6911 17.3284C18.0988 17.3284 19.24 16.1872 19.24 14.7794V5.22077C19.24 3.81302 18.0988 2.67181 16.6911 2.67181L3.30897 2.67181C1.90122 2.67181 0.760006 3.81302 0.760006 5.22077V14.7794C0.760006 16.1872 1.90122 17.3284 3.30897 17.3284Z"
                          stroke="currentColor"
                          strokeWidth="1.8"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_3518_21">
                          <rect width="20" height="20" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </Link>

                  <Link
                    href="https://discord.com/invite/jPdfwmaC9n"
                    className="flex justify-center items-center w-12 h-12 rounded-full bg-white border border-gray-200 transition-all duration-200 cursor-pointer hover:bg-gray-50 hover:border-gray-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 20 20"
                      fill="none"
                      className="w-5 h-5 text-gray-600"
                    >
                      <path
                        d="M16.9308 3.70833C15.6561 3.14167 14.2844 2.73333 12.8436 2.5C12.6564 2.83333 12.4688 3.2 12.3542 3.475C10.7813 3.25833 9.225 3.25833 7.68725 3.475C7.57267 3.2 7.37558 2.83333 7.18725 2.5C5.74517 2.73333 4.3735 3.145 3.09883 3.71667C0.444833 7.58333 -0.277 11.35 0.0852167 15.0667C1.943 16.4583 3.74267 17.3583 5.501 17.9333C5.94392 17.3417 6.34308 16.7167 6.69458 16.0583C6.05975 15.8167 5.45692 15.5167 4.88983 15.1667C5.02567 15.0667 5.159 14.9583 5.28858 14.8417C8.38892 16.2583 11.7627 16.2583 14.8203 14.8417C14.9498 14.9583 15.0831 15.0667 15.219 15.1667C14.6519 15.5208 14.0479 15.8208 13.4131 16.0625C13.7646 16.7167 14.1621 17.3458 14.6065 17.9375C16.3673 17.3625 18.1669 16.4625 20.0252 15.0667C20.4448 10.7417 19.1769 7.00833 16.9308 3.70833ZM6.6975 12.6083C5.69892 12.6083 4.88608 11.675 4.88608 10.5333C4.88608 9.39167 5.68142 8.45833 6.6975 8.45833C7.71358 8.45833 8.52642 9.39167 8.50892 10.5333C8.51267 11.675 7.71358 12.6083 6.6975 12.6083ZM13.411 12.6083C12.4127 12.6083 11.5994 11.675 11.5994 10.5333C11.5994 9.39167 12.3948 8.45833 13.411 8.45833C14.4273 8.45833 15.2398 9.39167 15.2223 10.5333C15.2223 11.675 14.4273 12.6083 13.411 12.6083Z"
                        fill="currentColor"
                      />
                    </svg>
                  </Link>

                  <Link
                    href="https://x.com/Predict_one"
                    className="flex justify-center items-center w-12 h-12 rounded-full bg-white border border-gray-200 transition-all duration-200 cursor-pointer hover:bg-gray-50 hover:border-gray-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 20 20"
                      fill="none"
                      className="w-5 h-5 text-gray-600"
                    >
                      <path
                        d="M15.2719 1.58655H18.0831L11.9414 8.60612L19.1667 18.1582H13.5094L9.07837 12.3649L4.0083 18.1582H1.19537L7.76454 10.65L0.833344 1.58655H6.63427L10.6395 6.88182L15.2719 1.58655ZM14.2853 16.4755H15.843L5.78784 3.18082H4.11623L14.2853 16.4755Z"
                        fill="currentColor"
                      />
                    </svg>
                  </Link>
                </div>
              </nav>

              {/* 底部按钮区域 */}
              <Divider />
              <div className="p-4">
                {isConnected ? (
                  // 已登录：显示登出按钮
                  <Button
                    color="danger"
                    variant="bordered"
                    className="w-full"
                    startContent={<ArrowLeftEndOnRectangleIcon className="h-5 w-5" />}
                    onPress={handleLogout}
                    isLoading={isLogoutLoading}
                    disabled={isLogoutLoading}
                  >
                    {isLogoutLoading ? t("nav.disconnecting") : t("nav.disconnect")}
                  </Button>
                ) : (
                  // 未登录：显示登录按钮
                  <Button
                    color="primary"
                    variant="solid"
                    className="w-full"
                    startContent={<ArrowRightEndOnRectangleIcon className="h-5 w-5" />}
                    onPress={handleLogin}
                  >
                    {t("Normal_Login")}
                  </Button>
                )}
              </div>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>

      <CustomConnectModal isOpen={isLoginModalOpen} onClose={handleLoginModalClose} />
    </>
  );
};

export default MoreNav;
