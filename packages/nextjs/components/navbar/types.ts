import { ReactNode } from "react";

export type HeaderMenuLink = {
  label: string;
  href?: string;
  icon?: React.ReactNode;
};

export type MenuItemProps = {
  label: string;
  icon: React.ReactNode;
  href?: string;
};

export type LinkItem = {
  label: string;
  href: string;
  icon: ReactNode;
};

export type MenuLinkItemProps = {
  label: string;
  icon: React.ReactNode;
  links?: LinkItem[];
};

export type DropdownMenuProps = {
  title: string;
  links: LinkItem[];
};

export interface NavItem {
  type: string;
  id: string;
  label: string;
  slug: string;
  region: string;
  image?: string;
  forceShow?: boolean;
  forceHide?: boolean;
}
