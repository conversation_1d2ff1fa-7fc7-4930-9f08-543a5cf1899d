import React, { useCallback, useEffect, useRef, useState } from "react";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import { NavItem } from "./types";
import { getSubHeaderListData } from "@/api/events";
import { getItem, setItem } from "@/utils";
import { Card, CardFooter, Image, Spinner } from "@heroui/react";
import { BookOpen, Gift, Telescope, Users, Volleyball } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useGlobalState } from "~~/services/store/store";

const NavItemCard: React.FC<{ itemData: NavItem; handleClick: any }> = ({ itemData, handleClick }) => {
  return (
    <Card isFooterBlurred className="w-[128px] h-[128px] cursor-pointer hover:shadow-[0_0_0_4px_rgba(96,165,250,1)]">
      <Image
        alt=""
        className="z-0 w-[128px] h-[128px] object-cover"
        src={itemData?.image}
        width={128}
        height={128}
        onClick={() => handleClick(itemData)}
      />
      <CardFooter className="absolute w-full flex bg-black/40 bottom-0 z-10 border-t-1 border-default-600 dark:border-default-100 p-1">
        <div
          onClick={() => handleClick(itemData)}
          className="flex w-full h-full text-medium font-semibold text-white items-center justify-center"
        >
          {itemData?.label}
        </div>
      </CardFooter>
    </Card>
  );
};

export const SubHeaderNav: React.FC = () => {
  const router = useRouter();
  const [currentNavText, setCurrentNavText] = useState<string>("");
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [navItems, setNavItems] = useState<NavItem[]>([]);
  const [isNavigating, setIsNavigating] = useState(false);
  const [navigatingTo, setNavigatingTo] = useState<string | null>(null);
  const [targetPath, setTargetPath] = useState<string | null>(null);
  const pathname = usePathname();
  const { i18n } = useTranslation();
  const { current_language } = useGlobalState().nativeCurrency;
  const setCurrentLanguage = useGlobalState(state => state.setCurrentLanguage);
  const [currentHoveredItem, setCurrentHoveredItem] = useState<string>("");
  const [hideTimeout, setHideTimeout] = useState<NodeJS.Timeout | null>(null);
  const [isInDropdown, setIsInDropdown] = useState(false);
  const isInDropdownRef = useRef(false);

  // 添加状态变化监听
  useEffect(() => {
    isInDropdownRef.current = isInDropdown;
  }, [dropdownVisible, isInDropdown]);

  // 检查当前页面是否匹配
  const isCurrentPage = useCallback(
    (path: string) => {
      return pathname.startsWith(path);
    },
    [pathname],
  );

  // 通用导航处理函数
  const handleNavigation = useCallback(
    async (path: string, buttonName: string) => {
      setNavigatingTo(buttonName);
      setTargetPath(path);
      try {
        await router.push(path);
      } catch (error) {
        // 导航失败时清除loading状态
        setNavigatingTo(null);
        setTargetPath(null);
      }
    },
    [router],
  );

  // 监听路径变化，当到达目标路径时清除loading状态
  useEffect(() => {
    if (targetPath && pathname === targetPath && navigatingTo) {
      console.log(`🏁 Navigation completed to ${pathname}, clearing loading state`);
      setNavigatingTo(null);
      setTargetPath(null);
    }
  }, [pathname, targetPath, navigatingTo]);

  // 根据浏览器默认语言设置初始地区
  const initializeLanguageFromBrowser = useCallback(
    (navItems: NavItem[]) => {
      const storedLanguage = getItem("current_language");

      // 如果没有存储的语言，使用浏览器默认语言
      if (!storedLanguage && typeof window !== "undefined") {
        const browserLanguage = navigator.language.split("-")[0]; // 获取浏览器语言代码

        // 查找匹配的地区
        const matchingRegion = navItems.find(item => item.region === browserLanguage);

        if (matchingRegion) {
          setItem("currentNavItem", matchingRegion);
          setItem("current_language", matchingRegion.region);
          i18n.changeLanguage(matchingRegion.region);
          setCurrentLanguage(matchingRegion.region);
          setCurrentNavText(matchingRegion.label);
        } else {
          // 如果没有匹配的地区，使用默认的英语地区
          const defaultRegion = navItems.find(item => item.region === "en");
          if (defaultRegion) {
            setItem("currentNavItem", defaultRegion);
            setItem("current_language", defaultRegion.region);
            i18n.changeLanguage(defaultRegion.region);
            setCurrentLanguage(defaultRegion.region);
            setCurrentNavText(defaultRegion.label);
          }
        }
      } else {
        // 如果有存储的语言，设置对应的地区文本和currentNavItem
        const currentRegion = navItems.find(item => item.region === storedLanguage);
        if (currentRegion) {
          setItem("currentNavItem", currentRegion); // 修复：设置currentNavItem
          setCurrentNavText(currentRegion.label);
        } else {
          // 如果找不到匹配的地区，使用默认的英语地区
          const defaultRegion = navItems.find(item => item.region === "en");
          if (defaultRegion) {
            setItem("currentNavItem", defaultRegion);
            setItem("current_language", defaultRegion.region);
            i18n.changeLanguage(defaultRegion.region);
            setCurrentLanguage(defaultRegion.region);
            setCurrentNavText(defaultRegion.label);
          } else {
            setCurrentNavText("Select Region");
          }
        }
      }
    },
    [i18n, setCurrentLanguage],
  );

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getSubHeaderListData();
        const navItemsData = res.data.sub_header_nav;
        setNavItems(navItemsData);

        // 初始化语言设置
        initializeLanguageFromBrowser(navItemsData);
      } catch (error) {
        setNavItems([]);
      }
    };

    fetchData();
  }, [initializeLanguageFromBrowser]);

  // 监听current_language变化，同步更新导航文本
  useEffect(() => {
    if (navItems.length > 0 && current_language) {
      const currentRegion = navItems.find(item => item.region === current_language);
      if (currentRegion) {
        setCurrentNavText(currentRegion.label);
      }
    }
  }, [current_language, navItems]);

  useEffect(() => {
    const storedNavItem = getItem("currentNavItem");
    if (storedNavItem) {
      setCurrentNavText(storedNavItem.label);
    }
  }, [pathname]);

  useEffect(() => {
    return () => {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }
    };
  }, [hideTimeout]);

  const handleClick = useCallback(
    async (item: NavItem) => {
      setIsNavigating(true);

      try {
        // 更新存储和全局状态
        setItem("currentNavItem", item);
        setItem("current_language", item.region);
        i18n.changeLanguage(item.region.toString());
        setCurrentLanguage(item.region);

        // 更新当前导航文本
        setCurrentNavText(item.label);

        // 关闭下拉菜单
        setDropdownVisible(false);
      } catch (error) {
      } finally {
        setTimeout(() => {
          setIsNavigating(false);
          console.log("🏁 Region navigation state cleared");
        }, 1000); // 延迟500ms清除loading状态
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [router, i18n, setCurrentLanguage],
  );

  const sortedNavItems = navItems.sort((a, b) => Number(a.id) - Number(b.id));
  const regionNavItems = sortedNavItems.filter(item => item.type === "normal");

  // 检查是否在事件详情页
  const isEventDetailPage = pathname.startsWith("/event");

  return (
    <div className="relative bg-transparent w-full">
      {/* 背景装饰 */}

      <div className="relative flex items-center min-h-14 md:min-h-12 px-3 md:px-6 w-full">
        <div
          className="flex space-x-1 md:space-x-4 items-center min-h-14 md:min-h-12 overflow-x-auto scrollbar-hide"
          style={{
            WebkitOverflowScrolling: "touch",
            scrollBehavior: "smooth",
          }}
        >
          {/* LIVE 指示器 - 移动端优化 */}
          <div className="flex items-center space-x-2 px-3 py-1.5 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-full flex-shrink-0">
            <div className="relative">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
              <div className="absolute inset-0 w-2 h-2 bg-red-500 rounded-full animate-ping opacity-75" />
            </div>
            <span className="text-red-600 font-semibold text-sm">LIVE</span>
          </div>

          {/* Global 地区切换 - 延迟隐藏逻辑 */}
          {!isEventDetailPage && (
            <div className="relative">
              {/* 按钮区域 */}
              <div
                onMouseEnter={() => {
                  if (!isNavigating) {
                    // 清除隐藏定时器
                    if (hideTimeout) {
                      clearTimeout(hideTimeout);
                      setHideTimeout(null);
                    }
                    setDropdownVisible(true);
                    setCurrentHoveredItem("region");
                  }
                }}
                onMouseLeave={() => {
                  // 如果用户在下拉框内，不设置延迟隐藏
                  if (isInDropdown) {
                    return;
                  }
                  // 设置1秒延迟隐藏
                  const timeout = setTimeout(() => {
                    // 使用ref获取最新的状态
                    if (!isInDropdownRef.current) {
                      setDropdownVisible(false);
                    } else {
                    }
                  }, 1000);
                  setHideTimeout(timeout);
                }}
              >
                <div
                  className={`group flex items-center px-2 md:px-4 py-2 rounded-lg md:rounded-xl transition-all duration-300 flex-shrink-0 ${
                    isNavigating
                      ? "bg-gradient-to-r from-blue-500 to-cyan-500 text-white cursor-wait shadow-lg shadow-blue-500/25"
                      : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 cursor-pointer border border-gray-200 hover:border-blue-300 hover:shadow-md"
                  }`}
                  onClick={() => {
                    if (!isNavigating) {
                      setDropdownVisible(!dropdownVisible);
                      setCurrentHoveredItem("region");
                    }
                  }}
                >
                  <div className="flex items-center space-x-2">
                    {isNavigating ? (
                      <Spinner size="sm" className="text-white" />
                    ) : (
                      <Telescope size={16} className="group-hover:rotate-12 transition-transform duration-300" />
                    )}
                    <span className="font-medium text-sm truncate max-w-[60px] md:max-w-none">{currentNavText}</span>
                  </div>
                  {!isNavigating && (
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/0 via-purple-500/0 to-cyan-500/0 group-hover:from-blue-500/5 group-hover:via-purple-500/5 group-hover:to-cyan-500/5 transition-all duration-300" />
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Sports 按钮 - 移动端优化 */}
          <div
            className={`group relative flex items-center px-2 md:px-4 py-2 rounded-lg md:rounded-xl transition-all duration-300 flex-shrink-0 ${
              navigatingTo === "sports"
                ? "bg-gradient-to-r from-orange-500 to-red-500 text-white cursor-wait shadow-lg shadow-orange-500/25"
                : isCurrentPage("/sports")
                ? "bg-gradient-to-r from-orange-50 to-red-50 text-orange-600 border border-orange-200 shadow-md"
                : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 cursor-pointer border border-gray-200 hover:border-orange-300 hover:shadow-md"
            }`}
            onClick={() => navigatingTo !== "sports" && handleNavigation("/sports", "sports")}
          >
            <div className="flex items-center space-x-2">
              {navigatingTo === "sports" ? (
                <Spinner size="sm" className="text-white" />
              ) : (
                <Volleyball size={16} className="group-hover:rotate-12 transition-transform duration-300" />
              )}
              <span className="font-medium text-sm">Sports</span>
            </div>
            {!navigatingTo && !isCurrentPage("/sports") && (
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-orange-500/0 to-red-500/0 group-hover:from-orange-500/5 group-hover:to-red-500/5 transition-all duration-300" />
            )}
          </div>
          {/* RedPocket 按钮 - 移动端优化 */}
          <div
            className={`group relative flex items-center px-2 md:px-4 py-2 rounded-lg md:rounded-xl transition-all duration-300 flex-shrink-0 ${
              navigatingTo === "redpocket"
                ? "bg-gradient-to-r from-yellow-500 to-orange-500 text-white cursor-wait shadow-lg shadow-yellow-500/25"
                : isCurrentPage("/redpocket")
                ? "bg-gradient-to-r from-yellow-50 to-orange-50 text-yellow-600 border border-yellow-200 shadow-md"
                : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 cursor-pointer border border-gray-200 hover:border-yellow-300 hover:shadow-md"
            }`}
            onClick={() => navigatingTo !== "redpocket" && handleNavigation("/redpocket", "redpocket")}
          >
            <div className="flex items-center space-x-2">
              {navigatingTo === "redpocket" ? (
                <Spinner size="sm" className="text-white" />
              ) : (
                <Gift size={16} className="group-hover:rotate-12 transition-transform duration-300" />
              )}
              <span className="font-medium text-sm">RedPocket</span>
            </div>
            {!navigatingTo && !isCurrentPage("/redpocket") && (
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-yellow-500/0 to-orange-500/0 group-hover:from-yellow-500/5 group-hover:to-orange-500/5 transition-all duration-300" />
            )}
          </div>
          {/* Learning Center 按钮 - 移动端显示 */}
          <div
            className={`group relative flex items-center px-2 md:px-4 py-2 rounded-lg md:rounded-xl transition-all duration-300 flex-shrink-0 ${
              navigatingTo === "learning"
                ? "bg-gradient-to-r from-green-500 to-emerald-500 text-white cursor-wait shadow-lg shadow-green-500/25"
                : isCurrentPage("/learning-center")
                ? "bg-gradient-to-r from-green-50 to-emerald-50 text-green-600 border border-green-200 shadow-md"
                : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 cursor-pointer border border-gray-200 hover:border-green-300 hover:shadow-md"
            }`}
            onClick={() => navigatingTo !== "learning" && handleNavigation("/learning-center", "learning")}
          >
            <div className="flex items-center space-x-1 md:space-x-2">
              {navigatingTo === "learning" ? (
                <Spinner size="sm" className="text-white" />
              ) : (
                <BookOpen size={14} className="md:w-4 md:h-4 group-hover:rotate-12 transition-transform duration-300" />
              )}
              <span className="font-medium text-xs md:text-sm">
                <span className="md:hidden">Learn</span>
                <span className="hidden md:inline">Learning Center</span>
              </span>
            </div>
            {!navigatingTo && !isCurrentPage("/learning-center") && (
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-500/0 to-emerald-500/0 group-hover:from-green-500/5 group-hover:to-emerald-500/5 transition-all duration-300" />
            )}
          </div>

          {/* Creator 按钮 - 移动端显示 */}
          <div
            className={`group relative flex items-center px-2 md:px-4 py-2 rounded-lg md:rounded-xl transition-all duration-300 flex-shrink-0 ${
              navigatingTo === "creator"
                ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white cursor-wait shadow-lg shadow-purple-500/25"
                : isCurrentPage("/creator")
                ? "bg-gradient-to-r from-purple-50 to-pink-50 text-purple-600 border border-purple-200 shadow-md"
                : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 cursor-pointer border border-gray-200 hover:border-purple-300 hover:shadow-md"
            }`}
            onClick={() => navigatingTo !== "creator" && handleNavigation("/creator", "creator")}
          >
            <div className="flex items-center space-x-1 md:space-x-2">
              {navigatingTo === "creator" ? (
                <Spinner size="sm" className="text-white" />
              ) : (
                <Users size={14} className="md:w-4 md:h-4 group-hover:rotate-12 transition-transform duration-300" />
              )}
              <span className="font-medium text-xs md:text-sm">Creator</span>
            </div>
            {!navigatingTo && !isCurrentPage("/creator") && (
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/0 to-pink-500/0 group-hover:from-purple-500/5 group-hover:to-pink-500/5 transition-all duration-300" />
            )}
          </div>
        </div>
      </div>

      {/* 下拉菜单 - 延迟隐藏逻辑 */}
      {dropdownVisible && (
        <div
          className="absolute px-4 md:px-6 py-4 left-0 top-full w-full bg-white bg-opacity-60 backdrop-filter backdrop-blur-lg shadow-lg z-[9998]"
          onMouseEnter={() => {
            // 用户进入下拉框，清除隐藏定时器，持久展示
            if (hideTimeout) {
              clearTimeout(hideTimeout);
              setHideTimeout(null);
            }
            setIsInDropdown(true);
          }}
          onMouseLeave={() => {
            // 用户离开下拉框，立即隐藏
            setIsInDropdown(false);
            setDropdownVisible(false);
          }}
        >
          <div className="hidden md:flex gap-4 flex-wrap">
            {/* 在事件详情页时不显示地区下拉菜单 */}
            {currentHoveredItem === "region" &&
              !isEventDetailPage &&
              regionNavItems.map(item => <NavItemCard key={item.id} itemData={item} handleClick={handleClick} />)}
          </div>

          <div className="flex flex-col md:hidden">
            {/* 在事件详情页时不显示地区下拉菜单 */}
            {currentHoveredItem === "region" &&
              !isEventDetailPage &&
              regionNavItems.map(item => (
                <div
                  key={item.id}
                  onClick={() => handleClick(item)}
                  className="p-2 w-full hover:bg-gray-100 hover:text-blue-600 rounded-lg cursor-pointer"
                >
                  {item.label}
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
};
