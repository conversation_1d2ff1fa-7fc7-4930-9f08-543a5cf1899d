import React, { useRef, useState } from "react";
import Link from "next/link";
import { DropdownMenuProps, MenuLinkItemProps } from "./types";
import { useTranslation } from "react-i18next";

const DropdownNormalMenu: React.FC<DropdownMenuProps> = ({ title, links }) => (
  <div className="mb-4">
    <h3 className="text-lg font-semibold">{title}</h3>
    <div className="grid grid-cols-2 gap-1">
      {links.map(link => (
        <Link key={link.href} href={link.href} passHref>
          <div className="flex items-center p-2 hover:bg-gray-100 rounded-md cursor-pointer">
            {link.icon}
            <span className="ml-2 text-blue-500 hover:underline">{link.label}</span>
          </div>
        </Link>
      ))}
    </div>
  </div>
);

const MenuLinkItem: React.FC<MenuLinkItemProps> = ({ label, icon, links }) => {
  const [isHovered, setIsHovered] = useState(false);
  const { t } = useTranslation();
  const hoverTimeout = useRef<NodeJS.Timeout | null>(null);

  const handleMouseEnter = () => {
    if (hoverTimeout.current) {
      clearTimeout(hoverTimeout.current);
    }
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    hoverTimeout.current = setTimeout(() => {
      setIsHovered(false);
    }, 300);
  };

  return (
    <li className="hidden lg:flex relative text-sm" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <div className="flex flex-col items-center p-1 gap-1 cursor-pointer hover:bg-secondary hover:shadow-lg hover:rounded-lg focus:bg-secondary active:text-neutral">
        {icon}
        <span>{t(label)}</span>
      </div>
      {label === "Normal_Markets" && links && (
        <div
          className={`absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-80 bg-white shadow-lg rounded-lg p-4 z-10 transition-opacity duration-300 ease-in-out ${
            isHovered ? "opacity-100 visible" : "opacity-0 invisible"
          }`}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <DropdownNormalMenu title="Browse" links={links} />
        </div>
      )}
    </li>
  );
};

export default MenuLinkItem;
