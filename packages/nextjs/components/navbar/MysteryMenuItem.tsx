"use client";

import React from "react";
import Link from "next/link";
import MysteryBoxIcon from "@/components/icons/MysteryBoxIcon";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";

interface MysteryMenuItemProps {
  className?: string;
  size?: "sm" | "md" | "lg";
}

const MysteryMenuItem: React.FC<MysteryMenuItemProps> = ({ className = "", size = "md" }) => {
  const { t } = useTranslation();

  const sizeConfig = {
    sm: {
      iconSize: 20,
      textSize: "text-xs",
      padding: "py-0 px-2",
      gap: "gap-0",
    },
    md: {
      iconSize: 24,
      textSize: "text-sm",
      padding: "py-1 px-2",
      gap: "gap-0",
    },
    lg: {
      iconSize: 28,
      textSize: "text-sm",
      padding: "py-1 px-2",
      gap: "gap-0",
    },
  };

  const config = sizeConfig[size];

  return (
    <motion.div className={`relative ${className}`} whileHover={{ scale: 1.05 }} whileTap={{ scale: 1 }}>
      <Link
        href="/mystery"
        className={`
          flex flex-col items-center
          ${config.padding} ${config.gap}
          ${config.textSize} text-gray-700 cursor-pointer
          hover:bg-gradient-to-br hover:from-yellow-50 hover:to-orange-50
          hover:shadow-lg hover:shadow-yellow-200/50
          rounded-lg transition-all duration-300
          focus:bg-gradient-to-br focus:from-yellow-50 focus:to-orange-50
          active:bg-gradient-to-br active:from-yellow-100 active:to-orange-100
          active:text-neutral
          group
        `}
      >
        {/* 背景光晕效果 */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-200/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          animate={{
            scale: [1, 1.02, 1],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />

        {/* 图标容器 */}
        <div className="relative z-10">
          <MysteryBoxIcon size={config.iconSize} />
        </div>

        {/* 文字标签 */}
        <span className="relative z-10 -mt-1 group-hover:text-orange-700 transition-colors duration-300">
          {t("nav.mystery")}
        </span>

        {/* 新功能标识 */}
        <motion.div
          className="absolute top-1 right-2.5 w-2 h-2 bg-red-500 rounded-full z-20"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.8, 1, 0.8],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </Link>
    </motion.div>
  );
};

export default MysteryMenuItem;
