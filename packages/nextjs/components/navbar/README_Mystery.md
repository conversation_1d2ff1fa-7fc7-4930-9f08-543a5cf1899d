# Mystery Box 菜单入口功能

## 🎁 功能概述

为项目添加了一个彩色动画宝箱图标的 Mystery 入口，用户可以通过点击该入口跳转到 `/mystery` 页面。该入口在所有设备上都可见，无论用户是否登录。

## ✨ 最新更新 (v3.0 - 海盗宝箱风格)

### 海盗宝箱重新设计
- **弧形盖子**：使用 SVG path 创建真实的弧形海盗宝箱盖子
- **木质纹理**：深棕色木质渐变，更真实的海盗宝箱外观
- **金属装饰**：金色的金属边框和铰链装饰
- **阴影效果**：底部椭圆阴影，增加立体感
- **比例优化**：更合理的宝箱比例，避免变形问题

### 样式对齐优化
- **文字样式**：与同级导航栏完全对齐（`text-sm text-gray-700`）
- **布局样式**：使用与其他菜单项相同的 `hover:bg-secondary hover:shadow-lg` 效果
- **尺寸统一**：图标和文字尺寸与导航栏其他项目保持一致

### 交互效果升级
- **宝箱开启动画**：鼠标悬停时弧形盖子向后旋转打开（-25度）
- **金币飞出效果**：3个带有$符号的金币从宝箱内部飞出，不同高度和速度
- **增强粒子效果**：悬停时粒子数量增加到6个，运动更加活跃
- **闪光强化**：悬停时闪光效果更加明显和频繁

## 📁 新增文件

### 1. 动画宝箱图标组件
- **文件**: `packages/nextjs/components/icons/MysteryBoxIcon.tsx`
- **功能**: 
  - 彩色渐变宝箱设计（金色主体，棕色盖子，银色锁扣）
  - 浮动动画效果
  - 闪光星星旋转动画
  - 浮动粒子效果
  - 悬停和点击交互动画

### 2. Mystery 菜单项组件
- **文件**: `packages/nextjs/components/navbar/MysteryMenuItem.tsx`
- **功能**:
  - 响应式设计（支持 sm/md/lg 三种尺寸）
  - 悬停时的背景光晕效果
  - 文字发光动画
  - 新功能红点标识
  - 多语言支持

## 🎨 设计特色

### 视觉效果
- **海盗宝箱造型**:
  - 深棕色木质主体（#8B4513 到 #654321 渐变）
  - 弧形盖子设计，更真实的海盗宝箱外观
  - 金色金属装饰边框和铰链
  - 银色锁扣和锁孔细节
- **动画效果**:
  - 弧形盖子开启动画（悬停时-25度旋转）
  - 金币飞出效果（带$符号，不同轨迹）
  - 闪光星星旋转（4秒循环）
  - 浮动粒子效果（悬停时增强）
  - 底部阴影效果增加立体感

### 交互效果
- **悬停**: 缩放 1.05 倍，背景光晕，文字变色
- **点击**: 缩放 0.95 倍
- **新功能标识**: 红色圆点，脉冲动画

## 📱 响应式布局

### 桌面端 (Header)
- 位置：顶部导航栏右侧，连接按钮左侧
- 尺寸：中等 (md)
- 布局：垂直排列（图标 + 文字）

### 移动端 (Header)
- 位置：顶部导航栏右侧
- 尺寸：小 (sm)
- 布局：垂直排列（图标 + 文字）

### 移动端 (Bottom Navigation)
- 位置：底部导航栏
- 尺寸：24px 图标
- 布局：垂直排列（图标 + 文字）
- 特色：红色新功能标识

## 🌍 多语言支持

### 翻译键值
- **中文**: `nav.mystery` → "神秘盲盒"
- **英文**: `nav.mystery` → "Mystery Box"  
- **韩文**: `nav.mystery` → "미스터리 박스"

### 翻译文件更新
- `packages/nextjs/locales/zh.json`
- `packages/nextjs/locales/en.json`
- `packages/nextjs/locales/ko.json`

## 🔧 技术实现

### 依赖库
- **Framer Motion**: 动画效果
- **React i18next**: 多语言支持
- **Lucide React**: 基础图标（如果需要）
- **HeroUI**: UI 组件库

### 组件架构
```
MysteryMenuItem
├── MysteryBoxIcon (动画宝箱图标)
├── 多语言文字标签
├── 背景光晕效果
├── 新功能标识
└── 悬浮交互效果
```

### 动画配置
```typescript
// 宝箱盖子开启动画（悬停时）
animate={isHovered ? {
  rotateX: [-15],
  y: [4],
} : {
  rotateX: [0, 5, 0],
  y: [6],
}}

// 金币飞出效果
animate={{ y: -8, opacity: [0, 1, 0] }}
transition={{ duration: 1, ease: "easeOut", delay: 0.1 }}

// 增强粒子效果（悬停时）
animate={isHovered ? {
  y: [0, -12, 0],
  x: [0, i % 2 === 0 ? 5 : -5, 0],
  opacity: [0, 1, 0],
  scale: [0.5, 1.2, 0.5],
} : {
  y: [0, -8, 0],
  opacity: [0, 1, 0],
  scale: [0.5, 1, 0.5],
}}

// 闪光强化效果
animate={isHovered ? {
  opacity: [0, 1, 0],
} : {
  opacity: [0, 0.8, 0],
}}
```

## 📍 集成位置

### 1. Header 组件
```tsx
// packages/nextjs/components/Header.tsx
import MysteryMenuItem from "./navbar/MysteryMenuItem";

// 桌面端
<MysteryMenuItem size="md" />

// 移动端  
<MysteryMenuItem size="sm" />
```

### 2. Bottom Navigation
```tsx
// packages/nextjs/components/navbar/BottomNavBar.tsx
import MysteryBoxIcon from "@/components/icons/MysteryBoxIcon";

<Link href="/mystery">
  <MysteryBoxIcon size={24} />
  <span>{t("nav.mystery")}</span>
</Link>
```

## 🎯 用户体验

### 视觉吸引力
- ✅ 彩色渐变设计，视觉冲击力强
- ✅ 多层动画效果，生动有趣
- ✅ 新功能标识，引导用户关注

### 交互体验
- ✅ 悬停反馈，提升交互感
- ✅ 点击反馈，确认操作
- ✅ 响应式设计，适配所有设备

### 可访问性
- ✅ 无论登录状态，都可访问
- ✅ 多语言支持，国际化友好
- ✅ 清晰的视觉层次，易于识别

## 🚀 使用方法

1. **访问入口**: 用户可以在以下位置找到 Mystery 入口
   - 桌面端：顶部导航栏右侧
   - 移动端：顶部导航栏右侧 + 底部导航栏

2. **点击跳转**: 点击任意 Mystery 入口都会跳转到 `/mystery` 页面

3. **视觉反馈**: 
   - 悬停时图标会放大并显示光晕效果
   - 点击时会有缩放反馈
   - 持续的动画效果吸引用户注意

## 🔮 未来扩展

- [ ] 根据用户状态动态显示不同的动画效果
- [ ] 添加音效反馈
- [ ] 根据盲盒可用状态改变图标样式
- [ ] 添加数字徽章显示可用盲盒数量
- [ ] 支持主题切换（暗色模式适配）
