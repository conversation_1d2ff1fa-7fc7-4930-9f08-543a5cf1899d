import { useCallback, useState } from "react";
import { HeaderMenuLink, MenuItemProps } from "./types";
import DepositModal from "@/components/deposit/DepositModal";
import { useWalletValidation } from "@/hooks/wallet/useWalletValidation";
import { setItem } from "@/utils";
import { <PERSON><PERSON>, Link } from "@heroui/react";
import { useTranslation } from "react-i18next";
import { useGlobalState } from "~~/services/store/store";

const menuLinks: HeaderMenuLink[] = [
  // {
  //   label: "Normal_Markets",
  //   href: "/markets/Global",
  //   icon: <Squares2X2Icon className="size-6" />,
  // },
  // {
  //   label: "Normal_Activity",
  //   href: "/activity",
  //   icon: <CursorArrowRippleIcon className="size-6" />,
  // },
  // {
  //   label: "Normal_Ranks",
  //   href: "/leaderboard",
  //   icon: <TrophyIcon className="size-6" />,
  // },
];

const MenuItem: React.FC<MenuItemProps> = ({ label, icon, href }) => {
  const { t, i18n } = useTranslation();
  const setCurrentLanguage = useGlobalState(state => state.setCurrentLanguage);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const targetMenu = { id: "2", label: "Global", slug: "Global", region: "en", forceShow: true };

  const handleClick = useCallback(() => {
    if (t(label) === "Markets") {
      setItem("currentNavItem", targetMenu);
      setItem("current_language", targetMenu.region);
      i18n.changeLanguage(targetMenu.region.toString());
      setCurrentLanguage(targetMenu.region);
    }
  }, [label, t, i18n, setCurrentLanguage, targetMenu]);

  return (
    <div className="hidden lg:flex relative">
      <Link
        href={href}
        onPress={handleClick}
        className="flex flex-col items-center py-1 px-3 gap-1 text-sm text-gray-700 cursor-pointer hover:bg-secondary hover:shadow-lg rounded-lg focus:bg-secondary active:text-neutral"
      >
        {icon}
        <span>{t(label)}</span>
      </Link>
    </div>
  );
};

const DepositButton: React.FC = () => {
  const { t } = useTranslation();
  const [isShowDepositModal, setIsShowDepositModal] = useState(false);
  const { isApproved, isValidating, isConnected, address, proxyWallet } = useWalletValidation();

  if (!isConnected || !address) {
    return null;
  }

  const getButtonText = () => {
    if (isValidating) return t("Normal_Validating");
    if (!isApproved) return t("Normal_Approval_Required");
    return t("Normal_Deposit");
  };

  const getButtonVariant = () => {
    if (!isApproved) return "bordered";
    return "solid";
  };

  return (
    <>
      <Button
        color="primary"
        variant={getButtonVariant()}
        size="md"
        radius="lg"
        className="min-w-16 md:min-w-20 font-semibold text-xs md:text-sm"
        onPress={() => setIsShowDepositModal(true)}
        isLoading={isValidating}
      >
        <span className="hidden md:inline">{getButtonText()}</span>
        <span className="md:hidden">{t("Normal_Deposit")}</span>
      </Button>

      {proxyWallet && (
        <DepositModal
          visible={isShowDepositModal}
          onClose={() => setIsShowDepositModal(false)}
          proxyWallet={proxyWallet}
        />
      )}
    </>
  );
};

export const HeaderMenuLinks: React.FC = () => {
  return (
    <div className="flex items-center gap-2">
      {menuLinks
        .filter((link): link is Required<HeaderMenuLink> => !!link.icon)
        .map((link, index) => (
          <MenuItem key={index} {...link} />
        ))}
      <DepositButton />
    </div>
  );
};
