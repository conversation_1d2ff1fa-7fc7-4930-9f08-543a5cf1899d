"use client";

import React from "react";
import { motion } from "framer-motion";

interface MysteryBoxIconProps {
  size?: number;
  className?: string;
}

const MysteryBoxIcon: React.FC<MysteryBoxIconProps> = ({ size = 24, className = "" }) => {
  const [isHovered, setIsHovered] = React.useState(false);

  return (
    <motion.div
      className={`relative inline-block ${className}`}
      style={{ width: size, height: size }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {/* 宝箱主体 */}
      <motion.svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        animate={{
          y: [0, -2, 0],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      >
        {/* 渐变定义 */}
        <defs>
          {/* 宝箱主体渐变 */}
          <linearGradient id="boxGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#8B4513" />
            <stop offset="50%" stopColor="#A0522D" />
            <stop offset="100%" stopColor="#654321" />
          </linearGradient>
          {/* 宝箱盖子渐变 */}
          <linearGradient id="lidGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#A0522D" />
            <stop offset="50%" stopColor="#CD853F" />
            <stop offset="100%" stopColor="#8B4513" />
          </linearGradient>
          {/* 金属装饰渐变 */}
          <linearGradient id="metalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FFD700" />
            <stop offset="50%" stopColor="#FFA500" />
            <stop offset="100%" stopColor="#FF8C00" />
          </linearGradient>
          {/* 锁扣渐变 */}
          <linearGradient id="lockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#C0C0C0" />
            <stop offset="50%" stopColor="#E6E6FA" />
            <stop offset="100%" stopColor="#A9A9A9" />
          </linearGradient>
        </defs>

        {/* 宝箱底部 */}
        <rect x="4" y="12" width="16" height="9" rx="1" fill="url(#boxGradient)" stroke="#654321" strokeWidth="0.5" />

        {/* 宝箱盖子 */}
        <motion.rect
          x="4"
          y="8"
          width="16"
          height="6"
          rx="1"
          fill="url(#lidGradient)"
          stroke="#654321"
          strokeWidth="0.5"
          style={{ transformOrigin: "12px 14px" }}
          animate={
            isHovered
              ? {
                  rotateX: [-15],
                }
              : {
                  rotateX: [0, 2, 0],
                }
          }
          transition={
            isHovered
              ? {
                  duration: 0.3,
                  ease: "easeOut",
                }
              : {
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }
          }
        />

        {/* 金属装饰条 */}
        <rect x="3" y="12" width="18" height="1" fill="url(#metalGradient)" />
        <rect x="3" y="20" width="18" height="1" fill="url(#metalGradient)" />

        {/* 锁扣 */}
        <rect
          x="10"
          y="10"
          width="4"
          height="4"
          rx="0.5"
          fill="url(#lockGradient)"
          stroke="#A9A9A9"
          strokeWidth="0.3"
        />

        {/* 锁孔 */}
        <circle cx="12" cy="12" r="0.8" fill="#696969" />

        {/* 木纹装饰 */}
        <rect x="4" y="15" width="16" height="0.5" fill="#654321" opacity="0.6" />
        <rect x="4" y="18" width="16" height="0.5" fill="#654321" opacity="0.6" />

        {/* 金币效果 - 只在悬停时显示 */}
        {isHovered && (
          <motion.g
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            {/* 金币1 */}
            <motion.circle
              cx="8"
              cy="12"
              r="1.2"
              fill="#FFD700"
              stroke="#FFA500"
              strokeWidth="0.2"
              initial={{ y: 0, opacity: 0 }}
              animate={{ y: -8, opacity: [0, 1, 0] }}
              transition={{ duration: 1, ease: "easeOut", delay: 0.1 }}
            />
            {/* 金币2 */}
            <motion.circle
              cx="12"
              cy="12"
              r="1"
              fill="#FFD700"
              stroke="#FFA500"
              strokeWidth="0.2"
              initial={{ y: 0, opacity: 0 }}
              animate={{ y: -10, opacity: [0, 1, 0] }}
              transition={{ duration: 1.2, ease: "easeOut", delay: 0.2 }}
            />
            {/* 金币3 */}
            <motion.circle
              cx="16"
              cy="12"
              r="0.8"
              fill="#FFD700"
              stroke="#FFA500"
              strokeWidth="0.2"
              initial={{ y: 0, opacity: 0 }}
              animate={{ y: -6, opacity: [0, 1, 0] }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
            />
          </motion.g>
        )}
      </motion.svg>

      {/* 闪光效果 */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        animate={
          isHovered
            ? {
                opacity: [0, 1, 0],
              }
            : {
                opacity: [0, 0.8, 0],
              }
        }
        transition={
          isHovered
            ? {
                duration: 0.5,
                repeat: 2,
                ease: "easeInOut",
              }
            : {
                duration: 2.5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1,
              }
        }
      >
        <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          {/* 闪光星星 */}
          <motion.g
            animate={{
              rotate: [0, 360],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "linear",
            }}
          >
            <path d="M12 2L13.5 8.5L20 10L13.5 11.5L12 18L10.5 11.5L4 10L10.5 8.5L12 2Z" fill="#FFD700" opacity="0.8" />
          </motion.g>
        </svg>
      </motion.div>

      {/* 浮动粒子效果 */}
      {[...Array(isHovered ? 6 : 3)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 h-1 bg-yellow-400 rounded-full pointer-events-none"
          style={{
            left: `${20 + (i % 3) * 15}%`,
            top: `${30 + (i % 3) * 10}%`,
          }}
          animate={
            isHovered
              ? {
                  y: [0, -12, 0],
                  x: [0, i % 2 === 0 ? 5 : -5, 0],
                  opacity: [0, 1, 0],
                  scale: [0.5, 1.2, 0.5],
                }
              : {
                  y: [0, -8, 0],
                  opacity: [0, 1, 0],
                  scale: [0.5, 1, 0.5],
                }
          }
          transition={{
            duration: isHovered ? 1 : 2,
            repeat: Infinity,
            ease: "easeInOut",
            delay: i * 0.2,
          }}
        />
      ))}
    </motion.div>
  );
};

export default MysteryBoxIcon;
