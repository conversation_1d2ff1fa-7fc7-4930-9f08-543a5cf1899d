import React, { useEffect } from "react";
// import Image from "next/image";
import { getEventsListData } from "@/api/markets";
import useInfiniteScroll from "@/components/other/LoadMore";
// import { formatLargeAmountWithCommas, formatLastTradePrice, formatOutcomePrice } from "@/utils";
import { Divider } from "@heroui/divider";

// import { Progress } from "@heroui/react";
// import { BeatLoader } from "react-spinners";

// interface Market {
//   icon: string;
//   groupItemTitle: string;
//   lastTradePrice: number;
//   outcomePrices: string;
// }

// interface EventCardData {
//   image: string;
//   title: string;
//   markets: Market[];
//   volume: number;
// }

// interface ProgressBarProps {
//   value: number;
// }

// const ProgressBar: React.FC<ProgressBarProps> = ({ value }) => {
//   return (
//     <div className="relative flex-center mt-0.5 w-28">
//       <Progress
//         size="lg"
//         radius="none"
//         classNames={{
//           track: "bg-blue-50 rounded-md overflow-hidden !important",
//           indicator: "bg-blue-100",
//         }}
//         value={value}
//       />
//       <div className="absolute text-blue-600 font-bold">{value + "%"}</div>
//     </div>
//   );
// };

// interface MarketItemProps {
//   market: Market;
// }

// const MarketItem: React.FC<MarketItemProps> = ({ market }) => (
//   <div className="flex items-center justify-end relative">
//     <Image className="h-4 w-4 object-cover rounded-full" src={market.icon} alt="" width={16} height={16} />
//     <span className="text-sm ml-2 whitespace-nowrap">{market.groupItemTitle}</span>
//   </div>
// );

// interface MarketsTextListItemProps {
//   eventCardData: EventCardData;
// }

// const MarketsTextListItem: React.FC<MarketsTextListItemProps> = ({ eventCardData }) => {
//   const { markets, volume } = eventCardData;
//   const outcomePrice = formatOutcomePrice(markets[0]?.outcomePrices);
//   const lastTradePriceOne = formatLastTradePrice(markets[0]?.lastTradePrice || 0);
//   const lastTradePriceTwo = formatLastTradePrice(markets[1]?.lastTradePrice || 0);
//   const totalBet = formatLargeAmountWithCommas(volume);

//   return (
//     <div className="w-full flex items-center justify-between">
//       <div className="flex w-[60%] items-center">
//         <Image
//           className="h-12 w-12 object-cover rounded-md border-purple-600 border-1 p-0.5"
//           src={eventCardData.image}
//           alt=""
//           width={48}
//           height={48}
//         />
//         <span className="text-sm font-semibold ml-4">{eventCardData.title}</span>
//       </div>
//       <div className="flex items-center w-[20%]">
//         {markets.length > 1 ? (
//           <div className="flex flex-col w-20">
//             {markets.slice(0, 2).map((market, index) => (
//               <MarketItem key={index} market={market} />
//             ))}
//           </div>
//         ) : (
//           <div className="w-20" />
//         )}
//         <div className="flex flex-col w-20 ml-4">
//           {markets.length > 1 ? (
//             <>
//               <ProgressBar value={lastTradePriceOne} />
//               <ProgressBar value={lastTradePriceTwo} />
//             </>
//           ) : (
//             <ProgressBar value={outcomePrice} />
//           )}
//         </div>
//       </div>

//       <div className="w-[20%]">{"$" + totalBet}</div>
//     </div>
//   );
// };

interface MarketsTextListProps {
  eventParams: any;
}

const MarketsTextList: React.FC<MarketsTextListProps> = props => {
  const { eventParams } = props;

  const {
    // data: eventCardListData,
    // hasMore,
    // loadMoreRef,
    setQueryParams,
    setHasMore,
  } = useInfiniteScroll(eventParams, getEventsListData);

  useEffect(() => {
    // 重置分页和数据
    setQueryParams({
      ...eventParams,
    });
    setHasMore(true);
  }, [setQueryParams, setHasMore, eventParams]);

  return (
    <div className="w-full flex flex-col p-1">
      <div className="flex w-full items-center justify-between text-xs text-gray-400 font-medium my-2">
        <span className="w-[60%]">MARKET</span>
        <span className="w-[20%]">% CHANCE</span>
        <span className="w-[20%]">TOTAL BET</span>
      </div>
      <Divider className="my-2 bg-gray-200" />
      {/* {eventCardListData.map((item, index) => (
        <React.Fragment key={index}>
          <MarketsTextListItem eventCardData={item} />
          <Divider className="my-2 bg-gray-200" />
        </React.Fragment>
      ))} */}
      {/* <div ref={loadMoreRef} className="flex-center w-full h-10 mt-6 text-base text-gray-400 font-normal">
        {hasMore ? <BeatLoader size={12} color={"#9ca3af"} /> : "End of results"}
      </div> */}
    </div>
  );
};

export default MarketsTextList;
