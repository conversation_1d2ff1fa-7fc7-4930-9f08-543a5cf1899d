import React from "react";
// import { sortEventsListData } from "@/api/markets";
import EventCard from "@/components/events-components/EventCard";
import { getCurrentTimestamp } from "@/utils";
// import useInfiniteScroll from "@/components/other/LoadMore";
import { BeatLoader } from "react-spinners";

const MarketsCardList = (props: any) => {
  const { EventCardListData, hasMore, loadMoreRef } = props;
  const timeStamp = getCurrentTimestamp();

  return (
    <div className="w-full flex flex-col items-center justify-center">
      {EventCardListData?.length > 0 && (
        <div className="w-full gap-3 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {EventCardListData?.map((data: any, index: any) => (
            <EventCard key={index} eventCardData={data} timeStamp={timeStamp} />
          ))}
        </div>
      )}
      <div ref={loadMoreRef} className="h-10 mt-6 text-base text-gray-400 font-normal">
        {hasMore ? <BeatLoader size={12} color={"#9ca3af"} /> : "End of results"}
      </div>
    </div>
  );
};

export default MarketsCardList;
