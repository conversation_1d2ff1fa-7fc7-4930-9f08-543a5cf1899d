"use client";

import { useState } from "react";
import { CollapsibleSection } from "@/components/shared/collapsible-section";
import { Radio, RadioGroup } from "@heroui/react";
import { useTranslation } from "react-i18next";

const statusOptions = [
  { label: "Markets_Status_All", value: "all" },
  { label: "Markets_Status_Active", value: "active" },
  { label: "Markets_Status_Resolved", value: "resolved" },
];

export function StatusFilter(props: any) {
  const { eventParams, setEventParams, setCurrentStatus } = props;
  const { t } = useTranslation();

  const [selectedValue, setSelectedValue] = useState("active");
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSelectedValue(value);
    setEventParams({
      ...eventParams,
      closed: value === "all" ? null : value === "active" ? false : true,
    });
    if (typeof setCurrentStatus === "function") {
      setCurrentStatus(value);
    }
  };

  return (
    <div className="space-y-4">
      <CollapsibleSection title="Status">
        <RadioGroup value={selectedValue} onChange={handleChange} className="gap-2 py-4 px-2">
          {statusOptions.map(option => {
            const id = `status-radio-${option.value}`;
            return (
              <label
                key={option.value}
                htmlFor={id}
                className={`flex h-9 items-center justify-between pl-2 cursor-pointer text-sm ${
                  selectedValue === option.value ? "bg-gray-100" : ""
                }`}
              >
                <span>{t(option.label)}</span>
                <Radio id={id} value={option.value} />
              </label>
            );
          })}
        </RadioGroup>
      </CollapsibleSection>
    </div>
  );
}
