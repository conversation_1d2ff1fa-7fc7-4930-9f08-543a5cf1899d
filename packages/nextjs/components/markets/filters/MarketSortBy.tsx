"use client";

import React from "react";
import { CollapsibleSection } from "@/components/shared/collapsible-section";
import { Button } from "@heroui/react";
import { ChartColumn, Crosshair, Droplet, Hourglass, Swords, TrendingUp } from "lucide-react";
import { useTranslation } from "react-i18next";

const sortOptions = [
  { key: "trending", label: "Markets_Sort_Trending", icon: TrendingUp, order: "volume_24hr" },
  { key: "liquidity", label: "Markets_Sort_Liquidity", icon: Droplet, order: "liquidity" },
  { key: "volume", label: "Markets_Sort_Volume", icon: ChartColumn, order: "volume" },
  { key: "newest", label: "Markets_Sort_Newest", icon: Crosshair, order: "start_date" },
  { key: "ending-soon", label: "Markets_Sort_EndingSoon", icon: Hourglass, order: "end_date" },
  { key: "competitive", label: "Markets_Sort_Competitive", icon: Swords, order: "competitive" },
];

export function MarketSortBy(props: any) {
  const { eventParams, setEventParams } = props;
  const { t } = useTranslation();

  const handleButtonClick = (option: any) => {
    setEventParams({
      ...eventParams,
      order: option.order,
    });
  };

  return (
    <div className="space-y-4">
      <CollapsibleSection title="Sort by" defaultOpen={true}>
        <div className="grid grid-cols-2 gap-2 pl-1">
          {sortOptions.map((option, index) => (
            <Button
              key={index}
              size="lg"
              variant="ghost"
              className={`flex w-[142px] items-center justify-start rounded-lg px-2 border ${
                eventParams.order === option.order ? "border-blue-500" : ""
              }`}
              startContent={
                <div className="flex-center size-7 bg-blue-50 rounded-md">
                  <option.icon className="size-4" />
                </div>
              }
              onClick={() => handleButtonClick(option)}
            >
              <span className="text-sm">{t(option.label)}</span>
            </Button>
          ))}
        </div>
      </CollapsibleSection>
    </div>
  );
}
