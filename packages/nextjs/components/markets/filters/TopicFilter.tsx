import { CollapsibleSection } from "@/components/shared/collapsible-section";

// import { TrendingUp } from "lucide-react";

export function TopicFilter(props: any) {
  const { tagListData, eventParams, setEventParams } = props;

  const handleTopicClick = (id: number) => {
    setEventParams({
      ...eventParams,
      tag_id: id,
    });
  };

  return (
    <div className="space-y-4">
      <CollapsibleSection title="Topics" defaultOpen={true}>
        <ul className="space-y-2 py-4 pl-2">
          {tagListData?.map((tag: any) => (
            <li
              key={tag.id}
              className={`flex cursor-pointer items-center rounded-md p-2 text-sm transition-colors ${
                eventParams.tag_id === tag.id
                  ? "bg-gray-100 dark:bg-gray-500 dark:text-white"
                  : "text-muted-foreground hover:bg-gray-100 dark:hover:bg-gray-500 dark:hover:text-white"
              }`}
              onClick={() => handleTopicClick(tag.id)}
            >
              {/* {index === 0 && <TrendingUp className="mr-2 size-4" />} */}
              <span className="text-gray-500">{tag.label}</span>
            </li>
          ))}
        </ul>
      </CollapsibleSection>
    </div>
  );
}
