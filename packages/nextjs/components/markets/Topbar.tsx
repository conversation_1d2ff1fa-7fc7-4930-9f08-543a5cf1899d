import React, { <PERSON><PERSON><PERSON>, useEffect, useState } from "react";
import { Button, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from "@heroui/react";
import {
  ChartColumn,
  ChevronDown,
  Crosshair,
  Droplet, // Grip,
  Hourglass, // List,
  ListFilter, // Search, // Star,
  Swords,
  TrendingUp,
} from "lucide-react";
import { useTranslation } from "react-i18next";

interface TopbarProps {
  setCurrentListType: Dispatch<"grid" | "list">;
  isShowSideBar: boolean;
  setIsShowSideBar: Dispatch<boolean>;
  eventParams: any;
  setEventParams: any;
}

interface DropdownMenuItem {
  key: string;
  label: string;
  icon: any;
  order: string;
}

interface DropdownMenuItemBoxProps {
  item: DropdownMenuItem;
  t: any;
  className?: string;
}

const DropdownMenuList: DropdownMenuItem[] = [
  { key: "trending", label: "Markets_Sort_Trending", icon: TrendingUp, order: "volume_24hr" },
  { key: "liquidity", label: "Markets_Sort_Liquidity", icon: Droplet, order: "liquidity" },
  { key: "volume", label: "Markets_Sort_Volume", icon: ChartColumn, order: "volume" },
  { key: "newest", label: "Markets_Sort_Newest", icon: Crosshair, order: "start_date" },
  { key: "ending-soon", label: "Markets_Sort_EndingSoon", icon: Hourglass, order: "end_date" },
  { key: "competitive", label: "Markets_Sort_Competitive", icon: Swords, order: "competitive" },
];

const DropdownMenuItemBox: React.FC<DropdownMenuItemBoxProps> = ({ item, t }) => (
  <div className="flex items-center">
    <div className="flex-center size-7 bg-blue-50 rounded-md">
      <item.icon className="size-4" />
    </div>
    <span className="ml-2 font-medium">{t(item.label)}</span>
  </div>
);

export default function Topbar(props: TopbarProps) {
  const { isShowSideBar, setIsShowSideBar, eventParams, setEventParams } = props;
  const { t } = useTranslation();
  const [currentDropdown, setCurrentDropdown] = useState(DropdownMenuList[0]);

  useEffect(() => {
    const matchedItem = DropdownMenuList.find(item => item.order === eventParams.order);
    if (matchedItem) {
      setCurrentDropdown(matchedItem);
    }
  }, [eventParams]);

  const onClickDropdown = (item: any) => {
    setCurrentDropdown(item);
    setEventParams({
      ...eventParams,
      order: item.order,
    });
  };

  // const [selectedView, setSelectedView] = useState<"grid" | "list">("grid");
  // const handleViewChange = (view: "grid" | "list") => {
  //   setSelectedView(view);
  //   setCurrentListType(view);
  //   setEventParams({
  //     ...eventParams,
  //     offset: 0,
  //   });
  // };

  return (
    <div className="flex w-full items-center justify-between gap-2 p-1 mt-2">
      <div className="flex flex-1 items-center gap-2">
        <div className="hidden sm:block">
          <Button
            isIconOnly
            size="lg"
            variant="ghost"
            className="border"
            onClick={() => {
              setIsShowSideBar(!isShowSideBar);
            }}
          >
            <ListFilter className="size-5" />
          </Button>
        </div>

        {/* <Input
          radius="md"
          variant={"flat"}
          size="lg"
          placeholder="Search by market"
          startContent={<Search className="text-muted-foreground size-4" />}
          className="border rounded-xl"
          classNames={{
            inputWrapper: ["bg-transparent"],
          }}
          type="search"
        /> */}
      </div>

      <div className="flex items-center gap-2">
        <Dropdown>
          <DropdownTrigger className="w-[208px]">
            <Button
              variant="ghost"
              size="lg"
              className="flex justify-between px-2 border"
              endContent={<ChevronDown className="size-5" />}
            >
              <DropdownMenuItemBox item={currentDropdown} t={t} />
            </Button>
          </DropdownTrigger>
          <DropdownMenu>
            {DropdownMenuList.map((item, index) => (
              <DropdownItem key={index} onClick={() => onClickDropdown(item)}>
                <DropdownMenuItemBox item={item} t={t} />
              </DropdownItem>
            ))}
          </DropdownMenu>
        </Dropdown>

        {/* <ButtonGroup variant="bordered" size="lg">
          <Button
            isIconOnly
            className={`text-muted-foreground border ${selectedView === "grid" ? "bg-light" : "bg-gray-200"}`}
            onClick={() => handleViewChange("grid")}
          >
            <Grip className="size-5" />
          </Button>
          <Button
            isIconOnly
            className={`text-muted-foreground border ${selectedView === "list" ? "bg-light" : "bg-gray-200"}`}
            onClick={() => handleViewChange("list")}
          >
            <List className="size-5" />
          </Button>
        </ButtonGroup> */}

        {/* <Button isIconOnly size="lg" className="border" variant="ghost">
          <Star className="size-5" />
        </Button> */}
      </div>
    </div>
  );
}
