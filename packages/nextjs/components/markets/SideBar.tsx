import React from "react";
import { MarketSortBy } from "./filters/MarketSortBy";
import { StatusFilter } from "./filters/StatusFilter";
import { TopicFilter } from "./filters/TopicFilter";

export default function SideBar(props: any) {
  const { isShowSideBar, tagListData, setCurrentStatus, eventParams, setEventParams } = props;
  return (
    <div
      className={`hidden md:block transition-transform duration-300 ${
        isShowSideBar ? "translate-x-0 w-[300px]" : "-translate-x-full w-0"
      } w-[300px] overflow-y-scroll scrollbar-hide`}
    >
      <div className="flex flex-col space-y-2 p-1">
        <MarketSortBy eventParams={eventParams} setEventParams={setEventParams} />
        <StatusFilter eventParams={eventParams} setEventParams={setEventParams} setCurrentStatus={setCurrentStatus} />
        <TopicFilter tagListData={tagListData} eventParams={eventParams} setEventParams={setEventParams} />
      </div>
    </div>
  );
}
