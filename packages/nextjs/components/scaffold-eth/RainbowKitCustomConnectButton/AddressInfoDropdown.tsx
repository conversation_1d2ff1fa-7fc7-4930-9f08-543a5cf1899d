"use client";

import { useRef, useState } from "react";
import { useLoginActions } from "@/hooks/useLoginActions";
import { getAddress } from "viem";
import { Address } from "viem";
import { ArrowTopRightOnSquareIcon, ChevronDownIcon, QrCodeIcon } from "@heroicons/react/24/outline";
import { ArrowLeftEndOnRectangleIcon } from "@heroicons/react/24/outline";
import { BlockieAvatar } from "~~/components/scaffold-eth";
import { useOutsideClick } from "~~/hooks/scaffold-eth";

type AddressInfoDropdownProps = {
  address: Address;
  blockExplorerAddressLink: string | undefined;
  ensAvatar?: string;
};

export const AddressInfoDropdown = ({ address, ensAvatar, blockExplorerAddressLink }: AddressInfoDropdownProps) => {
  const { logout } = useLoginActions();

  const checkSumAddress = address && address !== "" ? getAddress(address) : "";

  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [selectingNetwork, setSelectingNetwork] = useState(false);

  const dropdownRef = useRef<HTMLDetailsElement>(null);

  const closeDropdown = () => {
    setSelectingNetwork(false);
    dropdownRef.current?.removeAttribute("open");
  };

  useOutsideClick(dropdownRef, closeDropdown);

  const handleDisConnect = async () => {
    setIsDisconnecting(true);
    try {
      await logout();
    } catch (error) {
      console.error("Disconnect failed:", error);
    } finally {
      setIsDisconnecting(false);
      closeDropdown();
    }
  };

  if (!address || address === "") {
    return null;
  }

  return (
    <>
      <details ref={dropdownRef} className="dropdown dropdown-end leading-3 z-20">
        <summary tabIndex={0} className="btn btn-sm pl-0 pr-2 shadow-md dropdown-toggle gap-0 !h-auto">
          <BlockieAvatar address={checkSumAddress} size={30} ensImage={ensAvatar} />
          <ChevronDownIcon className="h-6 w-4 ml-2" />
        </summary>
        <ul
          tabIndex={0}
          className="dropdown-content menu z-[2] p-2 mt-2 shadow-center shadow-accent bg-base-200 rounded-box gap-1"
        >
          <li className={selectingNetwork ? "hidden" : ""}>
            <label htmlFor="qrcode-modal" className="btn-sm !rounded-xl flex gap-3 py-3">
              <QrCodeIcon className="h-6 w-4 ml-2 sm:ml-0" />
              <span className="whitespace-nowrap">View QR Code</span>
            </label>
          </li>

          <li className={selectingNetwork ? "hidden" : ""}>
            <button className="menu-item btn-sm !rounded-xl flex gap-3 py-3" type="button">
              <ArrowTopRightOnSquareIcon className="h-6 w-4 ml-2 sm:ml-0" />
              <a
                target="_blank"
                href={blockExplorerAddressLink}
                rel="noopener noreferrer"
                className="whitespace-nowrap"
              >
                View on Block Explorer
              </a>
            </button>
          </li>

          <li className={selectingNetwork ? "hidden" : ""}>
            <button
              className="menu-item text-error btn-sm !rounded-xl flex gap-3 py-3"
              type="button"
              onClick={handleDisConnect}
              disabled={isDisconnecting}
            >
              {isDisconnecting ? (
                <>
                  <div className="loading loading-spinner loading-xs ml-2 sm:ml-0"></div>
                  <span>Disconnecting...</span>
                </>
              ) : (
                <>
                  <ArrowLeftEndOnRectangleIcon className="h-6 w-4 ml-2 sm:ml-0" />
                  <span>Disconnect</span>
                </>
              )}
            </button>
          </li>
        </ul>
      </details>
    </>
  );
};
