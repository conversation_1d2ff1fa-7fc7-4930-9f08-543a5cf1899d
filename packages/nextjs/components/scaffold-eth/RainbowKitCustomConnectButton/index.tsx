"use client";

import { useEffect, useState } from "react";
import { Balance } from "../Balance";
import { AddressInfoDropdown } from "./AddressInfoDropdown";
import { AddressQRCodeModal } from "./AddressQRCodeModal";
import { WrongNetworkDropdown } from "./WrongNetworkDropdown";
import CustomSignInModal from "@/components/auth/CustomSignInModal";
import CustomConnectModal from "@/components/login/CustomConnectModal";
import computeProxyAddress from "@/contracts/computeProxyAddress";
import { useGlobalBalance } from "@/hooks/useGlobalBalance";
import { useLoginModal } from "@/hooks/useLoginModal";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useMagicStore } from "@/services/store/magicStore";
import { WalletType, useGlobalState } from "@/services/store/store";
import { getItem, setItem } from "@/utils";
import { orderEventManager } from "@/utils/events/orderEvents";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { ConnectButton } from "@rainbow-me/rainbowkit";
import { useTranslation } from "react-i18next";
import { Address } from "viem";
import { useAccount } from "wagmi";
import { CurrencyDollarIcon } from "@heroicons/react/24/solid";
import { useTargetNetwork } from "~~/hooks/scaffold-eth/useTargetNetwork";
import { getBlockExplorerAddressLink } from "~~/utils/scaffold-eth";

export const RainbowKitCustomConnectButton = () => {
  const { t } = useTranslation();
  const { address: wagmiAddress } = useAccount();
  const { address: userAddress, isConnected } = useUserAddress();
  const { targetNetwork } = useTargetNetwork();
  const { setWalletType } = useGlobalState();

  const { isLoginModalOpen, openLoginModal, closeLoginModal } = useLoginModal();

  const { magicUser, isLoggedIn: isMagicLoggedIn, initializeMagic, isInitialized } = useMagicStore();

  const [proxyWallet, setProxyWallet] = useState("");
  const [isShowSignatureModal, setIsShowSignatureModal] = useState(false);
  // RainbowKit 作为主组件，负责余额的获取和监听
  const { balance, isLoading, refreshBalance } = useGlobalBalance(proxyWallet, {
    enableBlockWatch: true, // 🎯 主组件启用区块监听
    enableAutoRefresh: true, // 🎯 主组件启用定时刷新
    refreshInterval: 30000, // 30秒刷新间隔
    skipInitialFetch: false, // 立即获取数据
  });

  // 初始化 Magic
  useEffect(() => {
    if (!isInitialized) {
      initializeMagic();
    }
  }, [isInitialized, initializeMagic]);

  const handleCloseModal = () => {
    setIsShowSignatureModal(false);
  };

  const getProxy = async () => {
    if (userAddress) {
      const proxyWallet = await computeProxyAddress(userAddress);
      if (proxyWallet) {
        setProxyWallet(proxyWallet);
        setItem(`login_proxyWallet`, proxyWallet);
      }
    }
  };

  useEffect(() => {
    if (wagmiAddress && !magicUser) {
      setWalletType(WalletType.METAMASK);
    } else if (isMagicLoggedIn && magicUser) {
      setWalletType(WalletType.MAGIC);
    } else {
      setWalletType(WalletType.NONE);
    }
  }, [wagmiAddress, magicUser, isMagicLoggedIn, setWalletType]);

  useEffect(() => {
    if (userAddress) {
      const localProxyWallet = getItem(`login_proxyWallet`);
      if (localProxyWallet) {
        setProxyWallet(localProxyWallet);
      } else {
        getProxy();
      }
    } else {
      setProxyWallet("");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userAddress]);

  useEffect(() => {
    const judgeSignatureStatus = async () => {
      if (userAddress) {
        const signatureStatus = await getItem(`login_signed_${userAddress}`);

        if (isMagicLoggedIn && magicUser) {
          if (!signatureStatus) {
            setItem(`login_signed_${userAddress}`, true);
          }
          return;
        }

        // 只有 wagmi 用户需要签名模态框
        if (wagmiAddress && !signatureStatus) {
          setIsShowSignatureModal(true);
        }
      }
    };

    judgeSignatureStatus();
  }, [userAddress, magicUser, isMagicLoggedIn, wagmiAddress]);

  // RainbowKit 作为主组件，智能监听订单成功事件
  useEffect(() => {
    if (!proxyWallet) return;

    const removeListener = orderEventManager.addEventListener("ORDER_SUCCESS", async () => {
      try {
        setTimeout(async () => {
          try {
            await refreshBalance(true);
          } catch (error) {
            console.error(`❌ RainbowKit: Delayed balance refresh failed:`, error);
          }
        }, 1000); // 延迟1秒，确保 OrderCard 的刷新已完成
      } catch (error) {
        console.error(`❌ RainbowKit: Event handling failed:`, error);
      }
    });

    return () => {
      removeListener();
    };
  }, [proxyWallet, refreshBalance]);

  return (
    <div>
      <CustomSignInModal
        onClose={handleCloseModal}
        visible={isShowSignatureModal}
        setIsShowSignatureModal={setIsShowSignatureModal}
      />

      <CustomConnectModal isOpen={isLoginModalOpen} onClose={closeLoginModal} />

      <ConnectButton.Custom>
        {({ account, chain, mounted }) => {
          const wagmiConnected = mounted && account && chain;

          const blockExplorerAddressLink = proxyWallet
            ? getBlockExplorerAddressLink(targetNetwork, proxyWallet)
            : undefined;

          return (
            <>
              {(() => {
                if (!isConnected) {
                  return (
                    <div className="flex flex-row gap-2 w-full sm:w-auto">
                      <Button
                        className="bg-blue-600 border text-white hover:bg-blue-500 shadow-sm w-full sm:w-auto text-sm sm:text-base px-2 sm:px-4"
                        size="md"
                        radius="md"
                        onPress={openLoginModal}
                        type="button"
                      >
                        <span className="truncate">{t("Normal_Login")}</span>
                      </Button>
                    </div>
                  );
                }

                if (wagmiConnected && (chain?.unsupported || chain?.id !== targetNetwork.id)) {
                  return (
                    <div className="w-full sm:w-auto">
                      <WrongNetworkDropdown />
                    </div>
                  );
                }

                // 统一的已登录状态显示（Magic 或 wagmi）
                return (
                  <div className="flex items-center justify-between sm:justify-start text-sm text-gray-600 w-full sm:w-auto">
                    <div className="hidden sm:flex items-center">
                      <Link
                        href="/portfolio"
                        className="flex flex-col items-center py-1 px-3 gap-1 text-sm text-gray-700 cursor-pointer hover:bg-secondary hover:shadow-lg rounded-lg focus:bg-secondary active:bg-secondary active:shadow-none active:text-neutral"
                      >
                        <CurrencyDollarIcon color="#43A047" className="w-6 h-6" />
                        <span>{t("Normal_Portfolio")}</span>
                      </Link>

                      <Link
                        href="/wallet"
                        className="flex flex-col items-center py-1 px-3 gap-1 mr-1 text-sm text-gray-700 cursor-pointer hover:bg-secondary hover:shadow-lg rounded-lg focus:bg-secondary active:bg-secondary active:shadow-none active:text-neutral"
                      >
                        <Balance balance={balance} isLoading={isLoading} />
                        <span>{t("Normal_Balance")}</span>
                      </Link>

                      <AddressInfoDropdown
                        address={proxyWallet as Address}
                        ensAvatar={account?.ensAvatar}
                        blockExplorerAddressLink={blockExplorerAddressLink}
                      />
                    </div>

                    <AddressQRCodeModal address={proxyWallet as Address} modalId="qrcode-modal" />
                  </div>
                );
              })()}
            </>
          );
        }}
      </ConnectButton.Custom>
    </div>
  );
};
