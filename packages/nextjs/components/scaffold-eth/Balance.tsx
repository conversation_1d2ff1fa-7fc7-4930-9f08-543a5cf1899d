"use client";

type BalanceProps = {
  balance: string | null;
  isLoading: boolean;
  className?: string;
};

const Balance = ({ balance, isLoading, className }: BalanceProps) => {
  if (isLoading || balance === null) {
    return (
      <div className="animate-pulse flex space-x-2">
        <div className="flex items-center">
          <div className="h-6 w-12 bg-slate-400 rounded-xl"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex-center text-medium font-semibold text-green-600 ${className}`}>
      <span className="font-bold">$</span>
      <span>{balance}</span>
    </div>
  );
};

export { Balance };
