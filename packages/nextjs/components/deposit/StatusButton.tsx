import { Button, Tooltip } from "@heroui/react";
import { Check } from "lucide-react";

const StatusButton = ({
  state,
  showTooltip,
  onClick,
  successMessage,
  buttonText,
  isDisabled,
}: {
  state: string;
  showTooltip: boolean;
  onClick: () => void;
  successMessage: string;
  buttonText: string;
  isDisabled?: boolean;
}) =>
  state === "success" ? (
    <div className="flex-center gpa-1 text-blue-600 text-medium font-semibold">
      <Check strokeWidth={5} className="size-4 mr-1" />
      {successMessage}
    </div>
  ) : (
    <Tooltip content="Please disconnect and connect with the correct wallet" color="danger" isOpen={showTooltip}>
      <Button
        className="bg-blue-600 text-white"
        isLoading={state === "loading"}
        isDisabled={isDisabled}
        onPress={onClick}
      >
        {buttonText}
      </Button>
    </Tooltip>
  );

export { StatusButton };
