import { Modal, ModalBody, <PERSON>dalContent, ModalHeader } from "@heroui/modal";
import { QRCodeSVG } from "qrcode.react";
import { useTranslation } from "react-i18next";

const WalletAddressModal = (props: any) => {
  const { visible, onClose, proxyWallet } = props;
  const { t } = useTranslation();

  return (
    <Modal size="sm" isOpen={visible} onClose={onClose}>
      <ModalContent>
        <ModalHeader className="w-full flex justify-center">
          <div className="font-bold text-xl">{t("Wallet_Send_Modal_Title")}</div>
        </ModalHeader>
        <ModalBody>
          <QRCodeSVG className="w-full flex-center mt-4 mb-8" value={proxyWallet} size={256} />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default WalletAddressModal;
