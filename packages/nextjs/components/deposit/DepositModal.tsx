import { useCallback, useEffect, useState } from "react";
import { StatusButton } from "./StatusButton";
import WalletAddressModal from "./WalletAddressModal";
import { checkAllowance, depositConstruct, getUSDCBalance, increaseAllowance } from "@/contracts/depositContract";
import { onClickExecTransaction } from "@/contracts/encodeMultiSendTransaction";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useWalletValidation } from "@/hooks/wallet/useWalletValidation";
import { useGlobalState } from "@/services/store/store";
import { Popover, PopoverContent, PopoverTrigger } from "@heroui/popover";
import { Button, Divider, Input, Modal, ModalBody, ModalContent, ModalHeader, addToast } from "@heroui/react";
import { parseUnits } from "ethers";
import { BadgeAlert, CircleCheck, ClockAlert, QrCode, RefreshCcw } from "lucide-react";
import { useSession } from "next-auth/react";
import { useTranslation } from "react-i18next";
import { NumericFormat as NumberFormat } from "react-number-format";
import { CheckCircleIcon, DocumentDuplicateIcon } from "@heroicons/react/24/outline";

interface DepositModalProps {
  proxyWallet: string;
  visible: boolean;
  onClose: () => void;
}

const CopyButton = ({
  isCopied,
  onCopy,
  proxyWallet,
}: {
  isCopied: boolean;
  onCopy: () => void;
  proxyWallet: string;
}) => {
  const handleCopy = async () => {
    await navigator.clipboard.writeText(proxyWallet);
    onCopy();
    addToast({
      title: "Address Copied",
      timeout: 1200,
      shouldShowTimeoutProgess: true,
      color: "success",
    });
  };

  return (
    <div
      className="bg-black text-white rounded-full flex items-center cursor-pointer gap-1 px-2 py-1"
      onClick={handleCopy}
    >
      {isCopied ? (
        <CheckCircleIcon className="size-4" aria-hidden="true" />
      ) : (
        <DocumentDuplicateIcon className="size-4" aria-hidden="true" />
      )}
    </div>
  );
};

const FailedDeposit = ({ handleRetry }: { handleRetry: () => void }) => {
  const { t } = useTranslation();

  return (
    <ModalContent>
      <ModalHeader className="flex-center gap-4">
        <div className="py-2">{t("Deposit_Modal_Deposit_Failed")}</div>
      </ModalHeader>
      <ModalBody className="px-8 pb-8">
        <div className="w-full flex-center flex-col gap-4">
          <ClockAlert className="size-16" />
          <div className="text-medium text-gray-500">{t("Deposit_Modal_Deposit_Error_Described")}</div>
          <Button className="w-full" size="lg" color="primary" radius="sm" onPress={handleRetry}>
            {t("Normal_Back")}
          </Button>
        </div>
      </ModalBody>
    </ModalContent>
  );
};

const CompletedDeposit = ({
  inputValue,
  onClose,
  handleRetry,
  transactionHash,
}: {
  inputValue: string;
  onClose: () => void;
  handleRetry: () => void;
  transactionHash: string;
}) => {
  const { t } = useTranslation();
  const targetUrl = `${process.env.NEXT_PUBLIC_BASESCAN}/${transactionHash}`;

  return (
    <ModalContent>
      <ModalHeader className="flex-center gap-4">
        <div className="py-2">{t("Deposit_Modal_Deposit_Completed")}</div>
      </ModalHeader>
      <ModalBody className="px-8 pb-8">
        <div className="w-full flex-center flex-col gap-4 text-medium">
          <CircleCheck className="size-16" color="#27AE60" />
          <div className="text-gray-500">{`${t("Deposit_Modal_Your_Funds")} ($${inputValue}) ${t(
            "Deposit_Modal_Were_Success",
          )}`}</div>
          <a href={targetUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500">
            View on Basescan
          </a>
          <Button
            className="w-full"
            size="lg"
            color="primary"
            radius="sm"
            onPress={() => {
              onClose();
              handleRetry();
            }}
          >
            {t("Normal_Done")}
          </Button>
        </div>
      </ModalBody>
    </ModalContent>
  );
};

const AmountInput = ({ value, onChange }: { value: string; onChange: (value: string) => void }) => {
  return (
    <NumberFormat
      value={value}
      thousandSeparator={true}
      decimalScale={4}
      fixedDecimalScale={false}
      allowNegative={false}
      prefix={"$"}
      onValueChange={values => {
        onChange(values.value);
      }}
      className="border rounded-medium w-full p-2 h-12"
    />
  );
};

export default function DepositModal({ proxyWallet, visible, onClose }: DepositModalProps) {
  const { t } = useTranslation();
  const { data: session } = useSession();
  const { address } = useUserAddress();
  const { walletType } = useGlobalState();

  // 使用共享的钱包验证 hook
  const { walletStatus } = useWalletValidation();

  // 状态管理
  const [inputValue, setInputValue] = useState("");
  const [USDCBalance, setUSDCBalance] = useState("");
  const [amountDifference, setAmountDifference] = useState(0);
  const [completedStatus, setCompletedStatus] = useState<"unsubmit" | "success" | "failed">("unsubmit");
  const [buttonState, setButtonState] = useState<"none" | "loading" | "success">("none");
  const [transactionHash, setTransactionHash] = useState("");
  const [isShowQRCode, setIsShowQRCode] = useState(false);
  const [isShowPopOne, setIsShowPopOne] = useState(false);
  const [isShowPopTwo, setIsShowPopTwo] = useState(false);
  const [isShowLimitError, setIsShowLimitError] = useState(true); // 初始为 true，因为 inputValue 初始为空
  const [isShowInsufficientError, setIsShowInsufficientError] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  // 输入验证逻辑 - 始终要求最少 1 USDC
  useEffect(() => {
    setIsShowLimitError(inputValue === "" || Number(inputValue) < 1);
  }, [inputValue]);

  useEffect(() => {
    const inputAmount = parseFloat(inputValue) || 0;
    const balance = parseFloat(USDCBalance) || 0;
    setIsShowInsufficientError(inputAmount > balance);
    setAmountDifference(inputAmount - balance);
  }, [inputValue, USDCBalance]);

  // 获取余额
  const fetchBalance = useCallback(async () => {
    if (!proxyWallet) return;

    try {
      const balance = await getUSDCBalance(proxyWallet);
      setUSDCBalance(balance);
    } catch (error) {
      setUSDCBalance("0");
      console.error("Error fetching USDC balance:", error);
    }
  }, [proxyWallet]);

  useEffect(() => {
    fetchBalance();
  }, [fetchBalance]);

  // 重置状态
  const handleRetry = useCallback(() => {
    setCompletedStatus("unsubmit");
    setButtonState("none");
    setTransactionHash("");
  }, []);

  // 处理授权操作
  const handleApprovalClick = useCallback(() => {
    onClickExecTransaction(
      address,
      proxyWallet,
      session,
      () => {
        // 这里不需要设置本地状态，因为 useWalletValidation 会自动更新
      },
      setShowTooltip,
    );
  }, [address, proxyWallet, session]);

  // 处理充值
  const handleDeposit = useCallback(async () => {
    if (!address) {
      console.error("No address available");
      return;
    }

    setButtonState("loading");

    try {
      const amount = parseUnits(inputValue, 6);
      const allowance = await checkAllowance(proxyWallet);

      // 如果授权额度不足，先增加授权
      if (allowance < amount) {
        await increaseAllowance(proxyWallet, address, session?.cookie, walletType);
      }

      // 执行充值
      await depositConstruct({
        proxyWalletAddr: proxyWallet,
        signerAddr: address,
        amount: parseFloat(inputValue),
        setTransactionHash,
        setCompletedStatus,
        setButtonState,
        cookie: session?.cookie,
        walletType,
      });
    } catch (error) {
      console.error("充值过程中发生错误:", error);
      setCompletedStatus("failed");
    } finally {
      setButtonState("none");
    }
  }, [address, inputValue, proxyWallet, session?.cookie, walletType]);

  // 计算按钮是否禁用 - 使用共享的钱包状态
  const isDepositDisabled =
    isShowLimitError || isShowInsufficientError || !address || !walletStatus.isApproved || buttonState === "loading";

  // 获取授权按钮的状态
  const getApproveButtonState = () => {
    if (walletStatus.isApproved) return "success";
    return "none";
  };

  return (
    <div>
      <Modal isOpen={visible} onClose={onClose} size={"2xl"}>
        {completedStatus === "success" && (
          <CompletedDeposit
            inputValue={inputValue}
            onClose={onClose}
            handleRetry={handleRetry}
            transactionHash={transactionHash}
          />
        )}

        {completedStatus === "failed" && <FailedDeposit handleRetry={handleRetry} />}

        {completedStatus === "unsubmit" && (
          <ModalContent>
            <ModalHeader className="flex-center">
              <div className="pt-2">{t("Wallet_Deposit")}</div>
            </ModalHeader>

            <ModalBody className="px-8 pb-8">
              <div className="flex flex-col">
                {/* Step 1: 钱包地址 */}
                <div className="flex w-full flex-col md:flex-row items-center text-black whitespace-pre-line">
                  <span className="font-semibold mr-2 w-auto">Step 1: </span>
                  <span className="flex items-center flex-col md:flex-row">
                    {t("Deposit_Modal_Step_One")}
                    <Popover placement="top" isOpen={isShowPopOne} disableAnimation>
                      <PopoverTrigger
                        onMouseEnter={() => setIsShowPopOne(true)}
                        onMouseLeave={() => setIsShowPopOne(false)}
                      >
                        <div className="underline cursor-pointer ml-2">{"USDC(BASE)"}</div>
                      </PopoverTrigger>
                      <PopoverContent>
                        <div className="text-small w-auto flex-nowrap px-1 py-2">
                          {process.env.NEXT_PUBLIC_USDC_CONTRACT_ADDRESS}
                        </div>
                      </PopoverContent>
                    </Popover>
                  </span>
                </div>

                <div className="w-full flex items-center justify-between mt-2">
                  <Input
                    value={proxyWallet}
                    labelPlacement="outside"
                    radius="sm"
                    size="lg"
                    className="border rounded-medium flex-grow"
                    classNames={{
                      inputWrapper: ["bg-transparent"],
                      label: ["w-full"],
                    }}
                    endContent={
                      <CopyButton
                        isCopied={isCopied}
                        proxyWallet={proxyWallet}
                        onCopy={() => {
                          setIsCopied(true);
                          setTimeout(() => {
                            setIsCopied(false);
                          }, 1200);
                        }}
                      />
                    }
                  />
                  <div className="h-12 ml-2">
                    <QrCode
                      className="flex h-full items-center size-8 cursor-pointer"
                      onClick={() => setIsShowQRCode(true)}
                    />
                  </div>
                </div>

                <Divider className="bg-gray-100 my-4" />

                {/* Step 2: 授权钱包 - 使用共享状态 */}
                <div className="flex items-center justify-between gap-4">
                  <div className="flex flex-col justify-center">
                    <div className="flex items-center">
                      <span className="font-semibold mr-2 w-auto">Step 2: </span>
                      <span className="mr-2 w-auto">{t("Deposit_Modal_Step_Two")}</span>
                    </div>
                  </div>
                  <StatusButton
                    state={getApproveButtonState()}
                    showTooltip={showTooltip}
                    onClick={handleApprovalClick}
                    successMessage="Complete"
                    buttonText="Sign"
                  />
                </div>

                <Divider className="bg-gray-100 my-4" />

                {/* Step 3: 输入金额 */}
                <div className="flex flex-col md:flex-row items-center justify-between text-black flex-nowrap">
                  <div className="flex flex-col md:flex-row items-center text-black flex-nowrap">
                    <span className="font-semibold mr-2">Step 3: </span>
                    <span className="flex items-center">
                      {t("Deposit_Modal_Step_Three")}
                      <Popover placement="top" isOpen={isShowPopTwo} disableAnimation>
                        <PopoverTrigger
                          onMouseEnter={() => setIsShowPopTwo(true)}
                          onMouseLeave={() => setIsShowPopTwo(false)}
                        >
                          <BadgeAlert color="#1E88E5" className="flex h-full items-center ml-2 size-5 cursor-pointer" />
                        </PopoverTrigger>
                        <PopoverContent>
                          <div className="px-1 py-2">
                            <div className="text-small w-[300px]">{t("Deposit_Modal_Step_Two_Tip")}</div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </span>
                  </div>
                  <div className="text-blue-600 font-semibold mt-2 md:mt-0">
                    {`available: $${parseFloat(USDCBalance || "0").toFixed(2)}`}
                  </div>
                </div>

                <div className="w-full flex items-center justify-between mt-2">
                  <AmountInput value={inputValue} onChange={setInputValue} />
                  <div className="h-12 ml-2">
                    <RefreshCcw className="flex h-full items-center size-8 cursor-pointer" onClick={fetchBalance} />
                  </div>
                </div>

                {/* 错误提示 */}
                {(isShowLimitError || isShowInsufficientError) && (
                  <div className="flex flex-col gap-0">
                    {isShowLimitError && (
                      <div className="text-red-500 text-sm mt-2">{t("Deposit_Modal_Min_Value_Error")}</div>
                    )}

                    {isShowInsufficientError && (
                      <div className="text-red-500 text-sm mt-2">
                        {t("Deposit_Modal_Insufficient_Balance_Error_1") +
                          " $" +
                          amountDifference.toFixed(2) +
                          t("Deposit_Modal_Insufficient_Balance_Error_2")}
                      </div>
                    )}
                  </div>
                )}

                {/* 充值按钮 - 使用共享的钱包状态 */}
                <Button
                  size="lg"
                  color="primary"
                  radius="sm"
                  isLoading={buttonState === "loading"}
                  isDisabled={isDepositDisabled}
                  onPress={handleDeposit}
                  className="w-full font-semibold text-white mt-4"
                >
                  {t("Wallet_Deposit")}
                </Button>
              </div>
            </ModalBody>
          </ModalContent>
        )}
      </Modal>

      <WalletAddressModal visible={isShowQRCode} onClose={() => setIsShowQRCode(false)} proxyWallet={proxyWallet} />
    </div>
  );
}
