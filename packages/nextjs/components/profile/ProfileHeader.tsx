import React from "react";
import { <PERSON><PERSON>, Image, Link } from "@heroui/react";
import { getAddress } from "viem";
import { BlockieAvatar } from "~~/components/scaffold-eth";

const ProfileHeader = (props: any) => {
  const { address } = props;
  const checkSumAddress = address && getAddress(address);
  return (
    <div className="w-full flex items-start gap-4 mt-8">
      {checkSumAddress && <BlockieAvatar address={checkSumAddress} size={120} />}

      <div className="ml-4">
        <div className="text-4xl font-medium">superxiaojie</div>
        <div className="flex items-center">
          <span className="bg-gray-200 px-2 rounded-xl">0x594c...c9ea</span>
          <p className="text-gray-600 ml-4">Joined Oct 2024</p>
        </div>
      </div>

      <div className="ml-auto flex flex-col items-center gap-4">
        <Link href={`/profile/setting`} className="flex items-center min-h-10 group cursor-pointer">
          <Button
            size="lg"
            className={`border-1 rounded-lg bg-transparent`}
            //   onPress={() => handleNavigation("games")}
          >
            Edit profile
          </Button>
        </Link>

        <div className="flex items-center gap-2 mr-2">
          <Image className="h-8 w-8 rounded-full" src="/logo.png" alt="" width={32} height={32} />
          <span className="text-gray-300 text-2xl font-semibold">PredictOne</span>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
