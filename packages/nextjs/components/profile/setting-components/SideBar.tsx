import React, { useState } from "react";

interface SidebarProps {
  items: string[];
  onSelect: (item: string) => void;
}

const SideBar: React.FC<SidebarProps> = ({ items, onSelect }) => {
  const [selectedItem, setSelectedItem] = useState(items[0]);

  const handleItemClick = (item: string) => {
    setSelectedItem(item);
    onSelect(item);
  };

  return (
    <div className="max-w-[180px] w-full p-4">
      <div className="flex flex-col space-y-4">
        {items.map(item => (
          <div
            key={item}
            className={`cursor-pointer p-3 rounded-lg ${
              selectedItem === item ? "bg-gray-200" : "bg-transparent"
            } hover:bg-gray-200`}
            onClick={() => handleItemClick(item)}
          >
            {item}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SideBar;
