import React, { useEffect, useState } from "react";
import HistoryList from "@/components/portfolio/HistoryList";
import PositionsList from "@/components/portfolio/PositionsList";
import { getItem, setItem } from "@/utils";
import { Divider } from "@heroui/divider";
import { useTranslation } from "react-i18next";

const tabs = [
  { id: "positions", label: "Portfolio_Positions" },
  { id: "history", label: "Portfolio_History_Orders" },
];

const Tabs = (props: any) => {
  const { tabs, currentTabSelect, setCurrentTabSelect } = props;
  const { t } = useTranslation();

  const renderTab = (tab: any) => (
    <div
      key={tab.id}
      className={`flex mr-2 relative justify-center cursor-pointer hover:text-black ${
        currentTabSelect === tab.id ? "text-black" : "text-gray-500"
      }`}
      onClick={() => {
        setCurrentTabSelect(tab.id);
        setItem("currentTabSelect", tab.id);
      }}
    >
      <div>{t(tab.label)}</div>
      <div
        className={`absolute bottom-0 w-full h-0.5 ${currentTabSelect === tab.id ? "bg-black" : "bg-transparent"}`}
      />
    </div>
  );

  return <div className="h-9 flex gap-4 text-medium font-medium overflow-x-auto">{tabs.map(renderTab)}</div>;
};

const SelectTable = (props: any) => {
  const { proxyWallet } = props;
  const [currentTabSelect, setCurrentTabSelect] = useState<string>(() => getItem("currentTabSelect") || "positions");

  useEffect(() => {
    const savedTab = getItem("currentTabSelect");
    if (savedTab) {
      setCurrentTabSelect(savedTab);
    }
  }, []);

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mt-4">
        <Tabs tabs={tabs} currentTabSelect={currentTabSelect} setCurrentTabSelect={setCurrentTabSelect} />
      </div>
      <Divider className="bg-gray-100" />

      <div>{currentTabSelect === "positions" && <PositionsList proxyWallet={proxyWallet} />}</div>
      <div>{currentTabSelect === "history" && <HistoryList proxyWallet={proxyWallet} />}</div>
    </div>
  );
};

export default SelectTable;
