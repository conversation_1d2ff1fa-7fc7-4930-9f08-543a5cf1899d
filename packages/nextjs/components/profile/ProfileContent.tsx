import React from "react";
import SelectTable from "./SelectTable";
import { getItem } from "@/utils";
import { RotateCw } from "lucide-react";

const mockData = [
  { title: "Positions value", value: "$0.00" },
  { title: "Profit/loss", value: "-$0.92" },
  { title: "Volume traded", value: "$112.00" },
  { title: "Markets traded", value: "4" },
];

const ProfileContent = () => {
  const proxyWallet = getItem(`login_proxyWallet`);

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
        {mockData.map((item, index) => (
          <div key={index} className="bg-white rounded-lg p-4 flex flex-col items-start border-1">
            <span className="text-gray-500 mb-2">
              <RotateCw className="size-6" />
            </span>
            <div className="text-gray-700 font-normal text-sm">{item.title}</div>
            <div className="text-gray-900 font-medium text-xl">{item.value}</div>
          </div>
        ))}
      </div>
      <div className="mt-8">
        <SelectTable proxyWallet={proxyWallet} />
      </div>
    </div>
  );
};

export default ProfileContent;
