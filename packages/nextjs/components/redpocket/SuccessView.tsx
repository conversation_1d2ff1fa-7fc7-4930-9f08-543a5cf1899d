"use client";

import React from "react";
import { Button } from "@heroui/react";
import { useTranslation } from "react-i18next";

interface SuccessViewProps {
  claimedAmount: number;
  creditAmount?: number;
  onGoHome: () => void;
}

const SuccessView: React.FC<SuccessViewProps> = ({ claimedAmount, creditAmount, onGoHome }) => {
  const { t } = useTranslation();
  return (
    <div className="relative h-[calc(100vh-128px)] flex flex-col items-center justify-center bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-15">
        <div className="absolute top-10 left-10 w-32 h-32 bg-emerald-300 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-green-300 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-teal-200 rounded-full blur-3xl opacity-30"></div>

        {/* 金币雨效果 */}
        <div className="absolute top-20 left-1/4 w-3 h-3 bg-yellow-400 rounded-full animate-bounce delay-300"></div>
        <div className="absolute top-32 right-1/3 w-2 h-2 bg-amber-400 rounded-full animate-bounce delay-700"></div>
        <div className="absolute top-40 left-1/3 w-2.5 h-2.5 bg-yellow-500 rounded-full animate-bounce delay-1100"></div>
        <div className="absolute top-28 right-1/4 w-2 h-2 bg-amber-500 rounded-full animate-bounce delay-1500"></div>
      </div>

      {/* 红包打开动画容器 */}
      <div className="relative z-10 flex flex-col items-center justify-center redpacket-open w-full max-w-sm md:max-w-lg mx-auto px-4">
        {/* 成功图标 - 响应式尺寸 */}
        <div className="mb-4 md:mb-6 relative">
          <div className="w-20 h-20 md:w-28 md:h-28 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full flex items-center justify-center shadow-2xl relative overflow-hidden">
            {/* 背景光晕 */}
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-300 to-green-400 opacity-50 animate-pulse"></div>

            {/* 成功图标 */}
            <div className="relative z-10">
              <svg className="w-10 h-10 md:w-14 md:h-14 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </div>

            {/* 装饰圆环 */}
            <div className="absolute inset-2 border-2 border-white/30 rounded-full"></div>
          </div>

          {/* 浮动装饰 - 响应式尺寸 */}
          <div className="absolute -top-1 -right-1 md:-top-2 md:-right-2 w-6 h-6 md:w-8 md:h-8 bg-yellow-400 rounded-full animate-ping opacity-75"></div>
          <div className="absolute -bottom-1 -left-1 w-4 h-4 md:w-5 md:h-5 bg-amber-400 rounded-full animate-pulse delay-500"></div>

          {/* 光芒效果 - 响应式尺寸 */}
          <div
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-28 h-28 md:w-36 md:h-36 bg-gradient-to-r from-transparent via-yellow-200 to-transparent rounded-full opacity-20 animate-spin"
            style={{ animationDuration: "3s" }}
          ></div>
        </div>

        {/* 庆祝文字 - 响应式字体 */}
        <div className="text-center mb-3">
          <div className="text-xl md:text-2xl font-bold text-emerald-600 mb-2">{t("redpocket.successTitle")}</div>
          <h1 className="text-lg md:text-xl font-bold text-gray-800 leading-tight">{t("redpocket.successMessage")}</h1>
        </div>

        {/* 金额显示 - 响应式设计 */}
        <div className="mb-6 md:mb-8 w-full">
          {/* 大框包裹 - 响应式内边距和圆角 */}
          <div className="bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100 p-4 md:p-6 rounded-2xl md:rounded-3xl border-2 border-gray-200 shadow-2xl relative overflow-hidden">
            {/* 背景装饰 */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30"></div>

            <div className="relative z-10">
              {/* 顶部总奖励 - 响应式字体和间距 */}
              <div className="text-center mb-4 md:mb-6">
                <div className="text-sm md:text-lg text-gray-600 font-semibold mb-2 md:mb-3 uppercase tracking-wider">
                  {t("redpocket.totalReward")}
                </div>

                {/* 总金额 - 响应式字体 */}
                <div className="text-4xl md:text-6xl font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-600 via-blue-500 to-indigo-600 mb-2 md:mb-3 animate-pulse">
                  {(claimedAmount + (creditAmount ? creditAmount / 1000000 : 0)).toFixed(2)}
                </div>

                <div className="text-lg md:text-2xl font-bold text-gray-700 mb-3 md:mb-4">USDC</div>

                {/* 装饰分割线 - 响应式尺寸 */}
                <div className="flex items-center justify-center space-x-2 md:space-x-3 mb-4 md:mb-6">
                  <div className="w-6 md:w-8 h-0.5 bg-gradient-to-r from-transparent to-purple-300"></div>
                  <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-purple-400 rounded-full"></div>
                  <div className="w-12 md:w-16 h-0.5 bg-gradient-to-r from-purple-300 via-blue-300 to-indigo-300"></div>
                  <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-indigo-400 rounded-full"></div>
                  <div className="w-6 md:w-8 h-0.5 bg-gradient-to-r from-indigo-300 to-transparent"></div>
                </div>
              </div>

              {/* 奖励详情 - 响应式布局 */}
              <div className="flex flex-col sm:flex-row gap-2 md:gap-3">
                {/* 直接奖励 - 响应式尺寸 */}
                <div className="flex-1 bg-gradient-to-r from-emerald-50 to-green-50 p-3 md:p-4 rounded-lg md:rounded-xl border border-emerald-200 shadow-lg relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-emerald-100/20 to-green-100/20"></div>

                  <div className="relative z-10 text-center">
                    <div className="text-sm text-emerald-600 font-semibold mb-1 uppercase tracking-wider">
                      {t("redpocket.directReward")}
                    </div>

                    <div className="text-xl md:text-2xl font-black text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-green-500 mb-1">
                      {claimedAmount.toFixed(2)}
                    </div>
                    <div className="text-xs text-gray-500 font-medium">{t("redpocket.withdrawAnytime")}</div>
                  </div>
                </div>

                {/* 交易奖励 - 响应式尺寸 */}
                {creditAmount && creditAmount > 0 && (
                  <div className="flex-1 bg-gradient-to-r from-amber-50 to-yellow-50 p-3 md:p-4 rounded-lg md:rounded-xl border border-amber-200 shadow-lg relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-amber-100/20 to-yellow-100/20"></div>

                    <div className="relative z-10 text-center">
                      <div className="text-sm text-amber-600 font-semibold mb-1 uppercase tracking-wider">
                        {t("redpocket.tradingReward")}
                      </div>

                      <div className="text-xl md:text-2xl font-black text-transparent bg-clip-text bg-gradient-to-r from-amber-600 to-yellow-500 mb-1">
                        {(creditAmount / 1000000).toFixed(2)}
                      </div>

                      <div className="text-xs text-gray-500 font-medium">{t("redpocket.availableForPrediction")}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 返回按钮 - 响应式设计 */}
        <div className="w-full space-y-3 md:space-y-4">
          <Button
            onPress={onGoHome}
            size="lg"
            className="w-full bg-gradient-to-r from-emerald-500 via-green-500 to-teal-600 text-white px-8 md:px-12 py-4 md:py-5 rounded-xl md:rounded-2xl shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-300 font-bold text-lg md:text-xl relative overflow-hidden bounce-slow"
          >
            {/* 按钮背景光效 */}
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-400 to-green-400 opacity-0 hover:opacity-20 transition-opacity duration-300"></div>

            <span className="relative z-10 flex items-center justify-center gap-2">
              <svg className="w-5 h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              {t("redpocket.goToFuture")}
            </span>
          </Button>

          {/* 小提示 - 响应式字体 */}
          <div className="text-center text-xs md:text-sm text-gray-500 opacity-75">{t("redpocket.successNote")}</div>
        </div>

        {/* 装饰元素 - 绿色主题 */}
        <div className="absolute -z-10 top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-emerald-400 rounded-full twinkle"></div>
          <div className="absolute top-1/3 right-1/4 w-1 h-1 bg-green-400 rounded-full twinkle delay-500"></div>
          <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-teal-400 rounded-full twinkle delay-1000"></div>
          <div className="absolute bottom-1/3 right-1/3 w-1 h-1 bg-emerald-500 rounded-full twinkle delay-1500"></div>
          <div className="absolute top-1/2 left-1/6 w-1 h-1 bg-green-300 rounded-full twinkle delay-2000"></div>
          <div className="absolute top-3/4 right-1/6 w-1.5 h-1.5 bg-teal-300 rounded-full twinkle delay-2500"></div>
        </div>
      </div>

      <style jsx>{`
        .redpacket-open {
          animation: redPacketOpen 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
        }

        .bounce-slow {
          animation: bounceSlow 2s infinite;
        }

        .twinkle {
          animation: twinkle 2s ease-in-out infinite;
        }

        .delay-500 {
          animation-delay: 0.5s;
        }

        .delay-1000 {
          animation-delay: 1s;
        }

        .delay-1500 {
          animation-delay: 1.5s;
        }

        @keyframes redPacketOpen {
          0% {
            transform: scale(0.8);
            opacity: 0;
          }
          70% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @keyframes bounceSlow {
          0%,
          20%,
          53%,
          80%,
          100% {
            animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
            transform: translate3d(0, 0, 0);
          }
          40%,
          43% {
            animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
            transform: translate3d(0, -15px, 0);
          }
          70% {
            animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
            transform: translate3d(0, -7px, 0);
          }
          90% {
            transform: translate3d(0, -2px, 0);
          }
        }

        @keyframes twinkle {
          0%,
          100% {
            opacity: 0.3;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.2);
          }
        }
      `}</style>
    </div>
  );
};

export default SuccessView;
