"use client";

import React from "react";
import CountdownTimer from "@/components/redpocket/CountdownTimer";
import { Button } from "@heroui/react";
import { useTranslation } from "react-i18next";

// 类型定义
interface ErrorInfo {
  error: string;
  details?: any;
}

interface FailedViewProps {
  onGoHome: () => void;
  onGoBack: () => void;
  errorInfo?: ErrorInfo | null;
  isEventEnded?: boolean;
  showCountdown?: boolean;
  nextRoundTimestamp?: string;
  onCountdownComplete?: () => void;
}

const FailedView: React.FC<FailedViewProps> = ({
  onGoHome,
  onGoBack,
  errorInfo,
  isEventEnded = false,
  showCountdown = false,
  nextRoundTimestamp = "",
  onCountdownComplete,
}) => {
  const { t } = useTranslation();
  return (
    <div
      className={`relative h-[calc(100vh-180px)] md:h-[calc(100vh-128px)] flex flex-col items-center justify-center overflow-hidden ${
        isEventEnded
          ? "bg-gradient-to-br from-purple-100 via-indigo-50 to-blue-100"
          : "bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50"
      }`}
    >
      <div
        className={`relative z-10 flex flex-col items-center justify-center text-center mx-auto ${
          isEventEnded ? "max-w-lg" : "max-w-md"
        }`}
      >
        {/* 倒计时显示 - 当需要显示倒计时时 */}
        {showCountdown && nextRoundTimestamp && (
          <div className="mb-6 md:mb-8">
            <CountdownTimer
              targetTimestamp={nextRoundTimestamp}
              title={t("redpocket.countdownTitle")}
              onComplete={onCountdownComplete}
            />
          </div>
        )}

        {/* 失败动画图标 - 移动端缩小 */}
        <div className="mb-6 md:mb-12 relative">
          <div className="w-28 h-28 md:w-40 md:h-40 bg-gradient-to-br from-red-100 to-orange-100 rounded-full flex items-center justify-center shadow-2xl relative overflow-hidden">
            {/* 背景装饰 */}
            <div className="absolute inset-0 opacity-50 bg-gradient-to-br from-red-50 to-orange-50"></div>

            {/* 主图标 */}
            <div className="relative z-10 w-14 h-14 md:w-20 md:h-20 bg-gradient-to-br from-red-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
              {isEventEnded ? (
                <svg
                  className="w-8 h-8 md:w-12 md:h-12 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              ) : (
                <svg
                  className="w-7 h-7 md:w-10 md:h-10 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </div>

            {/* 装饰圆环 */}
            <div className="absolute inset-4 border-2 rounded-full opacity-30 border-red-200"></div>
            <div className="absolute inset-8 border rounded-full opacity-20 border-orange-200"></div>
          </div>

          {/* 浮动装饰 */}
          <div className="absolute -top-2 -right-2 w-6 h-6 rounded-full opacity-60 animate-pulse bg-red-400"></div>
          <div className="absolute -bottom-1 -left-1 w-4 h-4 rounded-full opacity-40 animate-pulse delay-500 bg-orange-400"></div>
        </div>

        {/* 状态信息 */}
        <div className="mb-6 md:mb-10 space-y-3 md:space-y-4">
          <div className="text-xl md:text-3xl font-bold mb-3 md:mb-4 text-gray-700">
            {isEventEnded ? t("redpocket.eventEndedTitle") : t("redpocket.failedTitle")}
          </div>

          <div className="m-4 p-4 rounded-2xl shadow-sm bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200">
            {isEventEnded ? (
              <>
                <div className="text-lg text-purple-600 font-semibold mb-2">{t("redpocket.eventEndedInfo")}</div>
                <div className="text-base text-gray-700 font-medium mb-2">{t("redpocket.eventEndedMessage")}</div>
              </>
            ) : errorInfo ? (
              <>
                <div className="text-lg text-orange-600 font-semibold">
                  {errorInfo.error === "already opened" && t("redpocket.errorAlreadyOpened")}
                  {errorInfo.error === "Invalid Code" && t("redpocket.errorInvalidCode")}
                  {errorInfo.error === "you come too late" && t("redpocket.errorTooLate")}
                  {errorInfo.error !== "already opened" &&
                    errorInfo.error !== "Invalid Code" &&
                    errorInfo.error !== "you come too late" &&
                    errorInfo.error !== "event_ended" &&
                    errorInfo.error}
                </div>
              </>
            ) : (
              <>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>{t("redpocket.networkErrorTip")}</div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="px-4 space-y-3 md:space-y-4 w-full">
          {!isEventEnded && (
            <Button
              onPress={onGoBack}
              size="lg"
              variant="bordered"
              className="w-full border-2 border-amber-300 text-amber-700 hover:bg-amber-50 px-6 md:px-8 py-3 md:py-4 rounded-2xl font-semibold transition-all duration-300 hover:scale-105"
            >
              {t("redpocket.retryButton")}
            </Button>
          )}

          <Button
            onPress={onGoHome}
            size="lg"
            className={`w-full px-6 md:px-8 py-3 md:py-4 rounded-2xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 font-semibold ${
              isEventEnded
                ? "bg-gradient-to-r from-purple-500 to-indigo-600 text-white"
                : "bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
            }`}
          >
            {t("redpocket.goToHome")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FailedView;
