"use client";

import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

interface CountdownTimerProps {
  targetDate?: Date;
  targetTimestamp?: string;
  onComplete?: () => void;
  title?: string;
}

interface TimeLeft {
  hours: number;
  minutes: number;
  seconds: number;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ targetDate, targetTimestamp, onComplete, title }) => {
  const { t } = useTranslation();
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ hours: 0, minutes: 0, seconds: 0 });
  const [isExpired, setIsExpired] = useState(false);

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      let target: number;

      if (targetTimestamp) {
        target = new Date(targetTimestamp).getTime();
      } else if (targetDate) {
        target = targetDate.getTime();
      } else {
        return;
      }

      const difference = target - now;

      if (difference > 0) {
        const hours = Math.floor(difference / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ hours, minutes, seconds });
        setIsExpired(false);
      } else {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
        setIsExpired(true);
        if (onComplete) {
          onComplete();
        }
      }
    };

    // 立即计算一次
    calculateTimeLeft();

    // 每秒更新一次
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [targetDate, targetTimestamp, onComplete]);

  const formatNumber = (num: number): string => {
    return num.toString().padStart(2, "0");
  };

  if (isExpired) {
    return null;
  }

  return (
    <div className="w-full">
      <div className="bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 py-2 px-12 rounded-xl md:rounded-3xl border-2 border-amber-200 shadow-xl md:shadow-2xl relative overflow-hidden">
        {/* 背景装饰 - 移动端缩小 */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-1 left-2 md:top-2 md:left-4 w-12 h-12 md:w-16 md:h-16 bg-yellow-300 rounded-full blur-xl md:blur-2xl animate-pulse"></div>
          <div className="absolute bottom-1 right-2 md:bottom-2 md:right-4 w-14 h-14 md:w-20 md:h-20 bg-orange-300 rounded-full blur-xl md:blur-2xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 md:w-32 md:h-32 bg-amber-200 rounded-full blur-2xl md:blur-3xl opacity-50"></div>
        </div>

        <div className="relative z-10">
          {/* 标题 - 移动端缩小 */}
          {title && (
            <div className="text-center mb-3 md:mb-4">
              <div className="text-xs md:text-base text-amber-700 font-semibold uppercase tracking-wider">{title}</div>
            </div>
          )}

          {/* 倒计时显示 - 移动端缩小 */}
          <div className="flex items-center justify-center space-x-1 md:space-x-4">
            {/* 小时 */}
            <div className="flex flex-col items-center">
              <div className="bg-gradient-to-br from-amber-400 to-orange-500 text-white font-black text-lg md:text-3xl px-2 md:px-4 py-1.5 md:py-3 rounded-lg md:rounded-2xl shadow-md md:shadow-lg min-w-[45px] md:min-w-[80px] text-center relative overflow-hidden">
                {/* 数字光效 */}
                <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 animate-pulse"></div>
                <span className="relative z-10">{formatNumber(timeLeft.hours)}</span>
              </div>
              <div className="text-xs md:text-sm text-amber-600 font-semibold mt-1 md:mt-2 uppercase tracking-wider">
                {t("redpocket.hours")}
              </div>
            </div>

            {/* 分隔符 */}
            <div className="text-lg md:text-3xl font-black text-amber-600 animate-pulse">:</div>

            {/* 分钟 */}
            <div className="flex flex-col items-center">
              <div className="bg-gradient-to-br from-amber-400 to-orange-500 text-white font-black text-lg md:text-3xl px-2 md:px-4 py-1.5 md:py-3 rounded-lg md:rounded-2xl shadow-md md:shadow-lg min-w-[45px] md:min-w-[80px] text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 animate-pulse"></div>
                <span className="relative z-10">{formatNumber(timeLeft.minutes)}</span>
              </div>
              <div className="text-xs md:text-sm text-amber-600 font-semibold mt-1 md:mt-2 uppercase tracking-wider">
                {t("redpocket.minutes")}
              </div>
            </div>

            {/* 分隔符 */}
            <div className="text-lg md:text-3xl font-black text-amber-600 animate-pulse">:</div>

            {/* 秒钟 */}
            <div className="flex flex-col items-center">
              <div className="bg-gradient-to-br from-amber-400 to-orange-500 text-white font-black text-lg md:text-3xl px-2 md:px-4 py-1.5 md:py-3 rounded-lg md:rounded-2xl shadow-md md:shadow-lg min-w-[45px] md:min-w-[80px] text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 animate-pulse"></div>
                <span className="relative z-10 countdown-seconds">{formatNumber(timeLeft.seconds)}</span>
              </div>
              <div className="text-xs md:text-sm text-amber-600 font-semibold mt-1 md:mt-2 uppercase tracking-wider">
                {t("redpocket.seconds")}
              </div>
            </div>
          </div>
        </div>

        {/* CSS 动画 */}
        <style jsx>{`
          .countdown-seconds {
            animation: countdownPulse 1s ease-in-out infinite;
          }

          @keyframes countdownPulse {
            0%,
            100% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.05);
            }
          }
        `}</style>
      </div>
    </div>
  );
};

export default CountdownTimer;
