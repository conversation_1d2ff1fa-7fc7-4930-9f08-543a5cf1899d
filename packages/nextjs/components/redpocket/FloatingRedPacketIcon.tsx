"use client";

import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";

const FloatingRedPacketIcon: React.FC = () => {
  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);
  const [isFooterVisible, setIsFooterVisible] = useState(false);
  const { t } = useTranslation();
  // 点击跳转到红包页面
  const handleClick = () => {
    router.push("/redpocket");
  };
  const lastScrollYRef = useRef(0);
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const lastScrollY = lastScrollYRef.current;

      if (currentScrollY > lastScrollY && currentScrollY > 50) {
        setIsFooterVisible(true);
      }
      // 向上滚动时隐藏Footer
      else if (currentScrollY < lastScrollY) {
        setIsFooterVisible(false);
      }

      lastScrollYRef.current = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div
      className={`fixed right-6 z-[60] transition-all duration-300 ${
        // 移动端：避开底部菜单栏 (64px) + How it works触发行 (48px) + 安全距离
        // PC端：根据Footer显示状态调整位置
        isFooterVisible
          ? "bottom-32 md:bottom-24" // Footer显示时，给Footer留出空间 (移动端需要额外空间给How it works)
          : "bottom-32 md:bottom-6" // Footer隐藏时，正常位置
      }`}
    >
      {/* 主红包图标 */}
      <div
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className="relative cursor-pointer group"
      >
        {/* 背景光晕效果 */}
        <div className="absolute inset-0 bg-gradient-to-r from-red-400 to-orange-500 rounded-full blur-lg opacity-30 animate-pulse scale-110"></div>

        {/* 主按钮 */}
        <div
          className={`
            relative w-16 h-16 md:w-20 md:h-20
            bg-gradient-to-br from-red-500 via-red-600 to-red-700
            rounded-full shadow-2xl
            flex items-center justify-center
            transform transition-all duration-300 ease-out
            hover:scale-110 hover:shadow-3xl
            active:scale-95
            ${isHovered ? "rotate-12" : "rotate-0"}
          `}
        >
          {/* 红包图标 */}
          <svg className="w-8 h-8 md:w-10 md:h-10 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v-.07zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
          </svg>

          {/* 闪光效果 */}
          <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-transparent via-white/20 to-transparent animate-pulse"></div>
        </div>

        {/* 悬浮提示文字 */}
        <div
          className={`
            absolute bottom-full right-0 mb-2
            px-3 py-1.5
            bg-gray-800 text-white text-sm font-medium
            rounded-lg shadow-lg
            whitespace-nowrap
            transform transition-all duration-200
            ${isHovered ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2 pointer-events-none"}
          `}
        >
          {t("redpocket.claimButton")}
          {/* 小箭头 */}
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
        </div>

        {/* 动画波纹效果 */}
        <div className="absolute inset-0 rounded-full border-2 border-red-400 animate-ping opacity-20"></div>
        <div className="absolute inset-0 rounded-full border-2 border-orange-400 animate-ping opacity-10 animation-delay-1000"></div>

        {/* 金币掉落动画效果 */}
        {isHovered && (
          <>
            <div className="absolute -top-2 -left-2 w-3 h-3 bg-yellow-400 rounded-full animate-bounce opacity-80 animation-delay-200"></div>
            <div className="absolute -top-1 -right-3 w-2 h-2 bg-yellow-300 rounded-full animate-bounce opacity-60 animation-delay-500"></div>
            <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-orange-400 rounded-full animate-bounce opacity-70 animation-delay-800"></div>
          </>
        )}
      </div>

      {/* 新年红包特效 - 飘落的花瓣 */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-0 left-1/2 w-1 h-1 bg-pink-300 rounded-full animate-ping opacity-40 animation-delay-300"></div>
        <div className="absolute top-2 right-1/4 w-1 h-1 bg-red-300 rounded-full animate-ping opacity-30 animation-delay-600"></div>
      </div>

      {/* 自定义动画样式 */}
      <style jsx>{`
        .animation-delay-200 {
          animation-delay: 0.2s;
        }
        .animation-delay-300 {
          animation-delay: 0.3s;
        }
        .animation-delay-500 {
          animation-delay: 0.5s;
        }
        .animation-delay-600 {
          animation-delay: 0.6s;
        }
        .animation-delay-800 {
          animation-delay: 0.8s;
        }
        .animation-delay-1000 {
          animation-delay: 1s;
        }

        @keyframes float {
          0%,
          100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        @keyframes shake {
          0%,
          100% {
            transform: translateX(0);
          }
          25% {
            transform: translateX(-2px);
          }
          75% {
            transform: translateX(2px);
          }
        }

        .group:hover .animate-float {
          animation: float 2s ease-in-out infinite;
        }

        .group:active .animate-shake {
          animation: shake 0.5s ease-in-out;
        }
      `}</style>
    </div>
  );
};

export default FloatingRedPacketIcon;
