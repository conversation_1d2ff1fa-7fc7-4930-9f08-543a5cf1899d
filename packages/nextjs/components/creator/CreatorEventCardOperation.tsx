import React from "react";
import { formatLargeAmountWithCommas, getTotalVolumeClob, isWithinOneDays } from "@/utils";
import { Heart, MessageCircle, Users } from "lucide-react";
import { useTranslation } from "react-i18next";

const CreatorEventCardOperation: React.FC<any> = props => {
  const currentItemData = props?.currentItemData;
  const { t } = useTranslation();

  if (!currentItemData) {
    return null;
  }

  const { event_markets = [], commentCount = 0, createdAt } = currentItemData;
  const volumeclob = getTotalVolumeClob(event_markets);

  return (
    <div className="h-8 sm:h-9 flex items-center justify-between px-2 sm:px-3 bg-gradient-to-r from-purple-50/50 to-pink-50/50 border-t border-purple-100/50">
      <div className="flex items-center">
        {isWithinOneDays(createdAt) ? (
          <span className="h-5 sm:h-6 px-1.5 sm:px-2 flex items-center gap-0.5 sm:gap-1 text-purple-700 bg-gradient-to-r from-purple-100 to-pink-100 text-xs font-semibold rounded-full border border-purple-200/50">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="size-2.5 sm:size-3 text-purple-600"
            >
              <path
                fillRule="evenodd"
                d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z"
                clipRule="evenodd"
              />
            </svg>
            <span className="hidden sm:inline">{t("New") || "NEW"}</span>
            <span className="sm:hidden">N</span>
          </span>
        ) : (
          <span className="text-purple-600 text-xs font-medium px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-md bg-purple-50/50">
            <span className="hidden sm:inline">${formatLargeAmountWithCommas(volumeclob)} Vol.</span>
            <span className="sm:hidden">${formatLargeAmountWithCommas(volumeclob)}</span>
          </span>
        )}
      </div>
      <div className="flex items-center gap-1.5 sm:gap-3">
        {/* Community Trust Indicator - Hidden on mobile */}

        {/* Like/Support Button */}
        <Heart className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-purple-500 hover:text-red-500 transition-colors cursor-pointer" />

        {/* Comments */}
        <div className="flex items-center gap-0.5 sm:gap-1 text-purple-500 hover:text-purple-700 transition-colors cursor-pointer">
          <MessageCircle className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
          <span className="text-xs font-medium">{commentCount || 0}</span>
        </div>

        {/* Community Badge - Moved from top-right to avoid chart conflict */}
        <div className="flex items-center gap-1 px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-medium rounded-full shadow-sm">
          <Users className="size-2.5 sm:size-3" />
          <span className="hidden sm:inline">{t("Community") || "Community"}</span>
          <span className="sm:hidden">{t("Community_Short") || "Com"}</span>
        </div>
      </div>
    </div>
  );
};

export default CreatorEventCardOperation;
