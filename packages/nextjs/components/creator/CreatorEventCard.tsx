import React, { useEffect, useState } from "react";
import Image from "next/image";
import ProgressBar from "../events-components/ProgressBar";
import { EventCardProps, Market } from "../events-components/type";
import CreatorEventCardOperation from "./CreatorEventCardOperation";
import CreatorEventCardVote from "./CreatorEventCardVote";
import { base64DecodeByEn, base64DecodeByLanguage, hasExcludedTags, sortEventMarkets } from "@/utils";
import { Link } from "@heroui/link";
import { CheckCircle2, SquareCheck, Users } from "lucide-react";
import { useTranslation } from "react-i18next";
import { ChevronDoubleDownIcon, ChevronDoubleUpIcon } from "@heroicons/react/24/outline";
import { useGlobalState } from "~~/services/store/store";

// 文本处理函数：将中划线或下划线转换为空格并首字母大写
const formatDisplayText = (text: string): string => {
  if (!text) return text;

  return text
    .replace(/[-_]/g, " ") // 将中划线和下划线替换为空格
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // 每个单词首字母大写
    .join(" ");
};

function getOutcomeResult(outcome_prices?: string[]): [boolean, string] {
  if (
    Array.isArray(outcome_prices) &&
    outcome_prices.length === 2 &&
    (outcome_prices[0] === "1" || outcome_prices[1] === "1")
  ) {
    if (outcome_prices[0] === "1" && outcome_prices[1] === "0") {
      return [true, "Yes"];
    } else if (outcome_prices[0] === "0" && outcome_prices[1] === "1") {
      return [true, "No"];
    } else {
      return [true, "Unknown"];
    }
  }
  return [false, ""];
}

const ResultStatic: React.FC<{ resultText: string }> = ({ resultText }) => {
  const { t } = useTranslation();
  return (
    <div className="flex w-full items-center justify-center py-2">
      <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg">
        <CheckCircle2 className="size-5 text-purple-600" />
        <span className="text-sm font-semibold text-purple-700">
          {t("Result")}: {resultText}
        </span>
      </div>
    </div>
  );
};

const CreatorEventCard: React.FC<EventCardProps> = props => {
  const { eventCardData, timeStamp } = props;

  const [title, setTitle] = useState(formatDisplayText(base64DecodeByLanguage(eventCardData.title)));
  const [slug, setSlug] = useState(base64DecodeByEn(eventCardData.slug));
  const [defaultConditionId, setDefaultConditionId] = useState<number | null>(null);
  const isSingleQuestion = eventCardData.event_markets.length === 1;

  // 判断是否为单边事件
  const isSingleSidedEvent = hasExcludedTags(eventCardData);

  const { current_language } = useGlobalState().nativeCurrency;
  const sortedEventMarkets = sortEventMarkets(eventCardData.event_markets);

  const firstEventMarket = eventCardData?.event_markets?.[0];
  const questionMarket = firstEventMarket?.question_market;
  const lasttradeprice = questionMarket?.lasttradeprice;
  const percentageNum = lasttradeprice ? Math.round(lasttradeprice * 100) : 0;

  useEffect(() => {
    setTitle(formatDisplayText(base64DecodeByLanguage(eventCardData.title)));
    const newSlug = base64DecodeByEn(eventCardData.slug);
    setSlug(newSlug + "-" + current_language);

    const conditionId = eventCardData.event_markets[0]?.condition_id;
    setDefaultConditionId(conditionId);
  }, [current_language, eventCardData]);

  const onClickBtn = () => {
    const url = `/event/${slug}?tid=${eventCardData.id}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}`;
    window.location.href = url;
  };

  return (
    <div className="h-[180px] sm:h-[200px] flex-shrink-0 rounded-lg sm:rounded-xl border-2 border-purple-200 bg-gradient-to-br from-white to-purple-50 hover:bg-gradient-to-br hover:from-purple-50 hover:to-pink-50 hover:border-purple-300 transition-all duration-300 overflow-hidden shadow-sm hover:shadow-md">
      <div className="flex flex-col justify-between h-full relative">
        {/* Header Section */}
        <div className="flex h-14 sm:h-16 items-center justify-between px-3 sm:px-4 pt-2 sm:pt-3 flex-shrink-0">
          <div className="flex items-center justify-start gap-2 sm:gap-3 flex-1 pr-12 sm:pr-16">
            <div className="relative">
              <Image
                className="h-8 w-8 sm:h-10 sm:w-10 object-cover rounded-lg sm:rounded-xl border-2 border-purple-200 shadow-sm"
                src={eventCardData.image || "/image_alt.jpg"}
                alt=""
                width={40}
                height={40}
                onError={e => {
                  e.currentTarget.src = "/image_alt.jpg";
                }}
              />
              {/* Creator indicator */}
              <div className="absolute -bottom-0.5 sm:-bottom-1 -right-0.5 sm:-right-1 w-3 h-3 sm:w-4 sm:h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <Users className="size-1.5 sm:size-2 text-white" />
              </div>
            </div>
            <Link
              href={`/event/${slug}?tid=${eventCardData.id}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}`}
              className="text-xs sm:text-sm font-semibold text-gray-800 hover:text-purple-600 hover:underline transition-colors duration-200 flex-1"
            >
              <span className="line-clamp-2 leading-tight">{title}</span>
            </Link>
          </div>

          {/* Progress/Status Indicator */}
          {(() => {
            const outcome_prices = firstEventMarket?.question_market?.outcome_prices;
            const [isResult] = getOutcomeResult(outcome_prices);
            if (isSingleQuestion) {
              if (isResult) {
                return (
                  <div className="flex flex-col items-center justify-center mr-2">
                    <div className="bg-gradient-to-r from-purple-100 to-pink-100 p-[6px] rounded-lg">
                      <SquareCheck className="size-5" color="#9333ea" />
                    </div>
                    <span className="text-xs font-semibold text-purple-600">Done</span>
                  </div>
                );
              }
              if (lasttradeprice) {
                return <ProgressBar percentage={Number(percentageNum)} />;
              }
            }
            return null;
          })()}
        </div>

        {/* Content Section */}
        <div className="flex flex-col w-full px-3 sm:px-4 flex-1 min-h-0">
          {eventCardData.event_markets.length > 1 ? (
            <div className="flex flex-col gap-0.5 sm:gap-1 flex-1 overflow-y-auto no-scrollbar rounded-lg p-1.5 sm:p-2 min-h-0 bg-white/50 backdrop-blur-sm">
              {sortedEventMarkets.map((item: Market, index: number) => (
                <CreatorEventCardVote
                  key={`${item.condition_id}_${index}`}
                  currentData={item}
                  slug={slug}
                  tid={eventCardData.id}
                  current_language={current_language}
                  defaultConditionId={defaultConditionId}
                  timeStamp={timeStamp}
                  eventCardData={eventCardData}
                />
              ))}
            </div>
          ) : (
            <div className="flex w-full flex-1 items-end pb-2 sm:pb-3">
              {(() => {
                const outcome_prices = firstEventMarket?.question_market?.outcome_prices;
                const [isResult, resultText] = getOutcomeResult(outcome_prices);
                if (isResult) {
                  return <ResultStatic resultText={resultText} />;
                }

                if (isSingleSidedEvent) {
                  // 单边事件只显示 Yes 按钮 - 社区版本
                  return (
                    <button
                      onClick={onClickBtn}
                      className="h-9 sm:h-11 flex-1 flex items-center justify-center text-xs sm:text-sm font-semibold bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-lg sm:rounded-xl px-3 sm:px-4 py-2 hover:from-purple-200 hover:to-pink-200 transform hover:scale-[1.02] transition-all duration-200 active:scale-[0.98] border border-purple-200"
                    >
                      <span>Buy Yes</span>
                      <ChevronDoubleUpIcon className="size-3 sm:size-4 ml-1 sm:ml-2 text-purple-600" />
                    </button>
                  );
                }

                // 双边事件显示 Yes 和 No 按钮 - 社区版本
                return (
                  <div className="flex w-full gap-1.5 sm:gap-2">
                    <button
                      onClick={onClickBtn}
                      className="h-9 sm:h-11 flex-1 flex items-center justify-center text-xs sm:text-sm font-semibold bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-lg sm:rounded-xl px-2 sm:px-3 py-2 hover:from-purple-200 hover:to-pink-200 transform hover:scale-[1.02] transition-all duration-200 active:scale-[0.98] border border-purple-200"
                    >
                      <span>Buy Yes</span>
                      <ChevronDoubleUpIcon className="size-3 sm:size-4 ml-0.5 sm:ml-1 text-purple-600" />
                    </button>
                    <button
                      onClick={onClickBtn}
                      className="h-9 sm:h-11 flex-1 flex items-center justify-center text-xs sm:text-sm font-semibold bg-gradient-to-r from-red-100 to-orange-100 text-red-700 rounded-lg sm:rounded-xl px-2 sm:px-3 py-2 hover:from-red-200 hover:to-orange-200 transform hover:scale-[1.02] transition-all duration-200 active:scale-[0.98] border border-red-200"
                    >
                      <span>Buy No</span>
                      <ChevronDoubleDownIcon className="size-3 sm:size-4 ml-0.5 sm:ml-1 text-red-600" />
                    </button>
                  </div>
                );
              })()}
            </div>
          )}
        </div>

        {/* Footer Section */}
        <div className="w-full flex-shrink-0">
          <CreatorEventCardOperation currentItemData={eventCardData} />
        </div>
      </div>
    </div>
  );
};

export default CreatorEventCard;
