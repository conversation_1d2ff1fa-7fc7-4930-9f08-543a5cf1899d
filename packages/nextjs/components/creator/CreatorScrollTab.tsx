import React, { useEffect, useRef, useState } from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

// 文本处理函数：将中划线或下划线转换为空格并首字母大写
const formatTabLabel = (text: string): string => {
  if (!text) return text;

  return text
    .replace(/[-_]/g, " ") // 将中划线和下划线替换为空格
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // 每个单词首字母大写
    .join(" ");
};

interface CreatorScrollTabProps {
  tagListData: any[];
  selectedTagId: string;
  onTagClick: (tagId: string) => void;
}

const CreatorScrollTab: React.FC<CreatorScrollTabProps> = ({ tagListData, selectedTagId, onTagClick }) => {
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const handleTagClick = (tab: any) => {
    onTagClick(tab.id);
  };

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -400, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 400, behavior: "smooth" });
    }
  };

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener("scroll", checkScrollButtons);
      return () => container.removeEventListener("scroll", checkScrollButtons);
    }
  }, [tagListData]);

  useEffect(() => {
    const handleResize = () => checkScrollButtons();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  if (!tagListData || tagListData.length === 0) {
    return null;
  }

  return (
    <div className="relative w-full">
      {/* Left scroll button */}
      {canScrollLeft && (
        <button
          onClick={scrollLeft}
          className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-full p-2 shadow-lg hover:bg-white transition-all duration-200"
          aria-label="Scroll left"
        >
          <ChevronLeftIcon className="h-4 w-4 text-gray-600" />
        </button>
      )}

      {/* Right scroll button */}
      {canScrollRight && (
        <button
          onClick={scrollRight}
          className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-full p-2 shadow-lg hover:bg-white transition-all duration-200"
          aria-label="Scroll right"
        >
          <ChevronRightIcon className="h-4 w-4 text-gray-600" />
        </button>
      )}

      {/* Scrollable tabs container */}
      <div
        ref={scrollContainerRef}
        className="flex gap-2 overflow-x-auto scrollbar-hide px-8 py-2 mb-4"
        style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
      >
        {tagListData.map((tab: any) => (
          <button
            key={tab.id}
            onClick={() => handleTagClick(tab)}
            aria-label={tab.label}
            className={`h-10 flex items-center px-4 py-2 cursor-pointer text-sm font-semibold rounded-lg transition-all duration-200 whitespace-nowrap ${
              selectedTagId === tab.id
                ? "bg-blue-600 text-white transform scale-105"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900 "
            }`}
          >
            {formatTabLabel(tab.label)}
          </button>
        ))}
      </div>
    </div>
  );
};

export default CreatorScrollTab;
