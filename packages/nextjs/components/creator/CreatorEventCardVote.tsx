import React from "react";
import { base64DecodeByLanguage, formatLasttradeprice, hasExcludedTags } from "@/utils";
import { Link } from "@heroui/link";
import { CheckCircle2 } from "lucide-react";

// 文本处理函数：将中划线或下划线转换为空格并首字母大写
const formatDisplayText = (text: string): string => {
  if (!text) return text;

  return text
    .replace(/[-_]/g, " ") // 将中划线和下划线替换为空格
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // 每个单词首字母大写
    .join(" ");
};

function getOutcomeResult(outcome_prices?: string[]): [boolean, string] {
  if (
    Array.isArray(outcome_prices) &&
    outcome_prices.length === 2 &&
    (outcome_prices[0] === "1" || outcome_prices[1] === "1")
  ) {
    if (outcome_prices[0] === "1" && outcome_prices[1] === "0") {
      return [true, "Yes"];
    } else if (outcome_prices[0] === "0" && outcome_prices[1] === "1") {
      return [true, "No"];
    } else {
      return [true, "Unknown"];
    }
  }
  return [false, ""];
}

const CreatorEventCardVote: React.FC<any> = props => {
  const { slug, tid, timeStamp, defaultConditionId, current_language, eventCardData } = props;

  const questionMarket = props?.currentData?.question_market;
  if (!questionMarket) {
    return null;
  }

  const { id, question: groupItemTitle, lasttradeprice, outcome_prices } = questionMarket;
  const [isResult, resultText] = getOutcomeResult(outcome_prices);

  // 判断是否为单边事件
  const isSingleSidedEvent = hasExcludedTags(eventCardData);

  return (
    <div className="flex items-center justify-between py-1 sm:py-1.5 px-1.5 sm:px-2 rounded-lg hover:bg-white/80 transition-colors duration-200 min-w-0 border border-purple-100/50">
      <Link
        href={`/event/${slug}?tid=${tid}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}&qid=${id}`}
        className="text-xs sm:text-sm font-medium text-gray-800 hover:text-purple-600 hover:underline transition-colors duration-200 flex-1 mr-1.5 sm:mr-2 min-w-0"
      >
        <span className="block truncate">{formatDisplayText(base64DecodeByLanguage(groupItemTitle))}</span>
      </Link>
      <div className="flex items-center gap-1 sm:gap-1.5 flex-shrink-0">
        <div className="text-xs font-bold text-purple-700 bg-gradient-to-r from-purple-100 to-pink-100 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-lg min-w-[2rem] sm:min-w-[2.5rem] text-center border border-purple-200/50">
          {formatLasttradeprice(lasttradeprice)}
        </div>
        {isResult ? (
          <div className="flex items-center text-xs bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-lg px-1.5 sm:px-2 py-0.5 sm:py-1 cursor-default border border-purple-200/50">
            <CheckCircle2 className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-purple-600 mr-0.5 sm:mr-1" />
            <span className="hidden sm:inline">{resultText}</span>
            <span className="sm:hidden">{resultText.charAt(0)}</span>
          </div>
        ) : isSingleSidedEvent ? (
          // 单边事件只显示 Yes 按钮 - 社区版本
          <button className="text-xs font-semibold bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-lg px-1.5 sm:px-2 py-0.5 sm:py-1 hover:from-purple-200 hover:to-pink-200 transition-all duration-200 flex-shrink-0 border border-purple-200/50">
            Yes
          </button>
        ) : (
          // 双边事件显示 Yes 和 No 按钮 - 社区版本
          <div className="flex gap-0.5 sm:gap-1 flex-shrink-0">
            <button className="text-xs font-semibold bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-lg px-1 sm:px-1.5 py-0.5 sm:py-1 hover:from-purple-200 hover:to-pink-200 transition-all duration-200 border border-purple-200/50">
              Yes
            </button>
            <button className="text-xs font-semibold bg-gradient-to-r from-red-100 to-orange-100 text-red-700 rounded-lg px-1 sm:px-1.5 py-0.5 sm:py-1 hover:from-red-200 hover:to-orange-200 transition-all duration-200 border border-red-200/50">
              No
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreatorEventCardVote;
