import React from "react";
import { Divider } from "@heroui/divider";
import { useTranslation } from "react-i18next";

interface TableHeadProps {
  headers: { width: string; label: string; value?: string }[];
}

const TableHead: React.FC<TableHeadProps> = ({ headers }) => {
  const { t } = useTranslation();

  return (
    <div className="hidden w-full md:flex flex-col gap-1 text-gray-500">
      <Divider className="bg-gray-100" />
      <div className="flex w-full items-center justify-between text-xs font-semibold py-1 px-2">
        {headers.map((header, index) => (
          <div key={index} className={`w-[${header.width}] ${index === 0 ? "text-left" : "text-center"}`}>
            {t(header.label)}
            {header.value && <span className="ml-1">{header.value}</span>}
          </div>
        ))}
      </div>
      <Divider className="bg-gray-100" />
    </div>
  );
};

export default TableHead;
