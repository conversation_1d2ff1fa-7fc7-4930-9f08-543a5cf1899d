import React, { useEffect, useState } from "react";
import Image from "next/image";
import { getHistoryData } from "@/api/markets/history";
import SingleSidedButton from "@/components/ui/SingleSidedButton";
import {
  base64DecodeByLanguage,
  formatDynamicButtonPrice,
  formatLargeAmountWithCommas,
  formatLastTradePrice,
} from "@/utils";
import { Divider } from "@heroui/divider";
import { Popover, PopoverContent, PopoverTrigger } from "@heroui/popover";
import { Button, Tooltip } from "@heroui/react";
import { Gift } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "react-responsive";
import OrderBooksList from "~~/components/details/marketItem/OrderBooksList";
import { useGlobalState } from "~~/services/store/store";

const Tabs = (props: any) => {
  const { currentTabSelect, setCurrentTabSelect, bookData } = props;
  const { t } = useTranslation();
  const isMobile = useMediaQuery({ maxWidth: 767 });

  const isSelected = currentTabSelect === "orderbook";
  const hasData = bookData.asks.length > 0 || bookData.bids.length > 0;

  // 只有桌面端才自动展开
  useEffect(() => {
    if (hasData && !isMobile) {
      setCurrentTabSelect("orderbook");
    }
  }, [hasData, setCurrentTabSelect, isMobile]);

  return (
    <div className="h-8 flex gap-4 font-semibold">
      <div
        className={`cursor-pointer flex mr-2 relative justify-center text-medium hover:text-black ${
          isSelected ? "text-black" : "text-gray-500"
        }`}
        onClick={() => setCurrentTabSelect(currentTabSelect === "orderbook" ? "" : "orderbook")}
      >
        <div className="relative group flex items-center text-sm font-bold">{t("Event_OrderBook")}</div>
      </div>
    </div>
  );
};

const MarketItem = (props: any) => {
  const {
    item,
    bookData,
    selectedButtonType,
    selectedItemId,
    onButtonClick,
    onItemClick,
    currentSelectedItem,
    setIsShowOrderCardMobile,
    setBtnPriceData,
    lastTradePriceMap,
    isSingleSidedTrading,
  } = props;
  const isMobile = useMediaQuery({ maxWidth: 767 });

  const { t } = useTranslation();
  const { current_language } = useGlobalState().nativeCurrency;

  const [currentLanguage, setCurrentLanguage] = useState<string>("en");
  const [chanceNum, setChanceNum] = useState<number>(0);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const lng = urlParams.get("lng");
    if (lng) {
      setCurrentLanguage(lng);
    } else {
      setCurrentLanguage(current_language);
    }
  }, [current_language]);

  const [popState, setPopState] = useState(false);
  // 移动端默认不展开OrderBook
  const [currentTabSelect, setCurrentTabSelect] = useState<string>("");
  const [groupItemTitle, setGroupItemTitle] = useState<string>("");
  const {
    id,
    icon,
    volumeclob,
    order_min_size: orderMinSize,
    rewards_max_spread: rewardsMaxSpread,
    rewards_min_size: rewardsMinSize,
    question,
    clob_token_ids: clobTokenIds,
  } = item.question_market;

  const isSelected = selectedItemId === id;

  useEffect(() => {
    if (question) {
      setGroupItemTitle(base64DecodeByLanguage(question, currentLanguage));
    }
  }, [currentLanguage, question]);

  useEffect(() => {
    getHistoryData(clobTokenIds[0], "ALL")
      .then(res => {
        const { history } = res.data;
        if (history && history.length > 0) {
          const lastPrice = history[history.length - 1]?.p || 0;
          setChanceNum(formatLastTradePrice(lastPrice));
        } else {
          setChanceNum(0);
        }
      })
      .catch(() => {
        setChanceNum(0);
      });
  }, [clobTokenIds]);

  useEffect(() => {
    if (clobTokenIds && clobTokenIds.length === 2 && Object.keys(lastTradePriceMap).length > 0) {
      const yesTokenId = clobTokenIds[0];
      const noTokenId = clobTokenIds[1];

      const yesPrice = lastTradePriceMap[yesTokenId]?.price;
      const noPrice = lastTradePriceMap[noTokenId]?.price;

      if (yesPrice !== undefined) {
        setChanceNum(formatLastTradePrice(yesPrice));
      } else if (noPrice !== undefined) {
        setChanceNum(formatLastTradePrice(1 - noPrice));
      }
    }
  }, [clobTokenIds, lastTradePriceMap]);

  // 当切换到移动端时，关闭OrderBook
  useEffect(() => {
    if (isMobile) {
      setCurrentTabSelect("");
    }
  }, [isMobile]);

  const totalBet = formatLargeAmountWithCommas(volumeclob);

  const { YesPrice = null, NoPrice = null } = bookData
    ? formatDynamicButtonPrice(bookData, currentSelectedItem, selectedButtonType)
    : {};

  const hasData = bookData?.asks?.length > 0 || bookData?.bids?.length > 0;
  const handleButtonClick = (buttonType: string) => {
    onButtonClick(id, buttonType);
    setIsShowOrderCardMobile(true);
  };

  useEffect(() => {
    if (selectedItemId === id) {
      setBtnPriceData({
        YesPrice: YesPrice,
        NoPrice: NoPrice,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bookData, selectedItemId, id, currentSelectedItem]);

  return (
    <div>
      <div
        className="flex flex-col md:flex-row items-center justify-between py-3 px-2 hover:bg-gray-100 rounded-lg cursor-pointer"
        onClick={() => onItemClick(id)}
      >
        <div className="flex w-full md:w-[40%] items-center">
          <div className="flex w-full items-center gap-2">
            <Image
              className="h-10 w-10 object-cover rounded-full"
              src={icon || "/image_alt.jpg"}
              alt=""
              width={40}
              height={40}
            />
            <div className="flex flex-col whitespace-nowrap ml-2 w-[80%] md:max-w-[58%]">
              <Tooltip content={groupItemTitle}>
                <span className="text-md font-semibold hover:underline truncate">{groupItemTitle}</span>
              </Tooltip>
              <div className="flex items-center text-sm text-gray-600 flex-wrap">
                <span className="mr-3">${totalBet} Vol.</span>
                <Popover placement="bottom-end" isOpen={popState} disableAnimation>
                  <PopoverTrigger onMouseEnter={() => setPopState(true)} onMouseLeave={() => setPopState(false)}>
                    <div className="size-4 flex-center bg-gray-200 rounded-md z-1">
                      <Gift className="size-3" />
                    </div>
                  </PopoverTrigger>
                  <PopoverContent>
                    <div className="flex flex-col px-1 py-2 text-small w-[210px]">
                      <div className="flex items-center justify-between">
                        <span className="font-semibold">{t("Event_OrderBook_Tips_Rewards")}</span>
                        <span className="text-gray-500 font-medium">{orderMinSize * 1000}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-semibold">{t("Event_OrderBook_Tips_MaxSpread")}</span>
                        <span className="text-gray-500 font-medium font-sans">±{rewardsMaxSpread}¢</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-semibold">{t("Event_OrderBook_Tips_MinShares")}</span>
                        <span className="text-gray-500 font-medium">{rewardsMinSize}</span>
                      </div>
                      <div className="text-blue-600 font-medium">Learn More</div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
        </div>

        <div className="hidden md:flex-center w-[20%] text-2xl font-semibold">
          {chanceNum < 1 ? "<1%" : chanceNum + "%"}
        </div>

        <div className="flex items-center justify-center md:justify-end w-full md:w-[40%] gap-3 mt-2 md:mt-0">
          {isSingleSidedTrading ? (
            <SingleSidedButton
              title={groupItemTitle}
              price={YesPrice || "0"}
              isSelected={selectedItemId === id && selectedButtonType === "yes"}
              questionIndex={id}
              onClick={() => handleButtonClick("yes")}
              className="w-full md:max-w-[134px] flex-1"
            />
          ) : (
            <Button
              size="lg"
              radius="sm"
              onPress={() => handleButtonClick("yes")}
              className={`w-full md:max-w-[134px] flex-1 text-sm font-sans ${
                selectedItemId === id && selectedButtonType === "yes"
                  ? "bg-green-600 text-white"
                  : "bg-green-100 text-green-600"
              } hover:opacity-80 font-semibold`}
            >
              <div className="flex flex-col items-center">
                <span className="text-xs truncate max-w-[120px]">{"Yes"}</span>
                <span className="font-bold">{YesPrice}¢</span>
              </div>
            </Button>
          )}
          {!isSingleSidedTrading && (
            <Button
              size="lg"
              radius="sm"
              onPress={() => handleButtonClick("no")}
              className={`w-full md:max-w-[134px] flex-1 text-sm font-sans ${
                selectedItemId === id && selectedButtonType === "no"
                  ? "bg-red-600 text-white"
                  : "bg-red-100 text-red-600"
              } hover:opacity-80 font-semibold`}
            >
              <div className="flex flex-col items-center">
                <span className="text-xs truncate max-w-[120px]">{"No"}</span>
                <span className="font-bold">{NoPrice}¢</span>
              </div>
            </Button>
          )}
        </div>
      </div>

      {isSelected && bookData && (
        <div>
          <div className="flex items-center justify-between">
            <div className="ml-4 mt-1">
              <Tabs currentTabSelect={currentTabSelect} setCurrentTabSelect={setCurrentTabSelect} bookData={bookData} />
            </div>
          </div>

          <Divider className="bg-gray-100" />

          <div>
            {currentTabSelect === "orderbook" && hasData && (
              <OrderBooksList bookData={bookData} chanceNum={chanceNum} selectedButtonType={selectedButtonType} />
            )}
            {currentTabSelect === "orderbook" && !hasData && (
              <div className="flex items-center justify-center h-24 bg-gray-100 rounded-lg">
                <span className="text-gray-500">{"no data"}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketItem;
