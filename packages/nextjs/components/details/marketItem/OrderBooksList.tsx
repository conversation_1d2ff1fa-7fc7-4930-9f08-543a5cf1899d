import React, { useEffect, useRef } from "react";
import { calculateTotalForAsks, calculateTotalForBids, processNumber } from "@/utils";
import { useTranslation } from "react-i18next";
import TableHead from "~~/components/details/marketItem/TableHead";

const tableYseHeaderOne = [
  { width: "40%", label: "TRADE YES" },
  { width: "20%", label: "Event_OrderBook_Tabel_Price" },
  { width: "20%", label: "Event_OrderBook_Tabel_SHARES" },
  { width: "20%", label: "Event_OrderBook_Tabel_Total" },
];
const tableNoHeaderOne = [
  { width: "40%", label: "TRADE NO" },
  { width: "20%", label: "Event_OrderBook_Tabel_Price" },
  { width: "20%", label: "Event_OrderBook_Tabel_SHARES" },
  { width: "20%", label: "Event_OrderBook_Tabel_Total" },
];

const BookItem = (item: any) => {
  const { type, maxTotal } = item;
  const { price, size, total } = item.item;
  const typeColor = type == "asks" ? "red" : "green";
  const widthPercentage = ((parseFloat(total) / parseFloat(maxTotal)) * 100).toFixed(2);

  return (
    <div>
      <div className={`w-full h-9 flex items-center justify-between text-sm hover:bg-${typeColor}-50`}>
        <div className={`w-[40%] h-full`}>
          {type == "asks" && (
            <div className={`h-full bg-red-200 opacity-50`} style={{ width: `${widthPercentage}%` }}></div>
          )}
          {type == "bids" && (
            <div className={`h-full bg-green-200 opacity-50`} style={{ width: `${widthPercentage}%` }}></div>
          )}
        </div>

        <div className={`w-[20%] flex-center font-medium font-sans text-${typeColor}-600`}>
          {(price * 100).toFixed(1)}¢
        </div>
        <div className="w-[20%] flex-center">{processNumber(size, 18)}</div>
        <div className="w-[20%] flex-center">${processNumber(total, 18)}</div>
      </div>
    </div>
  );
};

const OrderBooksList = (props: any) => {
  const { bookData, chanceNum, selectedButtonType } = props;
  const { t } = useTranslation();

  const containerRef = useRef<HTMLDivElement>(null);
  const tableHeadRef = useRef<HTMLDivElement>(null);

  const { asks = [], maxAsksTotal } = bookData?.asks
    ? calculateTotalForAsks(bookData.asks)
    : { asks: [], maxAsksTotal: 0 };
  const { bids = [], maxBidsTotal } = bookData?.bids
    ? calculateTotalForBids(bookData.bids)
    : { bids: [], maxBidsTotal: 0 };
  const spread =
    asks.length > 0 && bids.length > 0 ? ((asks[asks.length - 1]?.price - bids[0]?.price) * 100).toFixed(1) : 0;

  useEffect(() => {
    if (containerRef.current && tableHeadRef.current) {
      const containerHeight = containerRef.current.offsetTop;
      const tableHeadOffsetTop = tableHeadRef.current.offsetTop;

      containerRef.current.scrollTop = tableHeadOffsetTop - containerHeight - 156;
    }
  }, [containerRef, tableHeadRef]);

  const tableHeaderTwo = [
    { width: "40%", label: `Event_OrderBook_Last`, value: `${chanceNum}¢` },
    { width: "20%", label: `Event_OrderBook_Spread`, value: `${spread}¢` },
    { width: "20%", label: "" },
    { width: "20%", label: "" },
  ];

  return (
    <div className="w-full flex flex-col">
      <TableHead headers={selectedButtonType == "yes" ? tableYseHeaderOne : tableNoHeaderOne} />

      <div ref={containerRef} className="h-[360px] overflow-y-auto no-scrollbar">
        {asks.map((item: any, index: number) => (
          <BookItem type="asks" key={index} item={item} maxTotal={maxAsksTotal} />
        ))}

        <div ref={tableHeadRef} className="relative">
          {asks.length > 0 && (
            <div className="px-1 bg-red-600 absolute bottom-12 left-6 text-white text-xs rounded-md opacity-90">
              {t("Event_OrderBook_Asks")}
            </div>
          )}

          <TableHead headers={tableHeaderTwo} />

          {bids.length > 0 && (
            <div className="px-1 bg-green-600 absolute top-12 left-6 text-white text-xs rounded-md opacity-90">
              {t("Event_OrderBook_Bids")}
            </div>
          )}
        </div>

        {bids.map((item: any, index: number) => (
          <BookItem type="bids" key={index} item={item} maxTotal={maxBidsTotal} />
        ))}
      </div>
    </div>
  );
};

export default OrderBooksList;
