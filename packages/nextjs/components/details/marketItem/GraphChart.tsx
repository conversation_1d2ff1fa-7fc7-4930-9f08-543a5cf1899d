import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import * as d3 from "d3";
import { ArrowDown, ArrowUp } from "lucide-react";

interface LineData {
  history: { t: number; p: number }[];
  color: string;
  name: string;
}

interface LineChartProps {
  data: LineData[];
}

const LineChart: React.FC<LineChartProps> = ({ data }) => {
  const svgRef = useRef<SVGSVGElement | null>(null);

  const historyLength = data[0]?.history.length;
  const lastPValue = data[0]?.history[historyLength - 1]?.p;

  const [currentY, setCurrentY] = useState<any>(lastPValue);
  const [currentYDiff, setCurrentYDiff] = useState<any>(0);
  useEffect(() => {
    if (lastPValue) {
      setCurrentY(lastPValue);
    }
  }, [lastPValue]);

  useEffect(() => {
    const svg = d3.select(svgRef.current);
    const width = 800;
    const height = 240;
    const margin = { top: 20, right: 40, bottom: 30, left: 0 };

    // 清空之前的内容
    svg.selectAll("*").remove();

    // 设置图表的宽高
    svg.attr("viewBox", `0 0 ${width} ${height}`);

    // 合并所有折线的数据以确定x轴和y轴的比例尺
    const allData = data.flatMap(line => line.history);
    const processedData = allData.map(d => ({
      date: new Date(d.t * 1000),
      value: d.p,
    }));

    // 设置x轴和y轴的比例尺
    const x = d3
      .scaleTime()
      .domain(d3.extent(processedData, d => d.date) as [Date, Date])
      .range([margin.left, width - margin.right]);

    const y = d3
      .scaleLinear()
      .domain([d3.min(processedData, d => d.value) as number, d3.max(processedData, d => d.value) as number])
      .nice()
      .range([height - margin.bottom, margin.top]);

    // 创建x轴和y轴
    const xAxis = (g: d3.Selection<SVGGElement, unknown, null, undefined>) =>
      g
        .attr("transform", `translate(0,${height - margin.bottom})`)
        .call(
          d3
            .axisBottom(x)
            .ticks(width / 80)
            .tickSizeOuter(0),
        )
        .call(g => g.selectAll(".domain, .tick line").attr("stroke", "gray")) // 设置轴线和刻度线颜色
        .call(g => g.selectAll(".tick text").attr("fill", "gray")) // 设置刻度文字颜色
        .call(g => g.selectAll(".tick line").attr("stroke", "none")); // 隐藏刻度线

    const yAxis = (g: d3.Selection<SVGGElement, unknown, null, undefined>) =>
      g.attr("transform", `translate(${width - margin.right},0)`);

    // 绘制x轴和y轴
    svg.append("g").call(xAxis);
    svg.append("g").call(yAxis);

    // 绘制每条折线
    data.forEach(lineData => {
      const line = d3
        .line<{ t: number; p: number }>()
        .defined(d => !isNaN(d.p))
        .x(d => x(new Date(d.t * 1000)))
        .y(d => y(d.p));

      svg
        .append("path")
        .datum(lineData.history)
        .attr("fill", "none")
        .attr("stroke", lineData.color)
        .attr("stroke-width", 2)
        .attr("d", line);
    });

    // 添加圆点
    const timeLabel = svg
      .append("text")
      .attr("fill", "#B1B3BE")
      .attr("x", 0) // 初始 x 坐标
      .attr("y", margin.top + 10) // 在竖线顶部下方一点
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .text(""); // 初始文本为空

    // 更新竖线和时间标签的位置和内容
    function updateVerticalLine(xPosition: any, timeText: any) {
      timeLabel.attr("x", xPosition).text(timeText);
    }

    const focusElements = data.map(lineData => {
      const circle = svg.append("circle").attr("r", 4).attr("fill", lineData.color).style("opacity", 0);

      const rect = svg
        .append("rect")
        .attr("width", 50) // 方块宽度
        .attr("height", 20) // 方块高度
        .attr("fill", lineData.color)
        .style("opacity", 0);

      const text = svg
        .append("text")
        .attr("fill", "#fff") // 文本颜色
        .attr("font-size", "12px")
        .style("opacity", 0);

      return { circle, rect, text };
    });

    // 初始化时将圆点设置在最后一个数据点的位置
    const lastDataPoint = data[0]?.history[data[0]?.history.length - 1];
    const lastXValue = x(new Date(lastDataPoint?.t * 1000));
    const lastYValue = y(lastDataPoint?.p);

    focusElements.forEach(focusElement => {
      focusElement.circle.style("opacity", 1).attr("cx", lastXValue).attr("cy", lastYValue);
    });

    // 添加鼠标事件监听器
    svg
      .append("rect")
      .attr("width", width)
      .attr("height", height)
      .style("fill", "none")
      .style("pointer-events", "all")
      .on("mousemove", function (event) {
        const [mouseX] = d3.pointer(event);
        const xDate = x.invert(mouseX);
        timeLabel.style("opacity", 1); // 展示时间标签
        // 更新时间标签
        updateVerticalLine(mouseX, d3.timeFormat("%b %d, %Y %-I:%M %p")(xDate));

        data.forEach((lineData, index) => {
          if (!lineData || !lineData.history || lineData.history.length === 0) {
            return;
          }
          const closestData = lineData.history.reduce((a, b) => {
            return Math.abs(b.t * 1000 - xDate.getTime()) < Math.abs(a.t * 1000 - xDate.getTime()) ? b : a;
          });
          if (!closestData) {
            return;
          }

          const focusElement = focusElements[index];
          if (!focusElement) {
            return;
          }
          focusElement.circle
            .attr("cx", x(new Date(closestData.t * 1000)))
            .attr("cy", y(closestData.p))
            .style("opacity", 1);

          const textContent = `${lineData.name}: ${(closestData.p * 100).toFixed(2)}%`;
          setCurrentY(closestData.p);
          setCurrentYDiff(closestData.p - lastPValue);

          focusElement.text
            .attr("x", x(new Date(closestData.t * 1000)) + 10) // 文本位置在方块内
            .attr("y", y(closestData.p) + 4) // 文本位置在方块内垂直居中
            .text(textContent)
            .style("opacity", 1);

          const textWidth = focusElement.text.node()?.getBBox().width;
          if (textWidth == null) {
            return;
          }

          focusElement.rect
            .attr("x", x(new Date(closestData.t * 1000)) + 5) // 方块位置在圆点右侧
            .attr("y", y(closestData.p) - 10) // 方块位置在圆点垂直居中
            .attr("width", textWidth + 10) // 方块宽度根据文本宽度调整
            .attr("height", 20) // 方块高度
            .attr("rx", 5) // 圆角半径
            .attr("ry", 5) // 圆角半径
            .style("opacity", 1);
        });
      })
      .on("mouseout", () => {
        // 获取数据集中最后一个数据点的位置
        const lastDataPoint = data[0]?.history[data[0]?.history.length - 1];
        const lastXValue = x(new Date(lastDataPoint?.t * 1000));
        const lastYValue = y(lastDataPoint.p);

        // 更新 circle 的位置和透明度
        focusElements.forEach(focusElement => {
          focusElement.circle.style("opacity", 1).attr("cx", lastXValue).attr("cy", lastYValue);
        });

        // 更新当前 y 值为最后一个数据点的 y 值
        setCurrentY(lastDataPoint.p);

        // 隐藏时间标签
        timeLabel.style("opacity", 0);

        // 隐藏其他元素
        focusElements.forEach(focusElement => {
          focusElement.rect.style("opacity", 0);
          focusElement.text.style("opacity", 0);
        });
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return (
    <div>
      <div className="w-full flex justify-between gap-3">
        <div className="flex flex-wrap flex-col items-start font-medium text-lg">
          <div className="text-gray-500">{data[0]?.name}</div>
          {currentY && (
            <div className="text-2xl text-blue-600 font-bold">
              {(currentY * 100).toFixed(0)}% chance
              <span className={`text-medium  ml-1 ${currentYDiff > 0 ? "text-green-500" : "text-red-500"}`}>
                {currentYDiff > 0 ? (
                  <ArrowUp className="inline-block size-4 mr-1" />
                ) : (
                  <ArrowDown className="inline-block size-4 mr-1" />
                )}
                {(currentYDiff * 100).toFixed(0)}%
              </span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2 mr-2">
          <Image className="h-6 w-6 rounded-full" src="/logo.png" alt="" width={32} height={32} />
          <span className="text-gray-300 text-2xl font-bold">PredictOne</span>
        </div>
      </div>
      <svg ref={svgRef} className="w-full" height="240"></svg>
    </div>
  );
};

export default LineChart;
