import React, { useEffect, useState } from "react";
import { getGraphHistoryData } from "@/api/markets/history";
import { ArrowRightLeft } from "lucide-react";
import TimeRangeSelector from "~~/components/details/lineChart/TimeRangeSelector";
import Graph<PERSON>hart from "~~/components/details/marketItem/GraphChart";

const Graph = (props: any) => {
  const { currentRewards } = props;
  const [historyData, setHistoryData] = useState<any>([]);
  const [isYesOutCome, setIsYesOutCome] = useState<any>(null);
  const [timeRange, setTimeRange] = useState<string>("ALL");
  const colorList = ["blue", "red"];
  const { tokens } = currentRewards?.data[0] || "";

  useEffect(() => {
    if (!tokens) return;
    const tokenId = isYesOutCome ? tokens[0].token_id : tokens[1].token_id;
    const name = isYesOutCome ? tokens[0].outcome : tokens[1].outcome;
    const color = isYesOutCome ? colorList[0] : colorList[1];
    getGraphHistoryData(tokenId, timeRange).then(res => setHistoryData([{ ...res.data, name: name, color: color }]));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentRewards, timeRange, isYesOutCome]);

  return (
    historyData && (
      <div className="w-full flex flex-col">
        <GraphChart data={historyData} />
        <div className="flex items-center justify-between">
          <TimeRangeSelector timeRange={timeRange} setTimeRange={setTimeRange} />
          <ArrowRightLeft className="size-4 cursor-pointer" onClick={() => setIsYesOutCome(!isYesOutCome)} />
        </div>
      </div>
    )
  );
};

export default Graph;
