import React, { useCallback, useEffect, useMemo, useState } from "react";
import useBtnList from "@/hooks/scaffold-pdone/useBtnList";
import { useWebSocketBooksList, useWebSocketLastTradePrice } from "@/hooks/useWebSocket";
import { formatDynamicButtonPrice, formatGameButtonPrice } from "@/utils";
import { Button } from "@heroui/react";
import { ChevronDown, ChevronUp } from "lucide-react";
import MarketItem from "~~/components/details/marketItem/MarketItem";
import TableHead from "~~/components/details/marketItem/TableHead";

const tableHeaders = [
  { width: "40%", label: "OUTCOME" },
  { width: "20%", label: "% CHANCE" },
  { width: "40%", label: "" },
];

const MarketsList = (props: any) => {
  const {
    marketsDataList,
    currentSelectedItem,
    selectedItemId,
    setSelectedItemId,
    selectedButtonType,
    setSelectedButtonType,
    setIsShowOrderCardMobile,
    setBtnPriceData,
    isSingleSidedTrading,
  } = props;

  const [showAll, setShowAll] = useState(false);
  const [lastTradePriceMap, setLastTradePriceMap] = useState<Record<string, { price: number; side: string }>>({});

  const handleToggleShowAll = useCallback(() => setShowAll(prevShowAll => !prevShowAll), []);

  const handleButtonClick = useCallback(
    (itemId: string, buttonType: string) => {
      setSelectedItemId(itemId);
      setSelectedButtonType(buttonType);
    },
    [setSelectedItemId, setSelectedButtonType],
  );

  const handleItemClick = useCallback(
    (itemId: string) => {
      setSelectedItemId((prevItemId: any) => (prevItemId === itemId ? null : itemId));
      setIsShowOrderCardMobile(true);
    },
    [setSelectedItemId, setIsShowOrderCardMobile],
  );

  const itemsToUse = useMemo(() => {
    const items = showAll ? marketsDataList : marketsDataList.slice(0, 4);
    return items;
  }, [showAll, marketsDataList]);

  const { btnDataList, getMatchingBook, setBtnDataList } = useBtnList(itemsToUse, selectedButtonType, selectedItemId);

  // 为单边交易创建专门的 getMatchingBook 函数
  const getMatchingBookForSingleSided = useCallback(
    (qid: number) => {
      const matchingData = btnDataList.find(data => data.token_id === qid);

      if (!matchingData) {
        return null;
      }

      // 单边交易总是返回 yesBookData，但如果 yesBookData 为空，也检查 noBookData
      return matchingData.yesBookData || matchingData.noBookData;
    },
    [btnDataList],
  );

  // websocket 图表数据
  const assetsIds = useMemo(() => {
    let tokenIdsArray;

    if (isSingleSidedTrading) {
      // 单边交易：只订阅第一个token ID（类似Sport页面）
      tokenIdsArray = itemsToUse
        .map(
          (item: { question_market: { clob_token_ids: string[] } }) => item.question_market.clob_token_ids?.[0] || "",
        )
        .filter(Boolean);
    } else {
      // 双边交易：订阅所有token IDs
      tokenIdsArray = itemsToUse
        .map((item: { question_market: { clob_token_ids: string[] } }) => item.question_market.clob_token_ids || [])
        .filter((tokenIds: any) => tokenIds && tokenIds.length > 0)
        .flat();
    }

    return tokenIdsArray;
  }, [itemsToUse, isSingleSidedTrading]);

  // 使用 WebSocket 数据
  useWebSocketBooksList(`${process.env.NEXT_PUBLIC_WSS}/ws`, assetsIds, setBtnDataList);

  // 使用 WebSocket 数据
  useWebSocketLastTradePrice(`${process.env.NEXT_PUBLIC_WSS}/ws`, assetsIds, (priceDataList: any[]) => {
    const updatedPriceMap: Record<string, { price: number; side: string }> = {};
    priceDataList.forEach(({ asset_id, price, side }) => {
      updatedPriceMap[asset_id] = { price: parseFloat(price), side };
    });
    setLastTradePriceMap(updatedPriceMap);
  });

  // 同步WebSocket数据到OrderCard - 当选中的市场或WebSocket数据变化时更新价格
  useEffect(() => {
    if (selectedItemId && setBtnPriceData) {
      // 获取当前选中市场的WebSocket数据
      const selectedMarketData = btnDataList.find(data => data.token_id === Number(selectedItemId));

      if (selectedMarketData) {
        if (isSingleSidedTrading) {
          // 单边交易：使用 yesBookData 或 noBookData（取有数据的那个）
          const bookData = selectedMarketData.yesBookData || selectedMarketData.noBookData;

          if (bookData) {
            const { YesPrice } = formatGameButtonPrice(selectedMarketData, currentSelectedItem);

            setBtnPriceData({
              YesPrice,
              NoPrice: 0, // 单边交易不需要 No 价格
            });
          }
        } else {
          // 双边交易：根据选中的按钮类型获取对应的订单簿数据
          const bookData =
            selectedButtonType === "yes" ? selectedMarketData.yesBookData : selectedMarketData.noBookData;

          if (bookData) {
            // 计算当前选中市场的价格
            const { YesPrice, NoPrice } = formatDynamicButtonPrice(bookData, currentSelectedItem, selectedButtonType);

            // 更新 OrderCard 的按钮价格数据
            setBtnPriceData({
              YesPrice,
              NoPrice,
            });
          }
        }
      }
    }
  }, [btnDataList, selectedItemId, selectedButtonType, currentSelectedItem, setBtnPriceData, isSingleSidedTrading]);

  return (
    <div className="w-full flex flex-col">
      <TableHead headers={tableHeaders} />
      <div className="w-full flex flex-col gap-2">
        {itemsToUse.map((item: any) => {
          // 根据交易类型选择合适的 getMatchingBook 函数
          const matchingBook = isSingleSidedTrading
            ? getMatchingBookForSingleSided(item.question_market.id)
            : getMatchingBook(item.question_market.id);

          return (
            item?.question_market?.clob_token_ids && (
              <MarketItem
                item={item}
                key={item.question_market.id}
                selectedButtonType={selectedButtonType}
                selectedItemId={Number(selectedItemId)}
                onButtonClick={handleButtonClick}
                onItemClick={handleItemClick}
                bookData={matchingBook}
                isExpanded={itemsToUse.length === 1}
                currentSelectedItem={currentSelectedItem}
                setIsShowOrderCardMobile={setIsShowOrderCardMobile}
                setBtnPriceData={setBtnPriceData}
                lastTradePriceMap={lastTradePriceMap}
                isSingleSidedTrading={isSingleSidedTrading}
              />
            )
          );
        })}

        {marketsDataList.length > 4 && (
          <Button
            onPress={handleToggleShowAll}
            variant="bordered"
            size="md"
            radius="md"
            className="flex flex-center font-semibold mb-4"
          >
            {showAll ? "Hide more" : "Show more"}
            {showAll ? <ChevronUp className="size-4 ml-2" /> : <ChevronDown className="size-4 ml-2" />}
          </Button>
        )}
      </div>
    </div>
  );
};

export default MarketsList;
