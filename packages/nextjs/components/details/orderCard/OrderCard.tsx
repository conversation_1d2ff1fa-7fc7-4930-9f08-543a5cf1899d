import React, { useCallback, useEffect, useRef, useState } from "react";
import useF<PERSON>zen from "@/hooks/useFrozen";
import { useGlobalBalance } from "@/hooks/useGlobalBalance";
import { useUserAddress } from "@/hooks/useUserAddress";
import { base64DecodeByLanguage, getItem } from "@/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@heroui/popover";
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Divider,
  Image,
  Modal,
  ModalBody,
  ModalContent,
  Tooltip,
} from "@heroui/react";
import { t } from "i18next";
import { ChevronUp, TrendingUp } from "lucide-react";
import OrderCardContent from "~~/components/details/orderCard/OrderCardContent";
import OrderCardTabs from "~~/components/details/orderCard/OrderCardTabs";
import OrderSimpleYesCardContent from "~~/components/details/orderCard/OrderSimpleYesCardContent";
import { useGlobalState } from "~~/services/store/store";

const OrderCard = (props: any) => {
  const {
    clobApis,
    cardType = "bilateral", // bilateral, single-sided
    marketsDataList,
    selectedItemId,
    selectedButtonType,
    isShowPreBuyModal,
    setSelectedButtonType,
    setIsShowPreBuyModal,
    currentSelectedItem,
    setCurrentSelectedItem,
    btnPriceData,
    allMarketsPriceData,
  } = props;

  const { address } = useUserAddress();
  const proxyWallet = getItem(`login_proxyWallet`);

  const { current_language } = useGlobalState().nativeCurrency;
  const { frozenValue, refreshFrozenValue, lockFunds, unlockFunds } = useFrozen(clobApis);
  // 使用全局余额管理，订单组件减少自动刷新，但确保有数据和手动刷新功能
  const { balance, refreshBalance: refreshBalanceValue } = useGlobalBalance(proxyWallet, {
    enableBlockWatch: false, // 不启用区块监听
    enableAutoRefresh: false, // 不启用定时刷新
    skipInitialFetch: false, // 允许初始获取，确保有数据显示
  });

  const [isShowPop, setIsShowPop] = useState(false);
  const [currentCardData, setCurrentCardData] = useState<any>({});
  const [groupItemTitle, setGroupItemTitle] = useState<string>("");
  const [selectedKey, setSelectedKey] = useState(new Set(["Limit"]));
  const [isMobile, setIsMobile] = useState(false);
  const [isMobileModalOpen, setIsMobileModalOpen] = useState(false);
  const [currentSelectedMarket, setCurrentSelectedMarket] = useState<any>({});

  // 添加防止频繁重渲染的 ref
  const isUpdatingRef = useRef(false);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  useEffect(() => {
    if (cardType === "single-sided" && currentSelectedMarket?.question) {
      // 对于 single-sided 类型，使用当前选中的 market 作为标题
      setGroupItemTitle(base64DecodeByLanguage(currentSelectedMarket.question));
    } else if (currentCardData?.question) {
      // 对于 bilateral 类型
      setGroupItemTitle(base64DecodeByLanguage(currentCardData?.question));
    }
  }, [current_language, currentSelectedMarket, currentCardData, cardType]);

  useEffect(() => {
    if (cardType === "single-sided") {
      // 对于 single-sided 类型，提取所有 question_market 作为数组，卡片按钮为每个 question_market 的 title 形式, 例如 球队A、球队B
      const allQuestionMarkets = marketsDataList.map((item: any) => item.question_market);
      setCurrentCardData(allQuestionMarkets);

      // 同时设置当前选中的市场
      if (selectedItemId) {
        const selectedMarket = allQuestionMarkets.find((market: any) => market.id === Number(selectedItemId));
        setCurrentSelectedMarket(selectedMarket || allQuestionMarkets[0] || {});
      } else if (allQuestionMarkets.length > 0) {
        setCurrentSelectedMarket(allQuestionMarkets[0]);
      }
    } else if (selectedItemId) {
      // 对于 normal 类型，匹配单个 question_market,卡片按钮为 Yes/No 形式
      const matchingMarket = marketsDataList.find((item: any) => item.question_market.id === Number(selectedItemId));
      setCurrentCardData(matchingMarket?.question_market);
      setCurrentSelectedMarket(matchingMarket?.question_market || {});
    }
  }, [selectedItemId, marketsDataList, cardType]);

  const orderCardProps = {
    address,
    currentSelectedItem,
    currentCardData,
    selectedButtonType,
    setSelectedButtonType,
    selectedKey,
    balance: balance ? Number(balance) : 0,
    proxyWallet,
    isShowPreBuyModal,
    setIsShowPreBuyModal,
    groupItemTitle,
    btnPriceData,
    clobApis,
    frozenValue,
    refreshFrozenValue,
    lockFunds,
    unlockFunds,
    refreshBalanceValue,
  };

  const orderGameCardProps = {
    ...orderCardProps,
    currentSelectedMarket: currentSelectedMarket,
    allMarketsPriceData: allMarketsPriceData,
  };

  // 模态框控制函数 - 使用 useCallback 稳定引用
  const handleOpenMobileModal = useCallback(() => {
    if (!isUpdatingRef.current) {
      setIsMobileModalOpen(true);
    }
  }, []);

  const handleCloseMobileModal = useCallback(() => {
    setIsMobileModalOpen(false);
  }, []);

  // 移动端浮动按钮
  const MobileFloatingButton = (
    <div className="fixed bottom-16 left-4 right-4 z-50 md:hidden">
      <Button
        className="w-full h-14 bg-blue-600 text-white font-semibold text-lg shadow-lg rounded-xl"
        onPress={handleOpenMobileModal}
        startContent={<TrendingUp className="w-5 h-5" />}
        endContent={<ChevronUp className="w-5 h-5" />}
      >
        {currentSelectedItem}{" "}
        {selectedButtonType ? selectedButtonType.charAt(0).toUpperCase() + selectedButtonType.slice(1) : ""}
      </Button>
    </div>
  );

  // 移动端半屏交易模态框
  const MobileModal = (
    <Modal
      isOpen={isMobileModalOpen}
      onClose={handleCloseMobileModal}
      placement="bottom"
      size="2xl"
      isDismissable={true}
      isKeyboardDismissDisabled={false}
      hideCloseButton={false}
      classNames={{
        base: "!m-0 !max-w-full !h-[70vh] !max-h-[70vh] !rounded-t-2xl !rounded-b-none",
        wrapper: "!items-end",
        backdrop: "bg-black/50",
        closeButton: "top-4 right-4 z-50 bg-white/80 hover:bg-white",
      }}
      motionProps={{
        variants: {
          enter: {
            y: 0,
            transition: {
              duration: 0.3,
              ease: "easeOut",
            },
          },
          exit: {
            y: "100%",
            transition: {
              duration: 0.2,
              ease: "easeIn",
            },
          },
        },
      }}
    >
      <ModalContent className="!rounded-t-2xl !rounded-b-none !mb-0 overflow-hidden">
        <ModalBody className="px-4 pb-8 pt-4 overflow-y-auto" onClick={e => e.stopPropagation()}>
          <div className="space-y-4">
            {/* 标题区域 */}
            <div className="flex h-10 gap-2 items-center">
              <Image
                alt="/"
                height={40}
                width={40}
                radius="full"
                src={cardType === "single-sided" ? currentSelectedMarket?.icon : currentCardData?.icon}
                className="rounded-full object-cover size-10 flex-shrink-0"
              />
              <Tooltip content={groupItemTitle}>
                <span className="text-md font-bold line-clamp-2 flex-1">{groupItemTitle}</span>
              </Tooltip>
            </div>

            {/* 选项卡 */}
            <OrderCardTabs
              currentSelectedItem={currentSelectedItem}
              setCurrentSelectedItem={setCurrentSelectedItem}
              selectedKey={selectedKey}
              setSelectedKey={setSelectedKey}
            />

            <Divider />

            <div className="pb-4">
              {cardType === "single-sided" ? (
                <OrderSimpleYesCardContent {...orderGameCardProps} />
              ) : (
                <OrderCardContent {...orderCardProps} />
              )}
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );

  // 如果没有数据，不渲染任何内容
  if (cardType === "single-sided") {
    if (!Array.isArray(currentCardData) || currentCardData.length === 0 || !currentSelectedMarket?.clob_token_ids) {
      return null;
    }
  } else if (!currentCardData?.clob_token_ids) {
    return null;
  }

  // 移动端渲染浮动按钮和模态框
  if (isMobile) {
    return (
      <>
        {MobileFloatingButton}
        {MobileModal}
      </>
    );
  }

  // 桌面端返回完整的卡片（保持原有逻辑）
  return (
    <Card className="max-w-[400px] bg-white">
      <CardHeader className="flex flex-col px-6 py-4 pb-0 gap-2 items-start justify-between">
        <div className="flex h-10 gap-2 items-center ">
          <Image
            alt="/"
            height={40}
            width={40}
            radius="full"
            src={cardType === "single-sided" ? currentSelectedMarket?.icon : currentCardData?.icon}
            className="rounded-full object-cover size-10 flex-shrink-0"
          />

          <Tooltip content={groupItemTitle}>
            <span className="max-w-[240px] text-md font-bold line-clamp-1">{groupItemTitle}</span>
          </Tooltip>
        </div>

        <OrderCardTabs
          currentSelectedItem={currentSelectedItem}
          setCurrentSelectedItem={setCurrentSelectedItem}
          selectedKey={selectedKey}
          setSelectedKey={setSelectedKey}
        />
      </CardHeader>

      <Divider />

      <CardBody className="px-6 my-2">
        {cardType === "single-sided" ? (
          <OrderSimpleYesCardContent {...orderGameCardProps} />
        ) : (
          <OrderCardContent {...orderCardProps} />
        )}

        <Popover placement="top" isOpen={isShowPop} disableAnimation>
          <PopoverTrigger onMouseEnter={() => setIsShowPop(true)} onMouseLeave={() => setIsShowPop(false)}>
            <span className="max-w-[300px] text-gray-500 text-sm font-semibold group mt-3">
              {t("Event_Order_PopText1")}
              <span className="underline ml-1 cursor-pointer">{t("Event_Order_PopText2")}</span>
            </span>
          </PopoverTrigger>
          <PopoverContent>
            <div className="px-1 py-2">
              <div className="text-small w-[300px]">{t("Event_Order_PopText3")}</div>
            </div>
          </PopoverContent>
        </Popover>
      </CardBody>
    </Card>
  );
};

export default OrderCard;
