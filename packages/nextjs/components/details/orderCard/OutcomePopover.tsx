import React, { useState } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@heroui/popover";
import { CircleAlert } from "lucide-react";

const PriceInfoPopover: React.FC = () => {
  const [popState, setPopState] = useState(false);

  return (
    <Popover placement="bottom" className="mt-4" isOpen={popState} disableAnimation>
      <PopoverTrigger onMouseEnter={() => setPopState(true)} onMouseLeave={() => setPopState(false)}>
        <CircleAlert className="size-4 text-gray-500" />
      </PopoverTrigger>
      <PopoverContent>
        <div className="px-1 py-2 text-small w-64">
          <div className="font-semibold">What do the prices mean?</div>
          <div>
            Prices reflect odds of 64% Yes and 36% No. When it ends, the correct outcome will be $1 and incorrect $0.
          </div>
          <div className="font-semibold mt-2">{"Why don't they add up to 100?"}</div>
          <div>Slight offsets happen due to market uncertainty and other price factors.</div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default PriceInfoPopover;
