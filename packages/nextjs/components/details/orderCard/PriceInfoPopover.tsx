import React, { useState } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@heroui/react";

interface PricePopoverProps {
  sharesValue: number;
  price: number;
  american: number;
  decimal: number;
}

const PriceInfoPopover: React.FC<PricePopoverProps> = ({ sharesValue, price, american, decimal }) => {
  const [pricePopState, setPricePopState] = useState(false);

  return (
    <Popover placement="top" isOpen={pricePopState} disableAnimation>
      <PopoverTrigger onMouseEnter={() => setPricePopState(true)} onMouseLeave={() => setPricePopState(false)}>
        <span className="border-b border-dashed border-blue-600">${((price * sharesValue) / 100).toFixed(2)}</span>
      </PopoverTrigger>
      <PopoverContent>
        <div className="px-1 py-2 min-w-[160px] flex-col items-center text-gray-500 text-medium font-medium">
          <div className="flex items-center justify-between">
            <div>Price</div>
            <div className="font-semibold text-black font-sans">{price}¢</div>
          </div>
          <div className="flex items-center justify-between">
            <div>American</div>
            <div className="font-semibold text-black">{american >= 0 ? `+${american}` : `-${american}`}</div>
          </div>
          <div className="flex items-center justify-between">
            <div>Decimal</div>
            <div className="font-semibold text-black">{decimal}</div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default PriceInfoPopover;
