import React from "react";
import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from "@heroui/react";
import { ChevronDown } from "lucide-react";

const OrderCardTabs = (props: any) => {
  const { currentSelectedItem, setCurrentSelectedItem, selectedKey, setSelectedKey } = props;

  const handleSelectionChange = (keys: any) => {
    setSelectedKey(new Set([keys.currentKey || ""]));
  };

  return (
    <div className="flex w-full h-9 relative items-start justify-between">
      <div className="flex gap-4 text-md font-semibold">
        <span
          className={`cursor-pointer flex relative justify-center ${
            currentSelectedItem === "Buy" ? "text-blue-600" : ""
          }`}
          onClick={() => setCurrentSelectedItem("Buy")}
        >
          <div>Buy</div>
          {currentSelectedItem === "Buy" && <div className="absolute bottom-[-12px] w-8 h-0.5 bg-blue-600" />}
        </span>
        <span
          className={`cursor-pointer flex relative justify-center ${
            currentSelectedItem === "Sell" ? "text-blue-600" : ""
          }`}
          onClick={() => setCurrentSelectedItem("Sell")}
        >
          <div>Sell</div>
          {currentSelectedItem === "Sell" && <div className="absolute bottom-[-12px] w-8 h-0.5 bg-blue-600" />}
        </span>
      </div>

      <Dropdown className="mr-2">
        <DropdownTrigger className="font-semibold cursor-pointer">
          <div className="flex items-center">
            {selectedKey}
            <ChevronDown className="size-4 ml-2" />
          </div>
        </DropdownTrigger>
        <DropdownMenu
          variant="flat"
          disallowEmptySelection
          selectionMode="single"
          selectedKeys={selectedKey}
          onSelectionChange={handleSelectionChange}
        >
          <DropdownItem key="Market">Market</DropdownItem>
          <DropdownItem key="Limit">Limit</DropdownItem>
          {/* <DropdownItem key="AMM">AMM</DropdownItem> */}
        </DropdownMenu>
      </Dropdown>
    </div>
  );
};

export default OrderCardTabs;
