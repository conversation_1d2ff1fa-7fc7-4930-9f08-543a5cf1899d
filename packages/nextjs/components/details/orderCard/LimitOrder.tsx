import React, { useCallback, useEffect, useState } from "react";
import NumberInput from "./NumberInput";
import OutcomePopover from "./OutcomePopover";
import PriceInfoPopover from "./PriceInfoPopover";
import { OrderType, calculateOrderPayment, roundToInteger, validateOrder } from "@/utils";
import { Button, Card, CardBody, Chip, Spinner } from "@heroui/react";
import { RefreshCcw, Settings } from "lucide-react";
import { useTranslation } from "react-i18next";

const LimitOrder = (props: any) => {
  const { t } = useTranslation();
  const {
    currentSelectedItem,
    currentCardData,
    selectedButtonType,
    availableBalance,
    orderButtonState,
    setOrderButtonState,
    orderTradeTokenId,
    YesPrice,
    NoPrice,
    handleOrder,
    checkRegion,
    calculateMaxShares,
    refreshAll,
    showInsufficientBalanceNotification,
    clobApis,
    orderState,
    // 使用传递过来的共通组件
    YesNoButtonGroup,
    TradeButton,
    // 单边交易验证函数
    validateSingleSidedLimitOrder,
  } = props;

  // LimitOrder 特有状态
  const [price, setPrice] = useState<number>(0);
  const [sharesValue, setSharesValue] = useState<number>(0);
  const [selectedPrice, setSelectedPrice] = useState<number>(0);
  const [priceError, setPriceError] = useState<string>("");
  const [sharesError, setSharesError] = useState<string>("");
  const [isUserInput, setIsUserInput] = useState(false);

  const { order_min_size, order_price_min_tick_size } = currentCardData;

  // LimitOrder 特有逻辑
  const [cardTemporaryData, setCardTemporaryData] = useState({
    payment: 0,
    potential: "0",
    potentialPersent: "0",
    american: "0",
    decimal: "0",
  });

  // LimitOrder 特有的订单处理
  const onClickHandleLimitOrder = async () => {
    setOrderButtonState("loading");

    try {
      const regionAllowed = await checkRegion();
      if (!regionAllowed) {
        setOrderButtonState("none");
        return;
      }

      // 使用单边交易验证函数（如果可用）或双边交易验证函数
      if (validateSingleSidedLimitOrder) {
        const validation = validateSingleSidedLimitOrder(price, sharesValue, order_min_size);
        if (validation.hasError) {
          // 根据错误类型设置相应的错误信息
          if (validation.errorMessage.includes("price") || validation.errorMessage.includes("Price")) {
            setPriceError(validation.errorMessage);
            setSharesError("");
          } else {
            setSharesError(validation.errorMessage);
            setPriceError("");
          }
          setOrderButtonState("none");
          return;
        } else {
          setPriceError("");
          setSharesError("");
        }
      } else {
        // 双边交易的原有验证逻辑
        if (validateOrder(price, sharesValue, order_min_size, currentSelectedItem, setPriceError, setSharesError)) {
          setOrderButtonState("none");
          return;
        }
      }

      if (currentSelectedItem === "Sell") {
        const maxAvailableShares = calculateMaxShares(
          selectedButtonType,
          currentSelectedItem,
          orderTradeTokenId,
          price,
        );

        if (sharesValue > maxAvailableShares) {
          showInsufficientBalanceNotification(sharesValue, maxAvailableShares);
          setOrderButtonState("none");
          return;
        }
      }

      if (currentSelectedItem === "Buy") {
        const paymentInUSDC = cardTemporaryData.payment / 1000000;
        if (paymentInUSDC > availableBalance) {
          showInsufficientBalanceNotification(paymentInUSDC, availableBalance);
          setOrderButtonState("none");
          return;
        }
      }

      const postParams = {
        shares: sharesValue,
        payment: cardTemporaryData.payment,
        side: currentSelectedItem,
        tokenId: orderTradeTokenId,
        price: price,
        type: OrderType.GTC,
      };

      await handleOrder(postParams);

      // 订单成功后额外刷新数据，确保 UI 及时更新
      console.log("🎉 LimitOrder: Order successful, refreshing data...");
      await refreshAll(true);

      // 如果是卖单，启动额外的轮询监控
      if (currentSelectedItem === "Sell") {
        console.log("🔄 LimitOrder: Starting sell order monitoring...");
      }
    } catch (error) {
      console.error("Limit订单过程中发生错误:", error);
      setOrderButtonState("none");
    }
  };

  // LimitOrder 特有的 useEffect
  useEffect(() => {
    if (selectedButtonType === "yes") {
      if (!isUserInput) {
        setSelectedPrice(Number(YesPrice));
      }
    }
    if (selectedButtonType === "no") {
      if (!isUserInput) {
        setSelectedPrice(Number(NoPrice));
      }
    }
  }, [selectedButtonType, YesPrice, NoPrice, isUserInput]);

  useEffect(() => {
    if (price > 0 && sharesValue > 0) {
      setCardTemporaryData({
        payment: calculateOrderPayment(price, sharesValue),
        potential: sharesValue.toFixed(2),
        potentialPersent: roundToInteger(((sharesValue * 100 - price * sharesValue) * 100) / (price * sharesValue)),
        american: ((100 - price) / (price / 100)).toFixed(2),
        decimal: (1 / (price / 100)).toFixed(2),
      });
    }
  }, [price, sharesValue]);

  useEffect(() => {
    setPrice(selectedPrice);
  }, [selectedPrice]);

  useEffect(() => {
    if (!isUserInput) {
      setPrice(selectedPrice);
    }
  }, [selectedPrice, isUserInput]);

  useEffect(() => {
    setIsUserInput(false);
  }, [selectedButtonType, currentSelectedItem]);

  const handleCompleteMaxShares = useCallback(async () => {
    try {
      // 如果是卖单且数据未准备好，先刷新数据
      if (currentSelectedItem === "Sell" && (orderState.isInitializing || orderState.error)) {
        // 避免在刷新过程中重复刷新
        if (!orderState.isRefreshing) {
          await refreshAll(true);
          // 等待状态更新
          await new Promise(resolve => setTimeout(resolve, 500));
        } else {
          // 如果正在刷新，等待刷新完成
          console.log("⏳ LimitOrder: Waiting for ongoing refresh...");
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      const maxShares = calculateMaxShares(selectedButtonType, currentSelectedItem, orderTradeTokenId, price);

      if (maxShares >= 0) {
        setSharesValue(maxShares);
      }
    } catch (error) {
      console.error("❌ LimitOrder: Error calculating max shares:", error);
    }
  }, [calculateMaxShares, selectedButtonType, currentSelectedItem, orderTradeTokenId, price, refreshAll, orderState]);

  // 计算可卖份数
  const getAvailableShares = useCallback(() => {
    if (currentSelectedItem !== "Sell" || orderState.isInitializing || orderState.error) {
      return 0;
    }

    return calculateMaxShares(selectedButtonType, currentSelectedItem, orderTradeTokenId, 1);
  }, [
    currentSelectedItem,
    orderState.isInitializing,
    orderState.error,
    calculateMaxShares,
    selectedButtonType,
    orderTradeTokenId,
  ]);

  // 获取显示内容
  const getAvailableDisplay = useCallback(() => {
    if (currentSelectedItem === "Buy") {
      return clobApis ? `$${availableBalance.toFixed(2)}` : "$0";
    } else {
      // 对于 Sell，只有在初始化时显示 loading，刷新时显示之前的数据
      if (orderState.isInitializing) {
        return <Spinner size="sm" color="default" variant="dots" />;
      }
      if (orderState.error) {
        return "—";
      }
      const shares = getAvailableShares();
      return <span>{`${shares.toFixed(2)}`}</span>;
    }
  }, [
    currentSelectedItem,
    clobApis,
    availableBalance,
    orderState.isInitializing,
    orderState.error,
    getAvailableShares,
  ]);

  return (
    <div className="flex flex-col space-y-3">
      {/* Yes/No 选择按钮 - 使用共通组件 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-base font-medium text-gray-900 mr-1">{t("Event_Order_Outcome")}</span>
            <OutcomePopover />
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => {
                console.log("🔄 Manual refresh triggered");
                refreshAll(true);
              }}
              className="flex-center size-4 bg-white border rounded-md hover:bg-gray-50 cursor-pointer"
              title="Refresh data"
            >
              <RefreshCcw className="size-3 text-gray-500" />
            </button>
            <Settings className="size-4 text-gray-500" />
          </div>
        </div>

        {/* 使用共通的 YesNoButtonGroup 组件 */}
        <YesNoButtonGroup />
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-base font-medium text-gray-900">{t("Event_Order_LimitPrice")}</span>
          <Chip size="sm" variant="flat" className="text-xs text-gray-500 bg-gray-100">
            <div className="flex items-center gap-1">
              {t("Event_Order_Available")}: {getAvailableDisplay()}
            </div>
          </Chip>
        </div>

        <NumberInput
          value={price}
          onChange={value => {
            setPrice(value);
            setIsUserInput(true);
          }}
          step={1}
          unit="¢"
          type="price"
          max={100}
          decimalLimit={order_price_min_tick_size}
        />
        {priceError && <div className="text-red-500 text-xs mt-1">{priceError}</div>}
      </div>

      {/* 份额输入 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="text-base font-medium text-gray-900">{t("Event_Order_Share")}</div>
          <Button
            size="sm"
            variant="bordered"
            className="text-gray-700 bg-white border-gray-300 hover:bg-gray-50 text-xs h-7"
            onPress={handleCompleteMaxShares}
            isDisabled={currentSelectedItem === "Sell" && (orderState.isInitializing || orderState.error)}
            isLoading={currentSelectedItem === "Sell" && orderState.isRefreshing}
          >
            {currentSelectedItem === "Buy" ? "Max" : orderState.isRefreshing ? "" : "Max"}
          </Button>
        </div>

        <NumberInput value={sharesValue} onChange={setSharesValue} step={1} type="shares" max={999999999.99} />
        {sharesError && <div className="text-red-500 text-xs mt-1">{sharesError}</div>}
      </div>

      {/* 订单信息显示 */}
      {price > 0 && sharesValue > 0 && (
        <Card className="bg-gray-50">
          <CardBody className="space-y-2 p-3">
            <div className="flex items-center justify-between text-xs md:text-sm">
              <span className="text-gray-600">{t("Event_Order_Total")}</span>
              <span className="font-semibold text-blue-600">
                <PriceInfoPopover
                  sharesValue={sharesValue}
                  price={price}
                  american={parseFloat(cardTemporaryData?.american)}
                  decimal={parseFloat(cardTemporaryData?.decimal)}
                />
              </span>
            </div>
            <div className="flex items-center justify-between text-xs md:text-sm">
              <span className="text-gray-600">{t("Event_Order_PotentialReturn")}</span>
              <span className="font-semibold text-green-600">
                ${cardTemporaryData?.potential} ({cardTemporaryData?.potentialPersent}%)
              </span>
            </div>
          </CardBody>
        </Card>
      )}
      {/* 下单按钮 - 使用共通的 TradeButton 组件 */}
      <TradeButton
        isDisabled={orderButtonState === "loading"}
        isLoading={orderButtonState === "loading"}
        loadingText="Processing..."
        onPress={onClickHandleLimitOrder}
      >
        {`${currentSelectedItem} at Limit Price`}
      </TradeButton>
    </div>
  );
};

export default LimitOrder;
