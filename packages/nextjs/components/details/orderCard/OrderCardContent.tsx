import React, { useCallback, useEffect, useRef, useState } from "react";
import TradeNotification from "../tradeNotification/TradeNotification";
import LimitOrder from "./LimitOrder";
import MarketOrder from "./MarketOrder";
import CustomConnectModal from "@/components/login/CustomConnectModal";
import NotificationCard from "@/components/other/NotificationCard";
import useOrderState from "@/hooks/orders/useOrderState";
import { useLoginModal } from "@/hooks/useLoginModal";
import { useUserAddress } from "@/hooks/useUserAddress";
import { checkApprovalStatus, checkGeolocation, checkProxyAndCheckApprove } from "@/utils";
import { handleMakeOrder } from "@/utils/signature/order";
import { Button, ButtonGroup, Link, Tooltip } from "@heroui/react";
import { useTranslation } from "react-i18next";
import { useGlobalState } from "~~/services/store/store";

const OrderCardContent = (props: any) => {
  const { t } = useTranslation();
  const { isLoginModalOpen, openLoginModal, closeLoginModal } = useLoginModal();

  const { selectedKey, ...otherProps } = props;
  const {
    address,
    currentSelectedItem,
    currentCardData,
    selectedButtonType,
    setSelectedButtonType,
    balance,
    isShowPreBuyModal,
    setIsShowPreBuyModal,
    groupItemTitle,
    btnPriceData,
    clobApis,
    frozenValue,
    refreshFrozenValue,
    refreshBalanceValue,
    lockFunds,
    unlockFunds,
  } = otherProps;

  const { isConnected } = useUserAddress();
  const SelectType = selectedKey.has("Market") ? "Market" : "Limit";

  // 共通状态
  const [availableBalance, setAvailableBalance] = useState(0);
  const [isRegionAllowed, setIsRegionAllowed] = useState(true);
  const [orderButtonState, setOrderButtonState] = useState<"none" | "loading" | "success">("none");
  const [orderTradeTokenId, setOrderTradeTokenId] = useState("");
  const [showNotification, setShowNotification] = useState(false);
  const [makeOrderStatus, setMakeOrderStatus] = useState<"success" | "failure" | "canceled">("success");
  const [orderError, setOrderError] = useState<string>("");

  const { clob_token_ids } = currentCardData;
  const { YesPrice, NoPrice } = btnPriceData;

  const { address: userAddress } = useUserAddress();
  const { walletType } = useGlobalState();

  const orderState = useOrderState({
    clobApis,
    walletType,
    address: userAddress || address,
    availableBalance: isConnected ? balance - frozenValue : 0,
    tokenIds: clob_token_ids || [],
  });

  const [notification, setNotification] = useState<{
    title: string;
    content?: string;
    footer?: string;
    notiStatus?: "success" | "failure";
  } | null>(null);

  const [tradeNotificationData, setTradeNotificationData] = useState({
    shares: 0,
    price: 0,
  });

  // 监听balance变化，当balance变化时立即刷新frozen值（仅在已登录时）
  const prevBalanceRef = useRef<number>(balance);
  useEffect(() => {
    // 只有在用户已登录时才监听余额变化
    if (!isConnected) {
      prevBalanceRef.current = balance;
      return;
    }

    const prevBalance = prevBalanceRef.current;

    // 如果balance发生了变化，说明有订单被执行了
    if (prevBalance !== balance && prevBalance > 0) {
      console.log(`💰 Balance changed: ${prevBalance} → ${balance}, refreshing frozen value...`);

      // 立即刷新frozen值以确保available balance计算正确
      refreshFrozenValue(clobApis, true)
        .then(() => {
          console.log(`✅ Frozen value refreshed after balance change`);
        })
        .catch((error: any) => {
          console.error(`❌ Failed to refresh frozen value after balance change:`, error);
        });
    }

    // 更新引用值
    prevBalanceRef.current = balance;
  }, [balance, refreshFrozenValue, clobApis, isConnected]);

  // 共通 Effect：更新可用余额，只有在用户已登录时才计算
  useEffect(() => {
    if (isConnected) {
      const newAvailableBalance = balance - frozenValue;
      setAvailableBalance(newAvailableBalance);
    } else {
      // 用户未登录时，不查询余额，设置为0
      setAvailableBalance(0);
    }
  }, [balance, frozenValue, isConnected]);

  // 共通 Effect：更新交易Token ID
  useEffect(() => {
    if (clob_token_ids) {
      if (selectedButtonType === "yes") {
        setOrderTradeTokenId(clob_token_ids[0]);
      }
      if (selectedButtonType === "no") {
        setOrderTradeTokenId(clob_token_ids[1]);
      }
    }
  }, [clob_token_ids, selectedButtonType]);

  // 监听 Buy/Sell 切换，刷新数据以获取最新状态
  const { refreshData, isReady } = orderState;
  useEffect(() => {
    if (isReady) {
      // 强制刷新以确保获取最新数据
      refreshData(true);
    }
  }, [currentSelectedItem, isReady, refreshData]);

  // 共通订单处理逻辑
  const { refreshAfterOrder } = orderState;
  const handleOrder = useCallback(
    async (postParams: any) => {
      if (!isShowPreBuyModal) {
        let lockAmount = 0;
        if (postParams.side === "Buy") {
          lockAmount = postParams.payment / 1000000;
        }

        try {
          const isProxyValid = await checkProxyAndCheckApprove(address, setIsShowPreBuyModal, notificationProps => {
            setNotification(notificationProps);
          });

          if (!isProxyValid) {
            setOrderButtonState("none");
            return;
          }

          const isApprovedAndHasApi = await checkApprovalStatus(address);
          if (isApprovedAndHasApi) {
            // 保存交易数据用于通知
            setTradeNotificationData({
              shares: postParams.shares,
              price: postParams.price || 0,
            });

            await handleMakeOrder(
              postParams,
              setOrderButtonState,
              setShowNotification,
              setMakeOrderStatus,
              setOrderError,
              async () => {
                await refreshFrozenValue(clobApis, true);
                await refreshBalanceValue(true);
                await refreshAfterOrder();
              },
            );
          } else {
            setOrderButtonState("none");
          }
        } catch (error: any) {
          console.error("处理订单时发生错误:", error);
          setOrderButtonState("none");

          // 如果订单失败，释放之前锁定的资金
          if (lockAmount > 0) {
            unlockFunds(lockAmount);
            console.log(`🔓 Funds unlocked due to order failure: ${lockAmount} USDC`);
          }

          // 抛出错误以便 MarketOrder 组件可以捕获并处理
          throw error;
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      address,
      isShowPreBuyModal,
      setIsShowPreBuyModal,
      refreshFrozenValue,
      clobApis,
      refreshAfterOrder,
      lockFunds,
      unlockFunds,
    ],
  );
  // 共通地理位置检查
  const checkRegion = useCallback(async () => {
    try {
      const regionAllowed = await checkGeolocation();
      setIsRegionAllowed(regionAllowed ?? true);
      if (!regionAllowed) {
        setNotification({
          title: t("Normal_Zone_Warning_Title"),
          content: t("Normal_Zone_Warning"),
          notiStatus: "failure",
        });
        return false;
      }
      return true;
    } catch (error) {
      console.error("地理位置检查失败:", error);
      return false;
    }
  }, [t]);

  // 共通按钮点击处理
  const handleButtonClick = useCallback(
    (button: string) => {
      setSelectedButtonType(button);
    },
    [setSelectedButtonType],
  );

  // 显示余额不足通知
  const showInsufficientBalanceNotification = useCallback(
    (required: number, available: number) => {
      if (currentSelectedItem === "Sell") {
        setNotification({
          title: t("Order_Insufficient_Shares_Title"),
          content: t("Order_Insufficient_Shares_Content", {
            required: required.toFixed(0),
            available: available.toFixed(0),
          }),
          notiStatus: "failure",
        });
      } else {
        setNotification({
          title: t("Order_Insufficient_Balance_Title"),
          content: t("Order_Insufficient_Balance_Content", {
            required: required.toFixed(2),
            available: available.toFixed(2),
          }),
          notiStatus: "failure",
        });
      }
    },
    [t, currentSelectedItem],
  );

  // 处理连接钱包
  const handleConnectWallet = useCallback(async () => {
    try {
      openLoginModal();
      console.log("openLoginModal 888 ");
    } catch (error) {
      console.error("打开登录模态框失败:", error);
      setNotification({
        title: t("Wallet_Connection_Failed_Title"),
        content: t("Wallet_Connection_Failed_Content"),
        notiStatus: "failure",
      });
    }
  }, [openLoginModal, t]);

  // 共通的 Yes/No 按钮组件
  const YesNoButtonGroup = () => (
    <ButtonGroup fullWidth size="lg" variant="flat">
      <Button
        className={`h-16 ${
          selectedButtonType === "yes"
            ? "bg-green-600 text-white shadow-lg"
            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
        }`}
        onPress={() => handleButtonClick("yes")}
      >
        <div className="flex flex-col items-center">
          <span className="font-semibold text-medium">Yes</span>
          <span className="font-semibold text-lg">{YesPrice}¢</span>
        </div>
      </Button>

      <Button
        className={`h-16 ${
          selectedButtonType === "no"
            ? "bg-red-600 text-white shadow-lg"
            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
        }`}
        onPress={() => handleButtonClick("no")}
      >
        <div className="flex flex-col items-center">
          <span className="font-semibold text-medium">No</span>
          <span className="font-semibold text-lg">{NoPrice}¢</span>
        </div>
      </Button>
    </ButtonGroup>
  );

  // 共通的交易按钮组件
  const TradeButton = ({
    isDisabled = false,
    isLoading = false,
    loadingText = "Processing...",
    children,
    onPress,
    ...buttonProps
  }: any) => {
    // 如果未连接钱包，显示连接钱包按钮
    if (!isConnected) {
      return (
        <Button
          size="lg"
          className="h-14 bg-blue-600 text-white font-semibold text-lg hover:bg-blue-700"
          onPress={handleConnectWallet}
          {...buttonProps}
        >
          {t("Normal_Login")}
        </Button>
      );
    }

    // 如果地区不允许，显示禁用按钮
    if (!isRegionAllowed) {
      return (
        <Tooltip
          classNames={{
            content: [
              "bg-neutral-300",
              "rounded-md",
              "px-2",
              "py-4",
              "shadow-md",
              "text-white",
              "w-72",
              "text-sm",
              "text-center",
            ],
          }}
          content={
            <div className="text-xs">
              <span>{t("Normal_Zone_Warning")}</span>
              <Link href="/toe" className="cursor-pointer underline text-xs text-blue-400 font-semibold">
                {t("Normal_Zone_Warning_Terms")}
              </Link>
            </div>
          }
        >
          <Button
            size="lg"
            className="h-14 bg-gray-400 text-gray-700 font-semibold text-lg cursor-not-allowed"
            isDisabled
            {...buttonProps}
          >
            {t("Unavailable_In_Your_Region")}
          </Button>
        </Tooltip>
      );
    }

    // 正常的交易按钮
    return (
      <Button
        size="lg"
        className="h-14 bg-blue-600 text-white font-semibold text-lg hover:bg-blue-700"
        isDisabled={isDisabled || isLoading}
        isLoading={isLoading}
        onPress={onPress}
        {...buttonProps}
      >
        {isLoading ? loadingText : children}
      </Button>
    );
  };

  // 共通的 props 对象，传递给子组件
  const commonProps = {
    // 原有 props
    ...otherProps,

    // 共通状态
    availableBalance,
    isRegionAllowed,
    orderButtonState,
    setOrderButtonState,
    orderTradeTokenId,
    showNotification,
    setShowNotification,
    makeOrderStatus,
    orderError,
    notification,
    setNotification,
    isConnected,
    orderType: SelectType as "Market" | "Limit",

    // 共通方法
    handleOrder,
    checkRegion,
    handleButtonClick,
    calculateMaxShares: orderState.calculateMaxShares,
    refreshAll: orderState.refreshAfterOrder,
    manualRefresh: orderState.refreshData,
    showInsufficientBalanceNotification,
    handleConnectWallet,

    // 订单状态
    orderState,

    // 共通组件
    YesNoButtonGroup,
    TradeButton,

    // 计算相关
    YesPrice,
    NoPrice,
  };

  // 渲染对应的订单组件
  const renderOrderComponent = () => {
    if (SelectType === "Market") {
      return <MarketOrder {...commonProps} />;
    }
    return <LimitOrder {...commonProps} />;
  };

  return (
    <div>
      {renderOrderComponent()}

      {/* 共通通知组件 */}
      {showNotification && (
        <TradeNotification
          side={currentSelectedItem}
          outcome={selectedButtonType}
          shares={tradeNotificationData.shares}
          price={tradeNotificationData.price}
          question={groupItemTitle}
          setShowNotification={setShowNotification}
          status={makeOrderStatus}
          orderError={orderError}
        />
      )}

      {notification && (
        <NotificationCard
          title={notification.title}
          content={notification.content}
          footer={notification.footer}
          notiStatus={notification.notiStatus}
          setShowNotification={() => setNotification(null)}
        />
      )}
      <CustomConnectModal isOpen={isLoginModalOpen} onClose={closeLoginModal} />
    </div>
  );
};

export default OrderCardContent;
