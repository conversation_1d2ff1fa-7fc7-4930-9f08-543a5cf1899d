import React, { useEffect, useState } from "react";
import { Input } from "@heroui/react";
import { Minus, Plus } from "lucide-react";

interface NumberInputProps {
  value: number;
  onChange: (value: number) => void;
  step?: number;
  min?: number;
  max?: number;
  type?: "price" | "shares";
  unit?: string;
  decimalLimit?: number;
}

const NumberInput: React.FC<NumberInputProps> = ({
  value,
  onChange,
  step = 1,
  min = 0,
  max = Infinity,
  type = "price",
  decimalLimit,
}) => {
  const [inputValue, setInputValue] = useState<string>(value.toString());

  useEffect(() => {
    setInputValue(value.toString());
  }, [value]);

  const handleDecrement = () => {
    const newValue = Math.max(value - step, min);
    setInputValue(newValue.toString());
    onChange(newValue);
  };

  const handleIncrement = () => {
    const newValue = Math.min(value + step, max);
    setInputValue(newValue.toString());
    onChange(newValue);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value;

    if (type === "price") {
      const limit = decimalLimit ? decimalLimit - 2 : 0;
      // 允许输入小数点和数字，并且最多 decimalLimit 位小数
      const regex = new RegExp(`^\\d*\\.?\\d{0,${limit}}$`);
      if (!regex.test(newValue)) {
        return;
      }
    } else if (type === "shares") {
      const regex = new RegExp(`^\\d*\\.?\\d{0,2}$`);
      if (!regex.test(newValue)) {
        return;
      }
    }

    // 去掉前导零
    if (newValue.startsWith("0") && newValue.length > 1 && !newValue.startsWith("0.")) {
      newValue = newValue.replace(/^0+/, "");
    }

    setInputValue(newValue);

    if (newValue === "" || newValue === ".") {
      onChange(0);
    } else {
      const parsedValue = parseFloat(newValue);
      if (!isNaN(parsedValue)) {
        const limitedValue = Math.min(Math.max(parsedValue, min), max);
        if (limitedValue !== parsedValue) {
          setInputValue(limitedValue.toString());
        }

        onChange(limitedValue);
      }
    }
  };
  return (
    <Input
      type="text"
      variant="bordered"
      value={inputValue}
      placeholder="0"
      onChange={handleChange}
      classNames={{
        input: "text-lg font-medium text-center",
        inputWrapper: "h-12 border-2 border-gray-200 focus:border-blue-500",
      }}
      startContent={
        <div className="flex-center size-7 rounded-md bg-gray-200 cursor-pointer" onClick={handleDecrement}>
          <Minus className="size-4" />
        </div>
      }
      endContent={
        <div className="flex items-center">
          <div className="w-6 flex justify-center">
            {type === "price" && <span className="text-sm font-sans">¢</span>}
          </div>

          <div className="flex-center size-7 rounded-md bg-gray-200 cursor-pointer ml-2" onClick={handleIncrement}>
            <Plus className="size-4" />
          </div>
        </div>
      }
    />
  );
};

export default NumberInput;
