import React, { useCallback, useEffect, useMemo, useState } from "react";
import { getMarketOrderQuote } from "@/api/order";
import { OrderType, roundToInteger } from "@/utils";
import { Button, Card, CardBody, Chip, Input, Spinner } from "@heroui/react";
import { useTranslation } from "react-i18next";

// 类型定义
interface MarketOrderProps {
  currentSelectedItem: "Buy" | "Sell";
  currentCardData: {
    order_min_size: number;
  };
  selectedButtonType: "yes" | "no";
  availableBalance: number;
  orderButtonState: "none" | "loading" | "success";
  setOrderButtonState: (state: "none" | "loading" | "success") => void;
  orderTradeTokenId: string;
  YesPrice: string;
  NoPrice: string;
  handleOrder: (params: any) => Promise<void>;
  checkRegion: () => Promise<boolean>;
  calculateMaxShares: (type: string, item: string, tokenId: string, price: number) => number;
  showInsufficientBalanceNotification: (required: number, available: number) => void;
  refreshAll: (force?: boolean) => Promise<void>;
  orderState: any; // 临时类型，稍后可以改为具体类型
  YesNoButtonGroup: React.ComponentType;
  TradeButton: React.ComponentType<any>;
  // 单边交易验证函数（可选）
  validateSingleSidedMarketOrder?: (
    amount: string,
    actualCost: number,
    actualShares: number,
    availableShares: number,
    minSize: number,
  ) => { hasError: boolean; errorMessage: string };
}

interface CardTemporaryData {
  payment: number;
  potential: string;
  potentialPersent: string;
  american: string;
  decimal: string;
}

// 常量定义
const CONSTANTS = {
  MINIMUM_SHARES: 5 * 10 ** 6,
  MINIMUM_ORDER_VALUE: 1 * 10 ** 6,
  CONVERSION_FACTOR: 1000000,
  DEBOUNCE_DELAY: 500,
  MAX_INPUT_VALUE: 999999999.99,
  MAX_INTEGER_DIGITS: 9,
} as const;

const QUICK_AMOUNTS = {
  BUY: [
    { value: 1, key: "add_1_dollar" },
    { value: 20, key: "add_20_dollars" },
    { value: 100, key: "add_100_dollars" },
  ],
  SELL: [10, 25, 50],
} as const;

const MarketOrder: React.FC<MarketOrderProps> = props => {
  const { t } = useTranslation();

  const {
    currentSelectedItem,
    currentCardData,
    selectedButtonType,
    availableBalance,
    orderButtonState,
    setOrderButtonState,
    orderTradeTokenId,
    YesPrice,
    NoPrice,
    handleOrder,
    checkRegion,
    calculateMaxShares,
    showInsufficientBalanceNotification,
    refreshAll,
    orderState,
    YesNoButtonGroup,
    TradeButton,
    // 单边交易验证函数
    validateSingleSidedMarketOrder,
  } = props;

  const [amount, setAmount] = useState<string>("");
  const [amountError, setAmountError] = useState<string>("");
  const [isQuoteLoading, setIsQuoteLoading] = useState<boolean>(false);
  const [actualCost, setActualCost] = useState<number>(0);
  const [actualShares, setActualShares] = useState<number>(0);
  const [availableShares, setAvailableShares] = useState<number>(0);
  const [cardTemporaryData, setCardTemporaryData] = useState<CardTemporaryData>({
    payment: 0,
    potential: "0",
    potentialPersent: "0",
    american: "0",
    decimal: "0",
  });

  // 计算市场价格
  const marketPrice = useMemo(() => {
    return selectedButtonType === "yes" ? Number(YesPrice) : Number(NoPrice);
  }, [selectedButtonType, YesPrice, NoPrice]);

  // 计算是否为有效输入
  const isValidInput = useMemo(() => {
    if (!amount) return false;
    const numAmount = parseFloat(amount);
    return !isNaN(numAmount) && numAmount > 0;
  }, [amount]);

  const { order_min_size } = currentCardData;

  // 数据重置函数
  const resetQuoteData = useCallback(() => {
    setActualCost(0);
    setActualShares(0);
    setCardTemporaryData({
      payment: 0,
      potential: "0",
      potentialPersent: "0",
      american: "0",
      decimal: "0",
    });
  }, []);

  // 错误处理函数
  const handleQuoteError = useCallback(
    (error: any) => {
      console.error("获取市场报价失败:", error);

      const isInsufficientLiquidity =
        error?.message?.includes("insufficient quantity to calculate price") ||
        error?.response?.data?.error?.includes("insufficient quantity to calculate price") ||
        [400].includes(error?.status || error?.response?.status) ||
        error?.code === "ERR_BAD_REQUEST";

      const isOrderbookError = error?.message?.includes("orderbook");

      let errorMessage = t("market_order.errors.network_error");

      if (isInsufficientLiquidity) {
        errorMessage =
          currentSelectedItem === "Buy"
            ? t("market_order.errors.insufficient_liquidity_buy")
            : t("market_order.errors.insufficient_liquidity_sell");
      } else if (isOrderbookError) {
        errorMessage = t("market_order.errors.orderbook_unavailable");
      }

      setAmountError(errorMessage);
      resetQuoteData();
      setOrderButtonState("none");
    },
    [currentSelectedItem, resetQuoteData, setOrderButtonState, t],
  );

  // 更新临时卡片数据
  const updateCardTemporaryData = useCallback((quote: any) => {
    const effectivePrice = quote.amount > 0 ? quote.actualCost / quote.amount : 0;

    setCardTemporaryData({
      payment: quote.actualCost,
      potential: (quote.amount / CONSTANTS.CONVERSION_FACTOR).toFixed(2),
      potentialPersent:
        effectivePrice > 0
          ? roundToInteger(((quote.amount - effectivePrice * quote.amount) * 100) / (effectivePrice * quote.amount))
          : "0",
      american: effectivePrice > 0 ? ((1 - effectivePrice) / effectivePrice).toFixed(2) : "0",
      decimal: effectivePrice > 0 ? (1 / effectivePrice).toFixed(2) : "0",
    });
  }, []);

  // 计算用户可卖的shares（不触发数据刷新）
  const calculateAvailableShares = useCallback(() => {
    if (!orderTradeTokenId) return 0;

    const maxShares = calculateMaxShares(selectedButtonType, currentSelectedItem, orderTradeTokenId, marketPrice);
    return maxShares;
  }, [calculateMaxShares, selectedButtonType, currentSelectedItem, orderTradeTokenId, marketPrice]);

  // 处理最大金额/份数（包含数据刷新）
  const handleCompleteMaxShares = useCallback(async () => {
    try {
      // 如果是卖单且数据未准备好，先刷新数据
      if (currentSelectedItem === "Sell" && (orderState.isInitializing || orderState.error)) {
        console.log("🔄 MarketOrder: 数据未准备好，开始刷新...");
        await refreshAll(true);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      const maxValue = calculateMaxShares(selectedButtonType, currentSelectedItem, orderTradeTokenId, marketPrice);

      if (currentSelectedItem === "Sell") {
        setAvailableShares(maxValue);
        setAmount(maxValue.toString());
      } else {
        const maxAmountInDollars = (maxValue * marketPrice) / 100;
        setAmount(maxAmountInDollars.toFixed(2));
      }
    } catch (error) {
      console.error("❌ MarketOrder: Error calculating max shares:", error);
    }
  }, [
    selectedButtonType,
    currentSelectedItem,
    orderTradeTokenId,
    marketPrice,
    calculateMaxShares,
    refreshAll,
    orderState,
  ]);

  // 获取市场订单报价
  const fetchMarketQuote = useCallback(async () => {
    if (!orderTradeTokenId || !isValidInput) {
      resetQuoteData();
      return;
    }

    setIsQuoteLoading(true);
    setAmountError("");

    try {
      const side = currentSelectedItem === "Buy" ? 1 : 0;
      const value = Math.floor(parseFloat(amount) * CONSTANTS.CONVERSION_FACTOR);

      const quote = await getMarketOrderQuote(orderTradeTokenId, side, value);

      if (quote) {
        setActualCost(quote.actualCost);
        setActualShares(quote.amount);
        updateCardTemporaryData(quote);
        setOrderButtonState("none");
      }
    } catch (error: any) {
      handleQuoteError(error);
    } finally {
      setIsQuoteLoading(false);
    }
  }, [
    orderTradeTokenId,
    isValidInput,
    amount,
    currentSelectedItem,
    resetQuoteData,
    updateCardTemporaryData,
    handleQuoteError,
    setOrderButtonState,
  ]);

  // 验证订单
  const validateMarketOrder = useCallback(
    (amount: string, minSize: number): boolean => {
      setAmountError("");
      const numAmount = parseFloat(amount);

      // 处理特殊情况：空输入或 "0"
      if (!amount && amount !== "0") {
        setAmountError(
          currentSelectedItem === "Buy"
            ? t("market_order.errors.amount_required")
            : t("market_order.errors.shares_required"),
        );
        return true;
      }

      if (numAmount <= 0) {
        setAmountError(
          currentSelectedItem === "Buy"
            ? t("market_order.errors.amount_required")
            : t("market_order.errors.shares_required"),
        );
        return true;
      }

      // 检查最大值限制
      if (numAmount > CONSTANTS.MAX_INPUT_VALUE) {
        setAmountError(
          `Maximum ${
            currentSelectedItem === "Buy" ? "amount" : "shares"
          } is ${CONSTANTS.MAX_INPUT_VALUE.toLocaleString()}`,
        );
        return true;
      }

      // 检查报价有效性
      if (actualCost <= 0 || actualShares <= 0) {
        setAmountError(t("market_order.errors.invalid_quote"));
        return true;
      }

      // 检查最小份数
      if (actualShares < CONSTANTS.MINIMUM_SHARES) {
        setAmountError(t("market_order.errors.minimum_shares"));
        return true;
      }

      // 买入验证
      if (currentSelectedItem === "Buy") {
        const minSizeInUSDC = minSize / CONSTANTS.CONVERSION_FACTOR;
        if (numAmount < minSizeInUSDC) {
          setAmountError(t("market_order.errors.minimum_amount", { amount: minSizeInUSDC.toFixed(2) }));
          return true;
        }
        if (actualCost < CONSTANTS.MINIMUM_ORDER_VALUE) {
          setAmountError(t("market_order.errors.minimum_order_value"));
          return true;
        }
      }
      // 卖出验证
      else {
        if (numAmount > availableShares) {
          setAmountError(t("market_order.errors.maximum_shares", { shares: availableShares }));
          return true;
        }
        if (numAmount < 1) {
          setAmountError(t("market_order.errors.minimum_share_required"));
          return true;
        }
      }

      return false;
    },
    [currentSelectedItem, actualCost, actualShares, availableShares, t],
  );

  // 处理订单
  const onClickHandleMarketOrder = useCallback(async () => {
    setOrderButtonState("loading");

    try {
      // 地区检查
      const regionAllowed = await checkRegion();
      if (!regionAllowed) {
        setOrderButtonState("none");
        return;
      }

      // 订单验证 - 使用单边交易验证函数（如果可用）或双边交易验证函数
      if (validateSingleSidedMarketOrder) {
        const validation = validateSingleSidedMarketOrder(
          amount,
          actualCost,
          actualShares,
          availableShares,
          order_min_size,
        );
        if (validation.hasError) {
          setAmountError(validation.errorMessage);
          setOrderButtonState("none");
          return;
        } else {
          setAmountError("");
        }
      } else {
        // 双边交易的原有验证逻辑
        if (validateMarketOrder(amount, order_min_size)) {
          setOrderButtonState("none");
          return;
        }

        // 报价检查
        if (actualCost <= 0 || actualShares <= 0) {
          setAmountError(t("market_order.errors.invalid_quote_retry"));
          setOrderButtonState("none");
          return;
        }

        // 余额检查（仅买入）
        if (currentSelectedItem === "Buy") {
          const actualCostInUSDC = actualCost / CONSTANTS.CONVERSION_FACTOR;
          if (actualCostInUSDC > availableBalance) {
            showInsufficientBalanceNotification(actualCostInUSDC, availableBalance);
            setOrderButtonState("none");
            return;
          }
        }
      }

      // 构建订单参数
      const shares = Math.floor(actualShares);
      const payment =
        currentSelectedItem === "Buy"
          ? Math.floor(parseFloat(amount) * CONSTANTS.CONVERSION_FACTOR)
          : Math.floor(actualCost);

      const price =
        actualCost > 0 && actualShares > 0
          ? Math.floor((actualCost / actualShares) * CONSTANTS.CONVERSION_FACTOR) / CONSTANTS.CONVERSION_FACTOR
          : marketPrice;

      const postParams = {
        shares: shares / CONSTANTS.CONVERSION_FACTOR,
        payment,
        side: currentSelectedItem,
        tokenId: orderTradeTokenId,
        price,
        type: OrderType.MARKET,
      };

      console.log("Market Order params:", postParams);
      await handleOrder(postParams);
    } catch (error: any) {
      console.error("Market订单过程中发生错误:", error);
      setOrderButtonState("none");
    }
  }, [
    checkRegion,
    validateMarketOrder,
    amount,
    order_min_size,
    actualCost,
    actualShares,
    currentSelectedItem,
    availableBalance,
    showInsufficientBalanceNotification,
    orderTradeTokenId,
    marketPrice,
    handleOrder,
    setOrderButtonState,
    t,
  ]);

  // 处理输入变化
  const handleAmountChange = useCallback(
    (value: string) => {
      // 限制整数部分最多9位数，小数部分最多2位
      const regex = /^\d{0,9}\.?\d{0,2}$/;
      if (!regex.test(value)) {
        return;
      }

      // 检查整数部分是否超过最大位数
      const parts = value.split(".");
      if (parts[0] && parts[0].length > CONSTANTS.MAX_INTEGER_DIGITS) {
        return;
      }

      // 规范化输入：移除前导零，但保留单个"0"和小数
      let normalizedValue = value;
      if (value && value !== "0" && value !== "0." && !value.includes(".")) {
        normalizedValue = value.replace(/^0+/, "") || "0";
      }

      // 最大值检查
      const numValue = parseFloat(normalizedValue);
      if (numValue > CONSTANTS.MAX_INPUT_VALUE) {
        return;
      }

      // 判断是否应该进入loading状态
      if (normalizedValue && numValue > 0) {
        setOrderButtonState("loading");
      } else {
        setOrderButtonState("none");
      }

      setAmount(normalizedValue);
      setAmountError("");
    },
    [setOrderButtonState],
  );
  // 快速金额/shares按钮
  const handleQuickAmount = useCallback(
    (index: number) => {
      const currentAmount = parseFloat(amount) || 0;

      const addAmount = currentSelectedItem === "Buy" ? QUICK_AMOUNTS.BUY[index].value : QUICK_AMOUNTS.SELL[index];

      const newAmount = (currentAmount + addAmount).toString();

      if (newAmount !== "0") {
        setOrderButtonState("loading");
      }

      setAmount(newAmount);
    },
    [amount, currentSelectedItem, setOrderButtonState],
  );

  // 获取按钮文本
  const buttonText = useMemo(() => {
    if (currentSelectedItem === "Sell" && availableShares <= 0) {
      return t("market_order.no_shares_available");
    }
    if (actualCost <= 0 || actualShares <= 0) {
      return t("market_order.unable_to_get_quote");
    }
    return t("market_order.at_market_price", {
      action: t(`market_order.${currentSelectedItem.toLowerCase()}`),
    });
  }, [currentSelectedItem, availableShares, actualCost, actualShares, t]);

  // 检查按钮是否禁用
  const isButtonDisabled = useMemo(() => {
    return (
      isQuoteLoading ||
      (!amount && amount !== "0") ||
      orderButtonState === "loading" ||
      (currentSelectedItem === "Sell" && availableShares <= 0) ||
      actualCost <= 0 ||
      actualShares <= 0 ||
      !!amountError
    );
  }, [
    isQuoteLoading,
    amount,
    orderButtonState,
    currentSelectedItem,
    availableShares,
    actualCost,
    actualShares,
    amountError,
  ]);

  // Effects
  useEffect(() => {
    const debounceTimer = setTimeout(fetchMarketQuote, CONSTANTS.DEBOUNCE_DELAY);
    return () => clearTimeout(debounceTimer);
  }, [fetchMarketQuote]);

  // 当相关参数变化时，重新计算可用份额
  useEffect(() => {
    if (currentSelectedItem === "Sell" && orderTradeTokenId) {
      const shares = calculateAvailableShares();
      setAvailableShares(shares);
    } else {
      setAvailableShares(0);
    }
  }, [
    currentSelectedItem,
    orderTradeTokenId,
    selectedButtonType,
    marketPrice,
    calculateAvailableShares,
    orderState.isInitializing,
    orderState.currentSellValue,
    orderState.hasData,
  ]);

  // 当切换买卖类型时，重置输入
  useEffect(() => {
    setAmount("");
    setAmountError("");
  }, [currentSelectedItem]);

  // 渲染快速按钮
  const renderQuickButtons = () => {
    const buttons =
      currentSelectedItem === "Buy"
        ? QUICK_AMOUNTS.BUY.map((item, index) => ({ ...item, index }))
        : QUICK_AMOUNTS.SELL.map((value, index) => ({
            value,
            key: `add_${value}_shares`,
            index,
          }));

    return buttons.map(({ index, key }) => (
      <Button
        key={index}
        size="sm"
        variant="bordered"
        className="text-gray-700 bg-white border-gray-300 hover:bg-gray-50 text-xs px-2 py-1 h-7"
        onPress={() => handleQuickAmount(index)}
      >
        {t(`market_order.quick_amounts.${key}`)}
      </Button>
    ));
  };

  // 渲染报价卡片
  const renderQuoteCard = () => {
    if (isQuoteLoading) {
      return (
        <Card className="bg-gray-50">
          <CardBody className="flex items-center justify-center py-3">
            <div className="flex items-center text-xs text-gray-500">
              <Spinner size="sm" className="mr-2" />
              {t("market_order.getting_quote")}
            </div>
          </CardBody>
        </Card>
      );
    }

    if (amountError && !actualShares) {
      return (
        <Card className="bg-gradient-to-br from-red-50 to-rose-50 border-red-200">
          <CardBody className="space-y-2 p-3">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">!</span>
              </div>
              <span className="text-base font-semibold text-red-700">{t("market_order.unable_to_get_quote")}</span>
            </div>
            <div className="text-xs text-red-600">{amountError}</div>
            <div className="text-xs text-gray-500">
              {t("market_order.suggestions.reduce_amount_or_limit", {
                type:
                  currentSelectedItem === "Buy"
                    ? t("market_order.suggestions.buy_amount")
                    : t("market_order.suggestions.sell_shares"),
              })}
            </div>
          </CardBody>
        </Card>
      );
    }

    if (actualShares > 0) {
      return (
        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
          <CardBody className="space-y-3 p-3">
            {currentSelectedItem === "Buy" ? (
              <>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-base font-semibold text-green-700">{t("market_order.to_win")}</span>
                      <div className="w-4 h-3 bg-green-500 rounded-sm flex items-center justify-center">
                        <span className="text-white text-xs font-bold">$</span>
                      </div>
                    </div>
                    <div className="text-xs text-gray-600">
                      {t("market_order.avg_price", {
                        price: actualShares > 0 ? (actualCost / actualShares).toFixed(3) : "0",
                      })}
                    </div>
                  </div>

                  <div className="flex items-center justify-center py-1">
                    <div className="flex items-baseline space-x-1">
                      <span className="text-xl font-bold text-green-600">$</span>
                      <span className="text-3xl font-bold text-green-700">{cardTemporaryData.potential}</span>
                    </div>
                  </div>

                  <div className="text-center">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800">
                      {t("market_order.return_percentage", { percentage: cardTemporaryData.potentialPersent })}
                    </span>
                  </div>
                </div>

                <div className="border-t border-green-200"></div>

                <div className="space-y-1 text-xs">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">{t("market_order.actual_cost")}</span>
                    <span className="font-semibold text-gray-800">
                      ${(actualCost / CONSTANTS.CONVERSION_FACTOR).toFixed(4)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">{t("market_order.shares_count")}</span>
                    <span className="font-semibold text-gray-800">
                      {(actualShares / CONSTANTS.CONVERSION_FACTOR).toFixed(2)}
                    </span>
                  </div>
                </div>
              </>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-base font-semibold text-blue-700">{t("market_order.to_receive")}</span>
                  <div className="text-xs text-gray-600 font-semibold">
                    {t("market_order.avg_price", {
                      price: actualShares > 0 ? (actualCost / actualShares).toFixed(3) : "0",
                    })}
                  </div>
                </div>

                <div className="flex items-center justify-center py-1">
                  <div className="flex items-baseline space-x-1">
                    <span className="text-xl font-bold text-blue-600">$</span>
                    <span className="text-3xl font-bold text-blue-700">
                      {(actualCost / CONSTANTS.CONVERSION_FACTOR).toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </CardBody>
        </Card>
      );
    }

    return null;
  };

  return (
    <div className="flex flex-col space-y-3">
      <YesNoButtonGroup />

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-base font-medium text-gray-900">
            {currentSelectedItem === "Buy" ? t("market_order.amount") : t("market_order.shares")}
          </h3>
          <Chip size="sm" variant="flat" className="text-xs text-gray-500 bg-gray-100">
            {currentSelectedItem === "Buy"
              ? t("market_order.available_balance", { balance: availableBalance.toFixed(2) })
              : t("market_order.available_shares", { shares: availableShares.toFixed(2) })}
          </Chip>
        </div>

        <Input
          type="text"
          placeholder="0"
          value={amount}
          onValueChange={handleAmountChange}
          startContent={
            currentSelectedItem === "Buy" ? <span className="text-2xl font-medium text-gray-700">$</span> : null
          }
          classNames={{
            input: "text-2xl font-medium text-right",
            inputWrapper: "h-12 border-2 border-gray-200 focus:border-blue-500",
          }}
          isInvalid={!!amountError}
          errorMessage={amountError}
          variant="bordered"
        />

        <div className="flex space-x-1">
          {renderQuickButtons()}
          <Button
            size="sm"
            variant="bordered"
            className="text-gray-700 bg-white border-gray-300 hover:bg-gray-50 text-xs px-2 py-1 h-7"
            onPress={handleCompleteMaxShares}
            isDisabled={
              (currentSelectedItem === "Sell" && (orderState.isInitializing || orderState.isRefreshing)) ||
              (currentSelectedItem === "Sell" && availableShares <= 0)
            }
            isLoading={orderState.isRefreshing}
          >
            {orderState.isRefreshing ? "" : t("market_order.max")}
          </Button>
        </div>
      </div>

      {renderQuoteCard()}

      <TradeButton
        isDisabled={isButtonDisabled}
        isLoading={orderButtonState === "loading"}
        loadingText={t("market_order.processing")}
        onPress={onClickHandleMarketOrder}
      >
        {buttonText}
      </TradeButton>
    </div>
  );
};

export default MarketOrder;
