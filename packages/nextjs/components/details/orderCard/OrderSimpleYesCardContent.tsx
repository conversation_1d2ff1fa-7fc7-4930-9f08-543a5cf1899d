import React, { useCallback, useEffect, useRef, useState } from "react";
import TradeNotification from "../tradeNotification/TradeNotification";
import LimitOrder from "./LimitOrder";
import MarketOrder from "./MarketOrder";
import CustomConnectModal from "@/components/login/CustomConnectModal";
import NotificationCard from "@/components/other/NotificationCard";
import useOrderState from "@/hooks/orders/useOrderState";
import { useLoginModal } from "@/hooks/useLoginModal";
import { useUserAddress } from "@/hooks/useUserAddress";
import {
  base64DecodeByLanguage,
  checkApprovalStatus,
  checkGeolocation,
  checkProxyAndCheckApprove,
  validateOrder,
} from "@/utils";
import { handleMakeOrder } from "@/utils/signature/order";
import { Button, Link, Tooltip } from "@heroui/react";
import { useTranslation } from "react-i18next";
import { useGlobalState } from "~~/services/store/store";

const OrderSimpleYesCardContent = (props: any) => {
  const { t } = useTranslation();
  const { isLoginModalOpen, openLoginModal, closeLoginModal } = useLoginModal();

  const { selectedKey, ...otherProps } = props;
  const {
    address,
    currentSelectedItem,
    currentSelectedMarket,
    selectedButtonType,
    setSelectedButtonType,
    balance,
    isShowPreBuyModal,
    setIsShowPreBuyModal,
    groupItemTitle,
    btnPriceData,
    clobApis,
    frozenValue,
    refreshFrozenValue,
    refreshBalanceValue,
    lockFunds,
    unlockFunds,
    allMarketsPriceData,
  } = otherProps;

  const { isConnected } = useUserAddress();
  const SelectType = selectedKey.has("Market") ? "Market" : "Limit";
  // 共通状态
  const [availableBalance, setAvailableBalance] = useState(0);
  const [isRegionAllowed, setIsRegionAllowed] = useState(true);
  const [orderButtonState, setOrderButtonState] = useState<"none" | "loading" | "success">("none");
  const [orderTradeTokenId, setOrderTradeTokenId] = useState("");
  const [showNotification, setShowNotification] = useState(false);
  const [makeOrderStatus, setMakeOrderStatus] = useState<"success" | "failure" | "canceled">("success");
  const [orderError, setOrderError] = useState<string>("");

  // 对于 Game 类型，使用当前选中的市场的 token IDs
  const { clob_token_ids } = currentSelectedMarket || {};
  const { YesPrice, NoPrice } = btnPriceData;

  const { address: userAddress } = useUserAddress();
  const { walletType } = useGlobalState();

  const orderState = useOrderState({
    clobApis,
    walletType,
    address: userAddress || address,
    availableBalance: isConnected ? balance - frozenValue : 0,
    tokenIds: clob_token_ids || [],
  });

  const [notification, setNotification] = useState<{
    title: string;
    content?: string;
    footer?: string;
    notiStatus?: "success" | "failure";
  } | null>(null);

  const [tradeNotificationData, setTradeNotificationData] = useState({
    shares: 0,
    price: 0,
  });

  // 监听balance变化，当balance变化时立即刷新frozen值
  const prevBalanceRef = useRef<number>(balance);
  const refreshFrozenValueRef = useRef(refreshFrozenValue);
  const clobApisRef = useRef(clobApis);

  // 更新 refs
  refreshFrozenValueRef.current = refreshFrozenValue;
  clobApisRef.current = clobApis;

  useEffect(() => {
    // 只有在用户已登录时才监听余额变化
    if (!isConnected) {
      prevBalanceRef.current = balance;
      return;
    }

    const prevBalance = prevBalanceRef.current;

    // 如果balance发生了变化，说明有订单被执行了
    if (prevBalance !== balance && prevBalance > 0) {
      // 立即刷新frozen值以确保available balance计算正确
      refreshFrozenValue(clobApis, true)
        .then(() => {
          console.log(`✅ Frozen value refreshed after balance change`);
        })
        .catch((error: any) => {
          console.error(`❌ Failed to refresh frozen value after balance change:`, error);
        });
    }

    // 更新引用值
    prevBalanceRef.current = balance;
  }, [balance, refreshFrozenValue, clobApis, isConnected]);

  // 共通 Effect：更新可用余额，只有在用户已登录时才计算
  useEffect(() => {
    if (isConnected) {
      const newAvailableBalance = balance - frozenValue;
      setAvailableBalance(newAvailableBalance);
    } else {
      // 用户未登录时，不查询余额，设置为0
      setAvailableBalance(0);
    }
  }, [balance, frozenValue, isConnected]);

  // 共通 Effect：更新交易Token ID
  useEffect(() => {
    if (clob_token_ids) {
      if (selectedButtonType === "yes") {
        setOrderTradeTokenId(clob_token_ids[0]);
      }
      if (selectedButtonType === "no") {
        setOrderTradeTokenId(clob_token_ids[1]);
      }
    }
  }, [clob_token_ids, selectedButtonType]);

  // 监听 Buy/Sell 切换，刷新数据以获取最新状态
  const { refreshData, isReady } = orderState;
  const refreshDataRef = useRef(refreshData);
  refreshDataRef.current = refreshData;

  useEffect(() => {
    if (isReady) {
      // 强制刷新以确保获取最新数据
      refreshDataRef.current(true);
    }
  }, [currentSelectedItem, isReady]); // 移除 refreshData 依赖

  // 共通订单处理逻辑
  const { refreshAfterOrder } = orderState;

  // 使用 ref 来避免依赖不稳定的函数
  const refreshAfterOrderRef = useRef(refreshAfterOrder);
  const lockFundsRef = useRef(lockFunds);
  const unlockFundsRef = useRef(unlockFunds);
  const refreshBalanceValueRef = useRef(refreshBalanceValue);

  // 更新 refs
  refreshAfterOrderRef.current = refreshAfterOrder;
  lockFundsRef.current = lockFunds;
  unlockFundsRef.current = unlockFunds;
  refreshBalanceValueRef.current = refreshBalanceValue;

  // 单边交易的Limit Order验证函数 - 直接使用双边交易的validateOrder
  const validateSingleSidedLimitOrder = useCallback(
    (price: number, sharesValue: number, order_min_size: number): { hasError: boolean; errorMessage: string } => {
      // 使用 currentSelectedMarket 中的 order_min_size
      const actualOrderMinSize = currentSelectedMarket?.order_min_size || order_min_size;

      let priceError = "";
      let sharesError = "";

      const hasValidationError = validateOrder(
        price,
        sharesValue,
        actualOrderMinSize,
        currentSelectedItem,
        (msg: string) => {
          priceError = msg;
        },
        (msg: string) => {
          sharesError = msg;
        },
      );

      if (hasValidationError) {
        return {
          hasError: true,
          errorMessage: priceError || sharesError,
        };
      }

      // 额外的余额检查
      if (currentSelectedItem === "Buy") {
        const paymentInUSDC = (((price * sharesValue) / 100) * 10 ** 6) / 1000000;
        if (paymentInUSDC > availableBalance) {
          return {
            hasError: true,
            errorMessage: t("Order_Insufficient_Balance_Content", {
              required: paymentInUSDC.toFixed(2),
              available: availableBalance.toFixed(2),
            }),
          };
        }
      } else {
        // 卖出时检查可用份额
        const maxAvailableShares = orderState.calculateMaxShares(
          selectedButtonType,
          currentSelectedItem,
          orderTradeTokenId,
          price,
        );
        if (sharesValue > maxAvailableShares) {
          return {
            hasError: true,
            errorMessage: t("Order_Insufficient_Shares_Content", {
              required: sharesValue.toFixed(0),
              available: maxAvailableShares.toFixed(0),
            }),
          };
        }
      }

      return { hasError: false, errorMessage: "" };
    },
    [
      currentSelectedItem,
      availableBalance,
      orderState,
      selectedButtonType,
      orderTradeTokenId,
      t,
      currentSelectedMarket,
    ],
  );

  // 单边交易的Market Order验证函数 - 直接使用双边交易的MarketOrder验证逻辑
  const validateSingleSidedMarketOrderWrapper = useCallback(
    (
      amount: string,
      actualCost: number,
      actualShares: number,
      availableShares: number,
      minSize: number,
    ): { hasError: boolean; errorMessage: string } => {
      // 使用 currentSelectedMarket 中的 order_min_size
      const actualMinSize = currentSelectedMarket?.order_min_size || minSize;

      // 直接使用双边交易MarketOrder.tsx中的验证逻辑
      const numAmount = parseFloat(amount);

      // 基础输入验证
      if (!amount && amount !== "0") {
        return {
          hasError: true,
          errorMessage:
            currentSelectedItem === "Buy"
              ? t("market_order.errors.amount_required")
              : t("market_order.errors.shares_required"),
        };
      }

      if (numAmount <= 0) {
        return {
          hasError: true,
          errorMessage:
            currentSelectedItem === "Buy"
              ? t("market_order.errors.amount_required")
              : t("market_order.errors.shares_required"),
        };
      }

      // 检查报价有效性
      if (actualCost <= 0 || actualShares <= 0) {
        return {
          hasError: true,
          errorMessage: t("market_order.errors.invalid_quote"),
        };
      }

      // 检查最小份数 (使用双边交易的常量)
      const MINIMUM_SHARES = 5 * 10 ** 6;
      if (actualShares < MINIMUM_SHARES) {
        return {
          hasError: true,
          errorMessage: t("market_order.errors.minimum_shares"),
        };
      }

      // 买入验证
      if (currentSelectedItem === "Buy") {
        const CONVERSION_FACTOR = 1000000;
        const MINIMUM_ORDER_VALUE = 1 * 10 ** 6;

        const minSizeInUSDC = actualMinSize / CONVERSION_FACTOR;
        if (numAmount < minSizeInUSDC) {
          return {
            hasError: true,
            errorMessage: t("market_order.errors.minimum_amount", { amount: minSizeInUSDC.toFixed(2) }),
          };
        }
        if (actualCost < MINIMUM_ORDER_VALUE) {
          return {
            hasError: true,
            errorMessage: t("market_order.errors.minimum_order_value"),
          };
        }
        // 余额检查
        const actualCostInUSDC = actualCost / CONVERSION_FACTOR;
        if (actualCostInUSDC > availableBalance) {
          return {
            hasError: true,
            errorMessage: t("Order_Insufficient_Balance_Content", {
              required: actualCostInUSDC.toFixed(2),
              available: availableBalance.toFixed(2),
            }),
          };
        }
      }
      // 卖出验证
      else {
        if (numAmount > availableShares) {
          return {
            hasError: true,
            errorMessage: t("market_order.errors.maximum_shares", { shares: availableShares }),
          };
        }
        if (numAmount < 1) {
          return {
            hasError: true,
            errorMessage: t("market_order.errors.minimum_share_required"),
          };
        }
      }

      return { hasError: false, errorMessage: "" };
    },
    [currentSelectedItem, availableBalance, t, currentSelectedMarket],
  );

  const handleOrder = useCallback(
    async (postParams: any) => {
      if (!isShowPreBuyModal) {
        let lockAmount = 0;
        if (postParams.side === "Buy") {
          lockAmount = postParams.payment / 1000000;
        }

        try {
          const isProxyValid = await checkProxyAndCheckApprove(address, setIsShowPreBuyModal, notificationProps => {
            setNotification(notificationProps);
          });

          if (!isProxyValid) {
            setOrderButtonState("none");
            return;
          }

          const isApprovedAndHasApi = await checkApprovalStatus(address);
          if (isApprovedAndHasApi) {
            // 保存交易数据用于通知
            setTradeNotificationData({
              shares: postParams.shares,
              price: postParams.price || 0,
            });

            await handleMakeOrder(
              postParams,
              setOrderButtonState,
              setShowNotification,
              setMakeOrderStatus,
              setOrderError,
              async () => {
                await refreshFrozenValueRef.current(clobApisRef.current, true);
                await refreshBalanceValueRef.current(true);
                await refreshAfterOrderRef.current();
              },
            );
          } else {
            setOrderButtonState("none");
          }
        } catch (error: any) {
          console.error("处理订单时发生错误:", error);
          setOrderButtonState("none");

          // 如果订单失败，释放之前锁定的资金
          if (lockAmount > 0) {
            unlockFundsRef.current(lockAmount);
            console.log(`🔓 Funds unlocked due to order failure: ${lockAmount} USDC`);
          }

          // 抛出错误以便 MarketOrder 组件可以捕获并处理
          throw error;
        }
      }
    },
    [address, isShowPreBuyModal, setIsShowPreBuyModal], // 减少依赖项
  );
  // 共通地理位置检查
  const checkRegion = useCallback(async () => {
    try {
      const regionAllowed = await checkGeolocation();
      setIsRegionAllowed(regionAllowed ?? true);
      if (!regionAllowed) {
        setNotification({
          title: t("Normal_Zone_Warning_Title"),
          content: t("Normal_Zone_Warning"),
          notiStatus: "failure",
        });
        return false;
      }
      return true;
    } catch (error) {
      console.error("地理位置检查失败:", error);
      return false;
    }
  }, [t]);

  // 共通按钮点击处理
  const handleButtonClick = useCallback(
    (button: string) => {
      setSelectedButtonType(button);
    },
    [setSelectedButtonType],
  );

  // 显示余额不足通知
  const showInsufficientBalanceNotification = useCallback(
    (required: number, available: number) => {
      if (currentSelectedItem === "Sell") {
        setNotification({
          title: t("Order_Insufficient_Shares_Title"),
          content: t("Order_Insufficient_Shares_Content", {
            required: required.toFixed(0),
            available: available.toFixed(0),
          }),
          notiStatus: "failure",
        });
      } else {
        setNotification({
          title: t("Order_Insufficient_Balance_Title"),
          content: t("Order_Insufficient_Balance_Content", {
            required: required.toFixed(2),
            available: available.toFixed(2),
          }),
          notiStatus: "failure",
        });
      }
    },
    [t, currentSelectedItem],
  );

  // 处理连接钱包
  const handleConnectWallet = useCallback(async () => {
    try {
      openLoginModal();
      console.log("openLoginModal 888 ");
    } catch (error) {
      console.error("打开登录模态框失败:", error);
      setNotification({
        title: t("Wallet_Connection_Failed_Title"),
        content: t("Wallet_Connection_Failed_Content"),
        notiStatus: "failure",
      });
    }
  }, [openLoginModal, t]);

  // 动态按钮组件 - 只显示当前选中的 question
  const YesNoButtonGroup = () => {
    // 如果没有选中的市场，不显示按钮
    if (!currentSelectedMarket || !currentSelectedMarket.id) {
      return null;
    }

    const market = currentSelectedMarket;
    const marketTitle = base64DecodeByLanguage(market.question);

    // 获取当前市场的实时价格
    let marketPrice = "0";

    // 优先使用所有市场价格数据中的实时价格
    if (allMarketsPriceData && allMarketsPriceData[market.id]?.YesPrice) {
      marketPrice = allMarketsPriceData[market.id].YesPrice.toString();
    } else if (btnPriceData?.YesPrice) {
      // 使用 btnPriceData 中的实时价格
      marketPrice = btnPriceData.YesPrice.toString();
    }

    return (
      <Button
        fullWidth
        size="lg"
        className="h-16 bg-green-600 text-white shadow-lg hover:bg-green-700"
        onPress={() => {
          // 设置选中的按钮类型为 yes（因为都是 Yes 情况）
          setSelectedButtonType("yes");
        }}
      >
        <div className="flex flex-col items-center">
          <span className="font-semibold text-medium truncate max-w-[200px]">{marketTitle}</span>
          <span className="font-semibold text-lg">{marketPrice}¢</span>
        </div>
      </Button>
    );
  };

  // 共通的交易按钮组件
  const TradeButton = ({
    isDisabled = false,
    isLoading = false,
    loadingText = "Processing...",
    children,
    onPress,
    ...buttonProps
  }: any) => {
    // 如果未连接钱包，显示连接钱包按钮
    if (!isConnected) {
      return (
        <Button
          size="lg"
          className="h-14 bg-blue-600 text-white font-semibold text-lg hover:bg-blue-700"
          onPress={handleConnectWallet}
          {...buttonProps}
        >
          {t("Normal_Login")}
        </Button>
      );
    }

    // 如果地区不允许，显示禁用按钮
    if (!isRegionAllowed) {
      return (
        <Tooltip
          classNames={{
            content: [
              "bg-neutral-300",
              "rounded-md",
              "px-2",
              "py-4",
              "shadow-md",
              "text-white",
              "w-72",
              "text-sm",
              "text-center",
            ],
          }}
          content={
            <div className="text-xs">
              <span>{t("Normal_Zone_Warning")}</span>
              <Link href="/toe" className="cursor-pointer underline text-xs text-blue-400 font-semibold">
                {t("Normal_Zone_Warning_Terms")}
              </Link>
            </div>
          }
        >
          <Button
            size="lg"
            className="h-14 bg-gray-400 text-gray-700 font-semibold text-lg cursor-not-allowed"
            isDisabled
            {...buttonProps}
          >
            {t("Unavailable_In_Your_Region")}
          </Button>
        </Tooltip>
      );
    }

    // 正常的交易按钮
    return (
      <Button
        size="lg"
        className="h-14 bg-blue-600 text-white font-semibold text-lg hover:bg-blue-700"
        isDisabled={isDisabled || isLoading}
        isLoading={isLoading}
        onPress={onPress}
        {...buttonProps}
      >
        {isLoading ? loadingText : children}
      </Button>
    );
  };

  // 共通的 props 对象，传递给子组件
  const commonProps = {
    // 原有 props
    ...otherProps,

    // 共通状态
    availableBalance,
    isRegionAllowed,
    orderButtonState,
    setOrderButtonState,
    orderTradeTokenId,
    showNotification,
    setShowNotification,
    makeOrderStatus,
    orderError,
    notification,
    setNotification,
    isConnected,
    orderType: SelectType as "Market" | "Limit",

    // 共通方法
    handleOrder,
    checkRegion,
    handleButtonClick,
    calculateMaxShares: orderState.calculateMaxShares,
    refreshAll: orderState.refreshAfterOrder,
    manualRefresh: orderState.refreshData,
    showInsufficientBalanceNotification,
    handleConnectWallet,

    // 单边交易验证函数
    validateSingleSidedLimitOrder,
    validateSingleSidedMarketOrder: validateSingleSidedMarketOrderWrapper,

    // 订单状态
    orderState,

    // 共通组件
    YesNoButtonGroup,
    TradeButton,

    // 计算相关
    YesPrice,
    NoPrice,
  };

  // 渲染对应的订单组件
  const renderOrderComponent = () => {
    if (SelectType === "Market") {
      return <MarketOrder {...commonProps} />;
    }
    return <LimitOrder {...commonProps} />;
  };

  return (
    <div>
      {renderOrderComponent()}

      {/* 共通通知组件 */}
      {showNotification && (
        <TradeNotification
          side={currentSelectedItem}
          outcome={selectedButtonType}
          shares={tradeNotificationData.shares}
          price={tradeNotificationData.price}
          question={groupItemTitle}
          setShowNotification={setShowNotification}
          status={makeOrderStatus}
          orderError={orderError}
        />
      )}

      {notification && (
        <NotificationCard
          title={notification.title}
          content={notification.content}
          footer={notification.footer}
          notiStatus={notification.notiStatus}
          setShowNotification={() => setNotification(null)}
        />
      )}
      <CustomConnectModal isOpen={isLoginModalOpen} onClose={closeLoginModal} />
    </div>
  );
};

export default OrderSimpleYesCardContent;
