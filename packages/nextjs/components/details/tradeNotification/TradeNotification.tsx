import React, { useEffect, useState } from "react";
import { Card } from "@heroui/react";
import { Al<PERSON><PERSON><PERSON>gle, CircleCheck, MinusCircle, X } from "lucide-react";

interface TradeNotificationProps {
  side: string;
  outcome: string;
  shares: number;
  price: number;
  question: string;
  setShowNotification: any;
  status: "success" | "failure" | "canceled";
  orderError: string;
}

const TradeNotification: React.FC<TradeNotificationProps> = ({
  side,
  outcome,
  shares,
  price,
  question,
  setShowNotification,
  status,
  orderError,
}) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false);
      setShowNotification(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, [setShowNotification]);

  if (!visible) return null;

  const onClickHandler = () => {
    setVisible(false);
    setShowNotification(false);
  };

  const renderIcon = () => {
    if (status === "success") {
      return <CircleCheck color="#43A047" strokeWidth={2} className="mr-2" />;
    } else if (status === "failure") {
      return <AlertTriangle color="#E64800" strokeWidth={2} className="mr-2" />;
    } else if (status === "canceled") {
      return <MinusCircle color="#FFC107" strokeWidth={2} className="mr-2" />;
    }
  };

  const renderMessage = () => {
    if (status === "success") {
      return `${side} “${outcome}” placed`;
    } else if (status === "failure") {
      return "Order failed";
    } else if (status === "canceled") {
      return "Order canceled";
    }
  };

  return (
    <div className="fixed bottom-14 left-5 z-50">
      <Card shadow="lg" radius="sm" className="w-[320px] p-4 relative bg-white gap-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center font-medium text-lg">
            {renderIcon()}
            {renderMessage()}
          </div>
          <X color="black" strokeWidth={2.5} className="size-4 cursor-pointer" onClick={onClickHandler} />
        </div>
        <div>{status === "success" ? question : orderError}</div>
        <div className="flex items-center text-gray-600 text-sm font-sans">
          {shares} shares @ {price}¢
        </div>
      </Card>
    </div>
  );
};

export default TradeNotification;
