"use client";

import { useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import "@/styles/artTalk.css";
import Artalk from "artalk";
import "artalk/dist/Artalk.css";
import axios from "axios";
import crypto from "crypto";

interface CommentsProps {
  address: string | undefined;
  pageTitle?: string;
  pageKey?: string;
  session: any;
}

const ArtalkComment = ({ pageTitle, address, session }: CommentsProps) => {
  const pathname = usePathname();
  const artalk = useRef<Artalk>();
  const containerRef = useRef<HTMLDivElement>(null);

  // Generate anonymous username
  const generateAnonymousName = (): string => {
    const randomDigits = () => Math.floor(100 + Math.random() * 900);
    return `Anonymous ${randomDigits()}***${randomDigits()}`;
  };

  // Truncate username if it exceeds 30 characters
  const truncateUsername = (username: string): string => {
    if (username.length > 30) {
      return `${username.slice(0, 7)}...${username.slice(-5)}`;
    }
    return username;
  };

  // Add delete button
  const addDeleteButton = (ctx: any) => {
    const list = ctx.get("list"); // Get comment list object
    if (!list || !list.commentNodes || !Array.isArray(list.commentNodes)) {
      console.warn("Comment node data is empty or invalid:", list);
      return;
    }

    list.commentNodes.forEach((commentNode: any) => {
      const commentData = commentNode.data; // Get comment data
      const commentElement = commentNode.$el; // Get comment DOM element

      if (!commentData || !commentElement) return;

      // Check if delete button already exists
      const existingDeleteButton = commentElement.querySelector(".delete-button");

      if (address) {
        // If address exists, add delete button if not already present
        if (!existingDeleteButton && commentData.nick === address) {
          const deleteButton = createDeleteButton(commentData, ctx, commentNode);
          const footer = commentElement.querySelector(".atk-actions");

          if (!footer) {
            console.warn("Footer element not found for the comment:", commentElement);
            return;
          }

          footer.appendChild(deleteButton);
        }
      } else {
        // If address does not exist, remove existing delete button
        if (existingDeleteButton) {
          existingDeleteButton.remove();
        }
      }
    });
  };

  // Create delete button
  const createDeleteButton = (commentData: any, ctx: any, commentNode: any): HTMLButtonElement => {
    const deleteButton = document.createElement("button");
    deleteButton.textContent = "Delete";
    deleteButton.className = "delete-button";
    deleteButton.style.display = "inline-flex";
    deleteButton.style.marginLeft = "2px";
    deleteButton.style.color = "red";
    deleteButton.style.cursor = "pointer";
    deleteButton.style.fontSize = "13px";
    deleteButton.style.lineHeight = "25px";

    deleteButton.onclick = async () => {
      try {
        // Set button to loading state
        deleteButton.disabled = true;
        deleteButton.textContent = "Deleting...";

        const response = await axios({
          url: "/api/comments",
          method: "post",
          headers: {
            "Content-Type": "application/json",
          },
          data: {
            comment_id: commentData.id,
            cookie: session.cookie,
          },
        });

        if (response.data.data.tx === "ok") {
          console.log(`Comment ${commentData.id} deleted successfully`);
          commentNode.$el?.remove(); // Remove comment from DOM
          ctx.reload(); // Reload comment list
        } else {
          console.error(`Failed to delete comment ${commentData.id}:`, response.data);
          alert("Failed to delete. Please try again later!");
        }
      } catch (err) {
        console.error(`Failed to delete comment ${commentData.id}:`, err);
        alert("Failed to delete. Please check your network connection!");
      } finally {
        // Restore button state
        deleteButton.disabled = false;
        deleteButton.textContent = "Delete";
      }
    };

    return deleteButton;
  };

  useEffect(() => {
    if (containerRef.current) {
      // Register plugin: Listen for comment list load event
      Artalk.use(ctx => {
        ctx.on("list-loaded", () => {
          addDeleteButton(ctx);
        });
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [address]);

  useEffect(() => {
    if (containerRef.current) {
      // Register plugin: Listen for comment submission event
      Artalk.use(ctx => {
        ctx.on("editor-submit", () => {
          const user = ctx.get("user");

          let updatedName = generateAnonymousName();
          let updatedEmail = "<EMAIL>";

          if (address) {
            updatedName = address;
            updatedEmail = `${address}@predict.one`;
          }

          // Update user information
          user.update({
            name: updatedName,
            email: updatedEmail,
          });

          console.log("User information updated:", {
            name: updatedName,
            email: updatedEmail,
          });
        });
      });
      Artalk.use(ctx => {
        ctx.on("list-loaded", () => {
          const commentNodes = ctx.getCommentNodes(); // 获取评论节点列表
          if (!commentNodes || !Array.isArray(commentNodes)) {
            console.warn("Comment node data is empty or invalid:", commentNodes);
            return;
          }

          commentNodes.forEach((commentNode: any) => {
            const commentData = commentNode.data; // 获取评论数据
            const commentElement = commentNode.$el; // 获取评论的 DOM 元素

            if (!commentData || !commentElement) return;

            // Truncate username if it exceeds 30 characters
            if (commentData.nick && commentData.nick.length > 30) {
              const truncatedName = truncateUsername(commentData.nick);
              const nameElement = commentElement.querySelector(".atk-header .atk-nick"); // 假设用户名的 DOM 元素有这个类名

              if (nameElement) {
                nameElement.textContent = truncatedName;
              }
            }
          });
        });
      });

      // Initialize Artalk
      artalk.current = Artalk.init({
        el: containerRef.current,
        pageKey: pathname,
        pageTitle: pageTitle || document.title,
        server: "https://comments.predict.one",
        site: "Prediciton One Comment",
        useBackendConf: true,
        uaBadge: true,
        gravatar: {
          mirror: "https://www.gravatar.com/avatar/",
          params: "d=identicon",
        },
        avatarURLBuilder: comment => {
          if (comment.nick) {
            const hash = crypto.createHash("md5").update(comment.nick).digest("hex");
            return `https://www.gravatar.com/avatar/${hash}?d=identicon`;
          }
          return "https://www.gravatar.com/avatar/?d=mp&s=200";
        },
      });
    }

    return () => {
      if (artalk.current) {
        artalk.current.destroy();
        artalk.current = undefined;
      }
    };
  }, [pathname, pageTitle, address]);

  return <div ref={containerRef}></div>;
};

export default ArtalkComment;
