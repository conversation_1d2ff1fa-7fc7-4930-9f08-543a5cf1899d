import React, { useCallback, useEffect, useRef, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { getEventsByTagId } from "@/api/events";
import { base64DecodeByEn, base64DecodeByLanguage, formatTime, getCurrentTimestamp, getTotalVolumeClob } from "@/utils";
import { Spinner } from "@heroui/react";
import { ArrowRight, Clock5, Trophy } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useGlobalState } from "~~/services/store/store";

// Constants
const ITEMS_PER_PAGE = 10;
const SCROLL_THRESHOLD = 300; // 增加阈值，让滚动触发更早

// 排序函数：未结束的事件正序在前，已结束的事件倒序在后
const sortEventsByEndDate = (events: RelatedEvent[]): RelatedEvent[] => {
  const currentTime = new Date().toISOString();

  // 分离未结束和已结束的事件
  const ongoingEvents = events.filter(event => event.end_date > currentTime);
  const endedEvents = events.filter(event => event.end_date <= currentTime);

  // 未结束的事件：按 end_date 正序排列（即将结束的在前）
  const sortedOngoingEvents = ongoingEvents.sort(
    (a, b) => new Date(a.end_date).getTime() - new Date(b.end_date).getTime(),
  );

  // 已结束的事件：按 end_date 倒序排列（最近结束的在前）
  const sortedEndedEvents = endedEvents.sort((a, b) => new Date(b.end_date).getTime() - new Date(a.end_date).getTime());

  // 合并：未结束的在前，已结束的在后
  return [...sortedOngoingEvents, ...sortedEndedEvents];
};

interface RelatedEventsComponentProps {
  currentEvent: {
    event_tags?: Array<{
      tag_id?: string | number;
      tag?: { id?: string | number };
    }>;
    id?: string | number;
  };
}

interface RelatedEvent {
  id: number;
  title: string;
  description: string;
  icon: string;
  image: string;
  volume: number;
  volume_24hr: number;
  start_date: string;
  end_date: string;
  slug: string;
  closed: boolean;
  featured: boolean;
  event_markets: Array<{
    condition_id: string;
  }>;
}

const RelatedEventsComponent: React.FC<RelatedEventsComponentProps> = ({ currentEvent }) => {
  const { current_language } = useGlobalState().nativeCurrency;
  const { t } = useTranslation();
  const router = useRouter();
  const [relatedEvents, setRelatedEvents] = useState<RelatedEvent[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false); // 专门用于底部加载更多
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);
  const timeStamp = getCurrentTimestamp();
  const containerRef = useRef<HTMLDivElement>(null);

  const fetchRelatedEvents = useCallback(
    async (isLoadMore = false) => {
      if (!currentEvent?.event_tags || currentEvent.event_tags.length === 0) {
        return;
      }

      // 区分初始加载和加载更多
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }
      try {
        // Handle both possible structures: event_tags[0].tag_id or event_tags[0].tag.id
        const tagId = currentEvent.event_tags[0].tag_id || currentEvent.event_tags[0].tag?.id;

        if (!tagId || isNaN(Number(tagId))) {
          return;
        }

        const currentOffset = isLoadMore ? offset : 0;

        const response = await getEventsByTagId({
          limit: ITEMS_PER_PAGE,
          offset: currentOffset,
          closed: false,
          tag_id: Number(tagId),
        });

        const originalEvents = response.data.events;
        const hasMoreData = originalEvents.length === ITEMS_PER_PAGE;

        // Filter out the current event from related events
        const filteredEvents = originalEvents.filter((event: RelatedEvent) => event.id !== Number(currentEvent.id));

        if (isLoadMore) {
          // 加载更多时，将新数据与现有数据合并后重新排序
          setRelatedEvents(prev => {
            const combinedEvents = [...prev, ...filteredEvents];
            // 去重：根据事件ID去除重复项
            const uniqueEvents = combinedEvents.filter(
              (event, index, self) => index === self.findIndex(e => e.id === event.id),
            );
            return sortEventsByEndDate(uniqueEvents);
          });
        } else {
          // 初始加载时，直接排序
          const sortedEvents = sortEventsByEndDate(filteredEvents);
          setRelatedEvents(sortedEvents);
        }

        // Check if there are more items to load
        setHasMore(hasMoreData);

        if (isLoadMore) {
          setOffset(prev => prev + ITEMS_PER_PAGE);
        } else {
          setOffset(ITEMS_PER_PAGE);
        }
      } catch (error) {
        // Silently handle errors
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [currentEvent, offset],
  );

  useEffect(() => {
    if (currentEvent?.event_tags) {
      setOffset(0);
      setHasMore(true);
      fetchRelatedEvents(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentEvent]);

  const handleEventClick = useCallback(
    (event: RelatedEvent) => {
      const slug = base64DecodeByEn(event.slug);
      const conditionId = event.event_markets?.[0]?.condition_id || "";
      const url = `/event/${slug}?tid=${event.id}&conditionid=${conditionId}&lng=${current_language}&timeStamp=${timeStamp}`;
      router.push(url);
    },
    [current_language, timeStamp, router],
  );

  // Infinite scroll handler - 监听页面滚动
  const handleScroll = useCallback(() => {
    if (!containerRef.current || loadingMore || !hasMore) return;

    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();
    const containerBottom = containerRect.bottom;
    const windowHeight = window.innerHeight;

    // 当容器底部接近视窗底部时加载更多
    if (containerBottom <= windowHeight + SCROLL_THRESHOLD) {
      fetchRelatedEvents(true);
    }
  }, [loadingMore, hasMore, fetchRelatedEvents]);

  // Add scroll event listener - 监听页面滚动，添加节流
  useEffect(() => {
    if (!containerRef.current) return;

    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", throttledHandleScroll, { passive: true });
    return () => window.removeEventListener("scroll", throttledHandleScroll);
  }, [handleScroll]);

  const formatVolume = (event: RelatedEvent) => {
    const volume = getTotalVolumeClob(event.event_markets);

    if (volume >= 1000000) return `$${(volume / 1000000).toFixed(1)}M`;
    if (volume >= 1000) return `$${(volume / 1000).toFixed(1)}K`;
    return `$${volume.toFixed(0)}`;
  };

  if (loading && relatedEvents.length === 0) {
    return (
      <div className="flex justify-center items-center py-8">
        <Spinner size="md" />
        <span className="ml-2 text-sm text-gray-500">{t("loading_related_events") || "Loading related events..."}</span>
      </div>
    );
  }

  if (relatedEvents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <div className="text-gray-400 text-md">{t("no_related_events_found") || "No related events found"}</div>
      </div>
    );
  }

  return (
    <div className="w-full mb-12">
      <div ref={containerRef} className="space-y-1">
        {relatedEvents.map((event, index) => (
          <div
            key={event.id}
            className="group relative flex items-start sm:items-center p-3 sm:p-4 rounded-xl cursor-pointer
                       bg-gradient-to-r from-transparent via-slate-50/40 to-transparent
                       hover:from-blue-50/50 hover:via-slate-100/60 hover:to-blue-50/50
                       hover:shadow-lg hover:shadow-blue-500/15 hover:scale-[1.01]
                       transition-all duration-300 ease-out animate-fade-in-up
                       border-l-2 border-l-transparent hover:border-l-blue-500/70
                       before:absolute before:inset-0 before:rounded-xl before:bg-gradient-to-r
                       before:from-blue-500/0 before:to-slate-400/0 before:opacity-0
                       hover:before:from-blue-500/5 hover:before:to-slate-400/5 hover:before:opacity-100
                       before:transition-all before:duration-300"
            onClick={() => handleEventClick(event)}
            style={{
              animationDelay: `${index * 50}ms`,
            }}
          >
            <div className="relative flex-shrink-0 mr-3 sm:mr-4">
              <div className="relative">
                <Image
                  alt=""
                  loading="lazy"
                  width="40"
                  height="40"
                  src={event.icon || event.image || "/image_alt.jpg"}
                  className="rounded-full object-cover size-10 sm:size-12 shadow-md shadow-slate-300/50
                           group-hover:shadow-lg group-hover:shadow-blue-500/30 transition-all duration-300"
                />
                <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-blue-500/20 to-slate-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </div>

            <div className="relative flex-grow min-w-0 z-10">
              <div className="text-sm sm:text-sm font-semibold text-gray-900 line-clamp-2 mb-1.5 sm:mb-2 group-hover:text-blue-700 transition-colors duration-300">
                {base64DecodeByLanguage(event.title)}
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center text-xs space-y-1 sm:space-y-0 sm:space-x-4">
                <div className="flex items-center text-slate-600 group-hover:text-blue-600 transition-colors duration-300">
                  <Trophy className="size-3 mr-1 sm:mr-1.5 text-blue-500 flex-shrink-0" />
                  <span className="font-medium truncate">{formatVolume(event)} Vol.</span>
                </div>

                <div className="flex items-center text-slate-600 group-hover:text-blue-600 transition-colors duration-300">
                  <Clock5 className="size-3 mr-1 sm:mr-1.5 text-slate-500 flex-shrink-0" />
                  <span className="font-medium truncate">{formatTime(event.end_date)} UTC</span>
                </div>
              </div>
            </div>

            <div className="relative flex-shrink-0 ml-2 sm:ml-3 z-10 self-start sm:self-center mt-1 sm:mt-0">
              <div className="flex items-center px-2 sm:px-3 py-1 sm:py-1.5 bg-gradient-to-r from-slate-100 to-slate-200 hover:from-blue-100 hover:to-slate-100 rounded-full shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105 border border-slate-200/50 hover:border-blue-300/50">
                <span className="text-xs text-slate-700 hover:text-blue-700 font-medium mr-1 sm:mr-1.5 transition-colors duration-300 whitespace-nowrap">
                  {t("view") || "View"}
                </span>
                <ArrowRight className="size-3 text-slate-600 hover:text-blue-600 group-hover:translate-x-0.5 transition-all duration-300 flex-shrink-0" />
              </div>
            </div>
          </div>
        ))}

        {/* 底部加载更多指示器 - 蓝银科技感设计 */}
        {loadingMore && (
          <div className="flex justify-center items-center py-4 mb-4">
            <div className="relative">
              <div className="w-6 h-6 border-2 border-slate-200 border-t-blue-500 rounded-full animate-spin"></div>
              <div className="absolute inset-0 w-6 h-6 border-2 border-transparent border-r-slate-400/40 rounded-full animate-spin-reverse"></div>
            </div>
            <span className="ml-3 text-sm font-medium text-slate-600">
              {t("loading_more_related_events") || "Loading more related events..."}
            </span>
          </div>
        )}

        {/* End indicator - 简洁设计 */}
        {!hasMore && relatedEvents.length > 0 && (
          <div className="text-center py-4 mt-2">
            <span className="text-sm text-slate-400">{t("no_more_related_events") || "No more related events"}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default RelatedEventsComponent;
