import React, { useEffect, useState } from "react";
import Image from "next/image";
import { getCommentsList } from "@/api/markets/detail";
import { Button, Input } from "@heroui/react";
import { <PERSON><PERSON>lert, ThumbsUp } from "lucide-react";

// import { base64DecodeByLanguage } from "@/utils";
// import { useGlobalState } from "~~/services/store/store";

interface Comment {
  id: number;
  comment: string;
  likes: number;
  timestamp: string;
  users: {
    uuid_key: string;
    avatar: string;
  };
  isLiked?: boolean; // 添加 isLiked 属性
}

const CommentsComponent = () => {
  // const { currentEvent } = props;
  const [commentsList, setCommentsList] = useState<Comment[]>([]);
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyText, setReplyText] = useState<string>("");
  const [newComment, setNewComment] = useState<string>("");

  useEffect(() => {
    const fetchComments = async () => {
      try {
        const response = await getCommentsList();
        const commentsWithLikes = response.data.comments.map((comment: Comment) => ({
          ...comment,
          isLiked: false, // 初始化 isLiked 属性
        }));
        setCommentsList(commentsWithLikes);
      } catch (error) {
        console.error("Error fetching commentsList:", error);
      }
    };

    fetchComments();
  }, []);

  const handleReplyClick = (index: number) => {
    setReplyingTo(replyingTo === index ? null : index);
  };

  const handleReplyChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setReplyText(event.target.value);
  };

  const handleReplySubmit = (index: number) => {
    // Handle reply submission logic here
    console.log(`Reply to comment ${index}: ${replyText}`);
    setReplyText("");
    setReplyingTo(null);
  };

  const handleThumbsUpClick = (id: number) => {
    setCommentsList(prevComments =>
      prevComments.map(comment =>
        comment.id === id
          ? {
              ...comment,
              isLiked: !comment.isLiked,
              likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1,
            }
          : comment,
      ),
    );
  };

  const handleNewCommentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewComment(event.target.value);
  };

  const handleNewCommentSubmit = () => {
    // Handle new comment submission logic here
    console.log(`New comment: ${newComment}`);
    setNewComment("");
  };

  return (
    <div>
      <div className="w-full flex items-center gap-2 mb-4 mt-2">
        <Input
          variant={"flat"}
          size="lg"
          placeholder="Write a comment..."
          className="border rounded-md"
          onChange={handleNewCommentChange}
          value={newComment}
          classNames={{
            inputWrapper: ["bg-transparent"],
          }}
          type="text"
        />
        <Button size="lg" radius="sm" className="bg-blue-600 font-semibold text-white" onClick={handleNewCommentSubmit}>
          Post
        </Button>
      </div>

      {commentsList.length > 0 && (
        <div className="flex flex-center px-6 py-1 my-1 gap-2 border bg-gray-100 text-md rounded-lg">
          <ShieldAlert className="size-6" />
          <p className="font-medium">Beware of external links, they may be phishing attacks.</p>
        </div>
      )}

      {commentsList.map((comment, index) => (
        <div key={comment?.id} className="flex items-start space-x-4 p-4 border-b border-gray-200 text-sm">
          <Image
            className="size-10 object-cover rounded-md"
            src={comment?.users?.avatar || "/image_alt.jpg"}
            alt=""
            width={40}
            height={40}
          />

          <div className="flex flex-col w-full gap-1">
            <div className="font-semibold">{comment?.users?.uuid_key}</div>
            <div className="text-gray-600">{comment?.comment}</div>
            <div className="flex items-center text-gray-400 font-medium gap-2">
              <ThumbsUp
                strokeWidth={2}
                color={comment.isLiked ? "#FDD835" : "#9E9E9E"}
                className="size-4 cursor-pointer"
                onClick={() => handleThumbsUpClick(comment.id)}
              />
              <div className="text-gray-500">{comment.likes}</div>
              <div className="text-gray-500 cursor-pointer" onClick={() => handleReplyClick(index)}>
                Reply
              </div>
            </div>
            {replyingTo === index && (
              <div className="w-full flex items-center gap-2">
                <Input
                  variant={"flat"}
                  size="md"
                  placeholder="Reply to this comment..."
                  // startContent={<Search className="text-muted-foreground size-4" />}
                  className="border rounded-md"
                  onChange={handleReplyChange}
                  classNames={{
                    inputWrapper: ["bg-transparent"],
                  }}
                  type="search"
                />
                <Button
                  size="md"
                  radius="sm"
                  // isLoading={buttonState === "loading"}
                  className="bg-blue-600 font-semibold text-white"
                  onClick={() => handleReplySubmit(index)}
                >
                  Post
                </Button>
                <span className="font-bold text-gray-500 cursor-pointer" onClick={() => setReplyingTo(null)}>
                  Cancel
                </span>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default CommentsComponent;
