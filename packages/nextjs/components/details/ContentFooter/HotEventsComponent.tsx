import React, { useCallback, useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { getBannerByRegion } from "@/api/markets";
import { formatTime, getCurrentTimestamp } from "@/utils";
import { Spinner } from "@heroui/react";
import { ArrowRight, Clock5, TrendingUp } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useGlobalState } from "~~/services/store/store";

// 热门事件数据类型定义
interface HotEvent {
  id: number;
  event_id: number;
  feature: string;
  image_url: string;
  region: string;
  sub_title: string;
  timestamp: string;
  title: string;
}

interface HotEventsComponentProps {
  className?: string;
  showBorder?: boolean;
}

const HotEventsComponent: React.FC<HotEventsComponentProps> = ({ className = "", showBorder = true }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { current_language } = useGlobalState().nativeCurrency;
  const timeStamp = getCurrentTimestamp();

  const [hotEvents, setHotEvents] = useState<HotEvent[]>([]);
  const [loading, setLoading] = useState(true);

  // 获取热门事件数据
  const fetchHotEvents = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getBannerByRegion(current_language);

      if (response?.data?.banner) {
        // 过滤热门事件并限制数量
        const featuredEvents = response.data.banner.filter((event: HotEvent) => event.feature).slice(0, 6);

        setHotEvents(featuredEvents);
      }
    } catch (error) {
      console.error("Failed to fetch hot events:", error);
      setHotEvents([]);
    } finally {
      setLoading(false);
    }
  }, [current_language]);

  useEffect(() => {
    fetchHotEvents();
  }, [fetchHotEvents]);

  // 处理事件点击
  const handleEventClick = useCallback(
    (event: HotEvent) => {
      const slug = event.title.replace(/\?/g, "").replace(/ /g, "-");
      const url = `/event/${slug}?tid=${event.event_id}&lng=${current_language}&timeStamp=${timeStamp}`;
      router.push(url);
    },
    [current_language, timeStamp, router],
  );

  const containerClasses = showBorder
    ? `w-full max-w-[400px] bg-white rounded-xl border border-gray-200 shadow-sm ${className}`
    : `w-full ${className}`;

  if (loading) {
    return (
      <div className={containerClasses}>
        <div className="flex justify-center items-center py-8">
          <Spinner size="md" />
          <span className="ml-2 text-sm text-gray-500">{t("loading") || "Loading..."}</span>
        </div>
      </div>
    );
  }

  if (hotEvents.length === 0) {
    return (
      <div className={containerClasses}>
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <TrendingUp className="size-8 text-gray-300 mb-2" />
          <div className="text-gray-400 text-sm">{t("no_data") || "No data available"}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={containerClasses}>
      {/* 标题区域 */}
      {showBorder && (
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-2">
            <TrendingUp className="size-5 text-orange-500" />
            <div className="text-lg font-semibold text-gray-900">{t("hot_events") || "Hot Events"}</div>
          </div>
          <div className="flex items-center text-xs text-gray-500">
            <span>
              {hotEvents.length} {t("items") || "items"}
            </span>
          </div>
        </div>
      )}

      {/* 事件列表 */}
      <div className={`space-y-1 ${showBorder ? "p-2" : ""}`}>
        {hotEvents.map((event, index) => (
          <div
            key={event.id}
            className="group relative flex items-start sm:items-center p-3 rounded-xl cursor-pointer 
                       bg-gradient-to-r from-transparent via-orange-50/30 to-transparent
                       hover:from-orange-50/40 hover:via-red-50/30 hover:to-orange-50/40
                       hover:shadow-lg hover:shadow-orange-400/10 hover:scale-[1.01]
                       transition-all duration-300 ease-out animate-fade-in-up
                       border-l-2 border-l-transparent hover:border-l-orange-400/60
                       before:absolute before:inset-0 before:rounded-xl before:bg-gradient-to-r 
                       before:from-orange-400/0 before:to-red-400/0 before:opacity-0
                       hover:before:from-orange-400/3 hover:before:to-red-400/3 hover:before:opacity-100
                       before:transition-all before:duration-300"
            onClick={() => handleEventClick(event)}
            style={{
              animationDelay: `${index * 50}ms`,
            }}
          >
            {/* 事件图片 */}
            <div className="relative flex-shrink-0 mr-3">
              <div className="relative">
                <Image
                  alt=""
                  loading="lazy"
                  width="40"
                  height="40"
                  src={event.image_url || "/image_alt.jpg"}
                  className="rounded-full object-cover size-10 shadow-md shadow-gray-300/50
                           group-hover:shadow-lg group-hover:shadow-orange-400/30 transition-all duration-300"
                  onError={e => {
                    e.currentTarget.src = "/image_alt.jpg";
                  }}
                />
                <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-orange-400/20 to-red-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                {/* 热门标识点 */}
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                  <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                </div>
              </div>
            </div>

            {/* 事件信息 */}
            <div className="relative flex-grow min-w-0 z-10">
              <div className="text-sm font-semibold text-gray-900 line-clamp-2 mb-1.5 group-hover:text-orange-700 transition-colors duration-300">
                {event.title}
              </div>

              {event.sub_title && (
                <div className="text-xs text-gray-600 line-clamp-1 mb-1 group-hover:text-orange-600 transition-colors duration-300">
                  {event.sub_title}
                </div>
              )}

              <div className="flex items-center text-xs text-gray-500 group-hover:text-orange-500 transition-colors duration-300">
                <Clock5 className="size-3 mr-1 flex-shrink-0" />
                <span className="font-medium truncate">{formatTime(event.timestamp)} UTC</span>
              </div>
            </div>

            {/* 查看按钮 */}
            <div className="relative flex-shrink-0 ml-2 z-10 self-start sm:self-center mt-1 sm:mt-0">
              <div
                className="flex items-center px-2 py-1 bg-gradient-to-r from-orange-100 to-red-100 
                            hover:from-orange-200 hover:to-red-200 rounded-full 
                            shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105
                            border border-orange-200/50 hover:border-orange-300/50"
              >
                <span className="text-xs text-orange-700 hover:text-orange-800 font-medium mr-1 transition-colors duration-300 whitespace-nowrap">
                  {t("view") || "View"}
                </span>
                <ArrowRight className="size-3 text-orange-600 hover:text-orange-700 group-hover:translate-x-0.5 transition-all duration-300 flex-shrink-0" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HotEventsComponent;
