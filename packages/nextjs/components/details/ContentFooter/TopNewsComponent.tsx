import React, { useCallback, useEffect, useRef, useState } from "react";
import { getTopNewsListData } from "@/api/events";
import { Image, Link } from "@heroui/react";
import Fuse from "fuse.js";
import { throttle } from "lodash";

interface TopNewsComponentProps {
  eventTitle: string;
}

const TopNewsComponent: React.FC<TopNewsComponentProps> = ({ eventTitle }) => {
  const [news, setNews] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const observer = useRef<IntersectionObserver | null>(null);

  const fetchNews = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getTopNewsListData();
      const allArticles = response.data.news;

      // Extract and deduplicate articles
      const articles: any[] = [];
      const seenTitles = new Set();

      allArticles.forEach((article: any) => {
        if (article.link && !seenTitles.has(article.title)) {
          seenTitles.add(article.title);
          articles.push({
            url: article.link,
            title: article.title,
            description: article.description,
            publishedAt: article.id,
          });
        }
      });

      // Perform fuzzy search using Fuse.js
      const fuse = new Fuse(articles, {
        keys: ["title", "description"], // Fields to search
        threshold: 1, // Lower threshold for stricter matches
      });

      const results = fuse.search(eventTitle);
      const filteredArticles = results.map(result => result.item);

      if (filteredArticles.length === 0) {
        setLoading(false);
        return;
      }

      // Update news state
      setNews(prevNews => [...prevNews, ...filteredArticles]);
    } catch (err) {
      console.error("Failed to fetch news:", err);
    } finally {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventTitle, page]);

  useEffect(() => {
    if (eventTitle) {
      fetchNews();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventTitle]);

  // Observe the last news element to implement infinite scrolling
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const lastNewsElementRef = useCallback(
    throttle((node: any) => {
      if (loading === true) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver(entries => {
        if (entries[0].isIntersecting) {
          setPage(prevPage => prevPage + 1);
        }
      });
      if (node) observer.current.observe(node);
    }, 1000),
    [loading],
  );

  return (
    <div className="space-y-4">
      {news.length === 0 && !loading && (
        <div className="flex justify-center items-center h-32 bg-gray-100 rounded-lg">
          <div className="text-gray-500">No relevant news</div>
        </div>
      )}

      {news.map((article, index) => (
        <div
          key={index}
          className="flex flex-col md:flex-row items-center w-full hover:bg-gray-100 p-2 rounded-lg"
          ref={index === news.length - 1 ? lastNewsElementRef : null}
        >
          <Image
            src={article.urlToImage || "/news.png"}
            alt={article.title}
            radius="lg"
            shadow="lg"
            width={180}
            height={86}
            className="w-full h-auto object-cover bg-transparent"
          />
          <div className="flex flex-col w-full md:w-2/3 ml-0 md:ml-4 mt-4 md:mt-0">
            <Link href={article.url} className="text-blue-500 font-semibold text-xl truncate">
              {article.title}
            </Link>
            <div className="line-clamp-3">{article.description}</div>
          </div>
        </div>
      ))}

      <div className="text-gray-500 text-medium mt-1 flex flex-center">{loading === true && "Loading..."}</div>
    </div>
  );
};

export default TopNewsComponent;
