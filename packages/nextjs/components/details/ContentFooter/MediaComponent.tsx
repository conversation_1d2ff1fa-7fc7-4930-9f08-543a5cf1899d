import React from "react";

interface MediaComponentProps {
  url: string;
}

const MediaComponent: React.FC<MediaComponentProps> = ({ url }) => {
  const getYouTubeEmbedUrl = (url: string) => {
    const youtubeRegex =
      /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
    const match = url.match(youtubeRegex);
    return match ? `https://www.youtube.com/embed/${match[1]}` : url;
  };

  const embedUrl = getYouTubeEmbedUrl(url);

  return (
    <div className="flex w-full h-full justify-center items-center rounded-lg bg-gray-100">
      {url ? (
        <iframe
          className="h-[300px] md:w-full md:h-[500px] p-4"
          src={embedUrl}
          title="YouTube video player"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          referrerPolicy="strict-origin-when-cross-origin"
          allowFullScreen
        ></iframe>
      ) : (
        <div className="flex justify-center items-center h-32 w-full bg-gray-100 rounded-lg">
          <div className="text-gray-500">No media resources available</div>
        </div>
      )}
    </div>
  );
};

export default MediaComponent;
