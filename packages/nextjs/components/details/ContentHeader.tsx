import React, { useEffect, useState } from "react";
import Image from "next/image";
import { base64DecodeByLanguage, formatLargeAmountWithCommas, formatTime, getTotalVolumeClob } from "@/utils";
import { ChevronsLeftRight, Clock5, FileText, Link as LinkI<PERSON>, Star, Trophy } from "lucide-react";
import { useMediaQuery } from "react-responsive";
import { useGlobalState } from "~~/services/store/store";

interface HeaderType {
  currentEvent: any;
  setIsShowiFrameModal: any;
}
const ContentHeader: React.FC<HeaderType> = (props: any) => {
  const { currentEvent, setIsShowiFrameModal } = props;
  const { current_language } = useGlobalState().nativeCurrency;
  const [eventTitle, setEventTitle] = useState<string>("");
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const volumeclob = getTotalVolumeClob(currentEvent?.event_markets);

  useEffect(() => {
    if (currentEvent?.title) {
      setEventTitle(base64DecodeByLanguage(currentEvent?.title));
    }
  }, [current_language, currentEvent?.title]);

  return (
    <div className="w-full flex flex-col md:mt-4 mt-2">
      <div className="flex flex-row sm:flex-col sm:items-center mb-3 sm:mb-4">
        <div className="flex-center rounded-full flex-shrink-0">
          {currentEvent?.icon && (
            <Image
              alt=""
              loading="lazy"
              width="48"
              height="48"
              decoding="async"
              src={currentEvent?.icon == "" ? "/image_alt.jpg" : currentEvent?.icon}
              className="rounded-full object-cover size-12 sm:size-16"
            />
          )}
        </div>
        <div className="flex items-center ml-3 sm:ml-0 sm:mt-4 text-lg sm:text-2xl font-bold flex-grow line-clamp-2 sm:text-center">
          {eventTitle}
        </div>
      </div>

      <div className="w-full flex flex-col gap-2 sm:gap-0 sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-5">
        <div className="flex items-center text-sm sm:text-base">
          <div data-state="closed" className="flex-center">
            <Trophy className="size-3 sm:size-4 mr-1 text-gray-500" />
          </div>
          <span className="text-gray-500 mr-2 sm:mr-3 text-xs sm:text-sm">
            ${formatLargeAmountWithCommas(volumeclob)} Vol.
          </span>
          <span className="flex items-center text-gray-500">
            <Clock5 className="size-3 sm:size-4 mr-1" />
            <span className="text-xs sm:text-sm">
              {currentEvent?.end_date && formatTime(currentEvent?.end_date) + " UTC"}
            </span>
          </span>
        </div>

        {!isMobile && (
          <div className="flex items-center">
            <Star className="size-5 m-1" />
            <FileText className="size-5 m-1" />
            <ChevronsLeftRight
              className="size-5 m-1 cursor-pointer"
              onClick={() => {
                setIsShowiFrameModal(true);
              }}
            />
            <LinkIcon className="size-5 m-1" />
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentHeader;
