import React, { useEffect, useState } from "react";
import { siteConfig } from "@/configs/site";
import { base64DecodeByLanguage } from "@/utils";
import { Modal, ModalBody, ModalContent, ModalHeader, Select, SelectItem, addToast } from "@heroui/react";
import { Image, Link } from "@heroui/react";
import { ArrowRight, CheckCircleIcon } from "lucide-react";
import ReactMarkdown from "react-markdown";
import { useMediaQuery } from "react-responsive";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { ghcolors } from "react-syntax-highlighter/dist/esm/styles/prism";
import { useGlobalState } from "~~/services/store/store";

interface ConfirmModalProps {
  visible: boolean;
  marketsDataList: any[];
  onClose: () => void;
}

const baseUrl = process.env.NEXT_PUBLIC_OL_URL
  ? process.env.NEXT_PUBLIC_OL_URL
  : `http://localhost:${process.env.PORT || 3000}`;

const IframeModal: React.FC<ConfirmModalProps> = ({ visible, onClose, marketsDataList }) => {
  const { current_language } = useGlobalState().nativeCurrency;
  const [currentSelected, setCurrentSelected] = useState(marketsDataList[0]?.condition_id || "");
  const selectedMarket = marketsDataList.find(market => market.condition_id === currentSelected) || {};
  const { condition_id = "", question_market } = selectedMarket;
  const [groupItemTitle, setGroupItemTitle] = useState<string>("");
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const [copyButtonText, setCopyButtonText] = useState("Copy");

  useEffect(() => {
    if (question_market?.question) {
      setGroupItemTitle(base64DecodeByLanguage(question_market?.question));
    }
  }, [current_language, question_market?.question]);

  const handleSelectChange = (value: string) => {
    setCurrentSelected(value);
  };

  useEffect(() => {
    if (marketsDataList.length > 0) {
      setCurrentSelected(marketsDataList[0].condition_id);
    }
  }, [marketsDataList]);

  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(markdown)
      .then(() => {
        addToast({
          title: "Address Copied",
          timeout: 500,
          shouldShowTimeoutProgess: true,
          color: "success",
        });
        setCopyButtonText("Copied");
        setTimeout(() => {
          setCopyButtonText("Copy");
        }, 1000);
      })
      .catch(err => {
        console.error("copy failed", err);
      });
  };

  const markdown = `
\`\`\`javascript
<iframe
  title="predictone-eventcard-iframe"
  src=\`${baseUrl}/iframecard?conditionid=${condition_id}\`
  width="500"
  height="200"
  frameBorder="0"
/>
\`\`\`
  `;

  return (
    <Modal size="3xl" isOpen={visible} onClose={onClose}>
      <ModalContent>
        <ModalHeader className="flex flex-center text-xl">
          <div>{"Embed"}</div>
        </ModalHeader>

        <ModalBody>
          <div className={`flex ${isMobile ? "flex-col" : "flex-row"} flex-center gap-4 h-[300px] p-2 mb-4`}>
            <div className="w-full md:w-1/2 h-full flex flex-col overflow-scroll gap-4">
              <div className="flex w-full items-center justify-between">
                <Select
                  placeholder="Select a market"
                  value={currentSelected}
                  disabledKeys={currentSelected}
                  onChange={e => handleSelectChange(e.target.value)}
                  className="max-w-[80%]"
                >
                  {marketsDataList.map(market => (
                    <SelectItem key={market.condition_id}>
                      {base64DecodeByLanguage(market.question_market?.question)}
                    </SelectItem>
                  ))}
                </Select>
                <div
                  onClick={copyToClipboard}
                  className="flex items-center bg-blue-600 text-sm text-white p-2 rounded-lg cursor-pointer"
                >
                  {copyButtonText === "Copied" && <CheckCircleIcon className="size-4 mr-1" aria-hidden="true" />}
                  {copyButtonText}
                </div>
              </div>
              <ReactMarkdown
                components={{
                  code({ className, children, ...props }) {
                    const match = /language-(\w+)/.exec(className || "");
                    return match ? (
                      <SyntaxHighlighter
                        // @ts-ignore
                        style={ghcolors}
                        language={match[1]}
                        PreTag="div"
                        customStyle={{ background: "bg-gray-100", border: "none", padding: 0 }}
                        {...props}
                      >
                        {String(children).replace(/\n$/, "")}
                      </SyntaxHighlighter>
                    ) : (
                      <code className={className} {...props}>
                        {children}
                      </code>
                    );
                  },
                }}
              >
                {markdown}
              </ReactMarkdown>
            </div>
            <div className="w-full md:w-1/2 h-full bg-gray-100 flex flex-center p-6">
              <div className="flex flex-col w-full bg-white shadow-md rounded-lg px-4 py-4 gap-4">
                <div className="flex items-center gap-2">
                  <Image
                    className="size-10 object-cover rounded-md"
                    src={question_market?.image ? question_market?.image : "/image_alt.jpg"}
                    alt="/image_alt.jpg"
                    width={40}
                    height={40}
                  />
                  <Link className="flex items-center group cursor-pointer">
                    <span className="h-full text-gray-900 text-sm font-semibold">{groupItemTitle}</span>
                  </Link>
                </div>

                <div className="flex items-center justify-between w-full">
                  <div className={"bg-transparent rounded-xl cursor-pointer"}>
                    <Link href="/" className="flex items-center">
                      <Image className="size-6 rounded-full" src="/logo.png" alt="" width={24} height={24} />
                      <div className="text-sm ml-1 font-semibold bg-clip-text bg-gradient-to-br from-blue-500 to-purple-700 text-transparent">
                        {siteConfig.name}
                      </div>
                    </Link>
                  </div>
                  <div className="flex flex-center bg-gray-200 text-xs px-2 py-1 rounded-md gap-2 cursor-pointer">
                    {"View Market"}
                    <ArrowRight className="size-3" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default IframeModal;
