import React, { useCallback, useEffect, useState } from "react";
import { getPositionsListDataInPofolio } from "@/api/order";
import NotificationCard from "@/components/other/NotificationCard";
import { onClickClaim } from "@/contracts/claimContract";
import { useClobApiOptimized } from "@/hooks/secret/useClobApiOptimized";
import { base64DecodeByLanguage, getConditionId, getItem, getPositionSide, processNumber } from "@/utils";
import { isTokenExpiredError } from "@/utils/auth/tokenErrorHandler";
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Divider, Spinner } from "@heroui/react";
import confetti from "canvas-confetti";
import { SquareCheck } from "lucide-react";
import { useSession } from "next-auth/react";
import { useGlobalState } from "~~/services/store/store";

interface CurrentPositionData {
  asset: string;
  totalbought: number;
  size: number;
  currentvalue: number;
  market: { outcome_prices: [string] };
}

const ClaimCard = ({ address, marketsDataList, outcome_prices, selectedQuestionId }: any) => {
  const [proxyWallet, setProxyWallet] = useState<string>("");
  const { data: session } = useSession();
  const { walletType } = useGlobalState();
  const { current_language } = useGlobalState().nativeCurrency;

  const clobApiResult = useClobApiOptimized();
  const clobApis = clobApiResult?.clobApis || null;

  const BULK_DATA_LIMIT = parseInt(process.env.NEXT_PUBLIC_BULK_DATA_LIMIT || "99999");

  const [currentCardData, setCurrentCardData] = useState<any>({});
  const [buttonState, setButtonState] = useState("none");
  const [currentPositionData, setCurrentPositionData] = useState<CurrentPositionData>();
  const [groupItemTitle, setGroupItemTitle] = useState<string>("");
  const [showNotification, setShowNotification] = useState(false);
  const [notiTitle, setNotiTitle] = useState<string>("");
  const [notiStatus, setNotiStatus] = useState<"success" | "failure">("success");
  const [isCanClaim, setIsCanClaim] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const notificationParams = {
    setNotiTitle,
    setNotiStatus,
    setButtonState,
    setShowNotification,
  };

  const currentConditionId = getConditionId(marketsDataList, selectedQuestionId);
  const asset = currentPositionData?.asset;
  const current_clob_token_id =
    currentCardData?.clob_token_ids && currentCardData.clob_token_ids?.length > 1
      ? outcome_prices[0] === "1"
        ? currentCardData.clob_token_ids[0]
        : currentCardData.clob_token_ids[1]
      : undefined;

  const getMatchingMarket = useCallback(
    (itemId: number) => marketsDataList.find((item: any) => item.question_market.id === itemId),
    [marketsDataList],
  );

  // 在客户端获取 proxyWallet，避免 SSR 问题
  useEffect(() => {
    const wallet = getItem(`login_proxyWallet`);
    if (wallet) {
      setProxyWallet(wallet);
    }
  }, []);

  // 监听登录状态变化，重置相关状态
  useEffect(() => {
    setError(null);
    setButtonState("none");

    // 如果用户登出，立即清理持仓数据
    if (!address || !session) {
      console.log("🔄 ClaimCard: User logged out, clearing position data");
      setCurrentPositionData({
        asset: current_clob_token_id,
        totalbought: 0,
        size: 0,
        currentvalue: 0,
        market: { outcome_prices: outcome_prices },
      });
      setIsCanClaim(false);
    }
  }, [address, session, clobApis, current_clob_token_id, outcome_prices]);

  useEffect(() => {
    if (selectedQuestionId) {
      const matchingMarket = getMatchingMarket(selectedQuestionId);
      setCurrentCardData(matchingMarket?.question_market);
    }
  }, [selectedQuestionId, marketsDataList, getMatchingMarket]);

  useEffect(() => {
    const checkIsCanClaim = async () => {
      if (!currentConditionId) {
        setIsLoading(true);
        setError(null);
        setIsCanClaim(false);
        return;
      }

      // 如果用户未登录，显示结果但持仓为0
      if (!address || !session || !clobApis) {
        setCurrentPositionData({
          asset: current_clob_token_id,
          totalbought: 0,
          size: 0,
          currentvalue: 0,
          market: { outcome_prices: outcome_prices },
        });
        setIsCanClaim(false);
        setIsLoading(false);
        setError(null);
        return;
      }

      setError(null);
      setIsLoading(true);

      try {
        // 底层的 initRequest 已经有重试机制，直接依赖底层重试
        const res = await getPositionsListDataInPofolio(clobApis, walletType, {
          offset: 0,
          limit: BULK_DATA_LIMIT,
        });

        if (res?.data?.data && Array.isArray(res.data.data)) {
          const matchingPositions = res.data.data.filter((position: any) => {
            const positionConditionId = position.conditionId?.String || position.conditionId;
            return positionConditionId === currentConditionId;
          });

          if (matchingPositions.length > 0) {
            const specificPosition = matchingPositions.find((position: any) => {
              const positionAsset = position.asset?.String || position.asset;
              return positionAsset === current_clob_token_id;
            });

            if (specificPosition) {
              const normalizedPosition = {
                asset: specificPosition.asset?.String || specificPosition.asset,
                totalbought: Number(specificPosition.totalBought || 0),
                size: Number(specificPosition.size || 0),
                currentvalue: Number(specificPosition.currentValue || 0),
                market: { outcome_prices: outcome_prices },
              };

              setCurrentPositionData(normalizedPosition);
              setIsCanClaim(true);
            } else {
              setIsCanClaim(true);
            }
          } else {
            setIsCanClaim(false);
          }
        } else {
          setIsCanClaim(false);
        }
      } catch (error) {
        let errorMessage = "Failed to load claim data";
        if (error instanceof Error) {
          if (error.message.includes("No API credentials")) {
            errorMessage = "Please enable trading to view positions";
          } else if (error.message.includes("Incomplete API credentials")) {
            errorMessage = "Trading credentials incomplete, please re-enable trading";
          } else if (error.message.includes("Signer is undefined")) {
            errorMessage = "Wallet connection issue, please reconnect";
          } else {
            errorMessage = error.message;
          }
        }

        setError(errorMessage);
        setIsCanClaim(false);
      } finally {
        setIsLoading(false);
      }
    };

    // 防抖机制：延迟执行，避免短时间内重复调用
    const isLoginStateChange = !address || !session || !clobApis;
    const debounceDelay = isLoginStateChange ? 50 : 100;

    const timeoutId = setTimeout(() => {
      checkIsCanClaim();
    }, debounceDelay);

    // 清理函数
    return () => {
      clearTimeout(timeoutId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [address, currentConditionId, current_clob_token_id, buttonState, walletType, session, clobApis]);

  // 简单的重试函数
  const handleRetry = useCallback(() => {
    setError(null);
    // 通过更新 buttonState 来触发 useEffect 重新执行
    setButtonState(prev => (prev === "retry" ? "none" : "retry"));
  }, []);

  useEffect(() => {
    if (currentCardData?.question) {
      setGroupItemTitle(base64DecodeByLanguage(currentCardData.question));
    }
  }, [current_language, currentCardData?.question]);

  const outcome = outcome_prices.length > 0 && outcome_prices[0] === "1" ? "Yes" : "No";
  const size = processNumber(currentPositionData?.size || 0, 18);
  const side = asset ? getPositionSide(asset, currentCardData.clob_token_ids) : "";
  const valuePerShare = 1;
  const total = (Number(size) * Number(valuePerShare)).toFixed(2);

  const handleConfetti = () => {
    confetti({
      particleCount: 120,
      spread: 140,
      origin: { x: 0.5, y: 0.5 },
    });
  };

  const handleClaim = async () => {
    setButtonState("loading");
    try {
      await onClickClaim(
        proxyWallet,
        address,
        session?.cookie,
        currentConditionId,
        currentCardData.clob_token_ids,
        notificationParams,
        walletType,
      );
      handleConfetti();
    } catch (error: any) {
      console.error("Claim failed:", error);

      // 检查是否是token过期错误
      if (isTokenExpiredError(error)) {
        setError("Login session expired, please login again");
        setButtonState("none");
      } else {
        setButtonState("none");
      }
    }
  };

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="max-w-[400px] h-64 flex items-center justify-center">
        <Spinner size="lg" color="primary" />
      </div>
    );
  }

  // 显示错误状态
  if (error) {
    return (
      <div className="max-w-[400px] h-64 flex items-center justify-center">
        <div className="text-red-500 text-center px-4">
          <div className="font-semibold mb-2">Unable to load claim data</div>
          <div className="text-sm mb-4">{error}</div>
          <Button size="sm" color="primary" variant="bordered" onPress={handleRetry} isLoading={isLoading}>
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {currentCardData && (
        <Card className="max-w-[400px]">
          <CardHeader className="flex flex-col flex-center py-8 gap-4">
            <SquareCheck className="size-12" color="#2563eb" />
            <div className="font-semibold text-blue-600 text-lg">Outcome: {outcome}</div>
            <span className="max-w-[240px] text-md font-bold line-clamp-1 text-gray-600">{groupItemTitle}</span>
          </CardHeader>

          <Divider />

          <CardBody className="px-6 my-4 gap-1 flex flex-col flex-center">
            <div className="font-semibold text-lg">Your Earnings</div>

            <div className="w-full flex justify-between items-center">
              <div className="text-medium font-normal text-gray-600">Position</div>
              <div className="flex text-medium font-medium gap-1">
                <div>{size}</div>
                <div>{side}</div>
              </div>
            </div>

            <div className="w-full flex justify-between items-center">
              <div className="text-medium font-normal text-gray-600">Value per share</div>
              <div className="text-medium font-medium">${valuePerShare}</div>
            </div>

            <div className="w-full flex justify-between items-center">
              <div className="text-medium font-normal text-gray-600">Total</div>
              <div className="text-medium font-medium">${total}</div>
            </div>

            <Button
              size="lg"
              radius="sm"
              isDisabled={!isCanClaim || !address || !session}
              isLoading={buttonState === "loading"}
              className="w-full bg-green-600 font-semibold text-white mt-4"
              onPress={handleClaim}
            >
              {!address || !session ? "No Login" : isCanClaim ? "Claim winnings" : "No position"}
            </Button>
          </CardBody>
        </Card>
      )}

      {showNotification && (
        <NotificationCard title={notiTitle} setShowNotification={setShowNotification} notiStatus={notiStatus} />
      )}
    </div>
  );
};

export default ClaimCard;
