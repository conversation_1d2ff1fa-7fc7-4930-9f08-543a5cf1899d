import React, { useEffect, useState } from "react";
import { getHistoryData } from "@/api/markets/history";
import { base64DecodeByEn } from "@/utils";
import { useTranslation } from "react-i18next";
import LineChart from "~~/components/details/lineChart/LineChart";
import TimeRangeSelector from "~~/components/details/lineChart/TimeRangeSelector";

const EventChart = (props: any) => {
  const { chartDataList } = props;
  const { t } = useTranslation();

  const [historyData, setHistoryData] = useState<any>([]);
  const [timeRange, setTimeRange] = useState<string>("ALL");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const colorList = ["rgb(22, 82, 240)", "rgb(255, 89, 82)", "rgb(155, 81, 224)", "rgb(39, 174, 96)"];
  useEffect(() => {
    if (chartDataList?.length >= 1 && timeRange) {
      setIsLoading(true);
      const limitedChartDataList = chartDataList.slice(0, 4); // Only the first 4 data are retained
      Promise.all(
        limitedChartDataList.map((item: any, index: number) => {
          const { clob_token_ids } = item.question_market;
          const firstClobTokenId = clob_token_ids?.[0];
          return getHistoryData(firstClobTokenId, timeRange)
            .then(res => ({
              ...res.data,
              history: res.data.history || [],
              name: base64DecodeByEn(item.question_market.question),
              color: colorList[index],
            }))
            .catch(() => ({
              history: [],
              name: base64DecodeByEn(item.question_market.question),
              color: colorList[index],
            }));
        }),
      ).then(results => {
        setHistoryData(results);
        setIsLoading(false);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chartDataList, timeRange]);

  const LoadingState = () => (
    <div className="w-full h-[240px] bg-white rounded-xl flex flex-col items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
      <div className="text-gray-600 text-sm font-medium">{t("chart.loading")}</div>
    </div>
  );

  const NoDataState = () => (
    <div className="w-full h-[240px] bg-white rounded-xl flex flex-col items-center justify-center">
      <div className="mb-4">
        <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      </div>
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-700 mb-2">{t("chart.no_data_title")}</h3>
        <p className="text-sm text-gray-500 max-w-sm">{t("chart.no_data_desc")}</p>
      </div>
    </div>
  );

  return (
    <div className="w-full flex flex-col">
      {isLoading ? (
        <LoadingState />
      ) : historyData?.[0]?.history?.length > 0 ? (
        <LineChart data={historyData} />
      ) : (
        <NoDataState />
      )}
      <TimeRangeSelector timeRange={timeRange} setTimeRange={setTimeRange} />
    </div>
  );
};

export default EventChart;
