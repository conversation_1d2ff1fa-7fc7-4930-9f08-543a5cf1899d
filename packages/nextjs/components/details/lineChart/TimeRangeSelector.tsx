import React from "react";

interface TimeRangeFilterProps {
  timeRange: string;
  setTimeRange: (event: any) => void;
}

const TimeRangeFilter = (props: TimeRangeFilterProps) => {
  const { timeRange, setTimeRange } = props;

  return (
    <div className="w-full flex flex-col mt-2">
      <div className="flex gap-1 sm:gap-2 mb-3 sm:mb-4">
        {["1H", "6H", "1D", "1W", "1M", "ALL"].map(range => (
          <button
            key={range}
            className={`px-2 py-1 sm:px-3 sm:py-1.5 rounded-2xl sm:rounded-3xl border-none font-medium text-xs sm:text-sm transition-colors
              ${timeRange === range ? "bg-gray-500 text-white" : "bg-white text-gray-400 hover:bg-gray-50"}`}
            onClick={() => setTimeRange(range)}
          >
            {range}
          </button>
        ))}
      </div>
    </div>
  );
};

export default TimeRangeFilter;
