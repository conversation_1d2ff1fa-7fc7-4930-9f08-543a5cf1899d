import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import * as d3 from "d3";
import { useMediaQuery } from "react-responsive";

interface LineData {
  history: { t: number; p: number }[];
  color: string;
  name: string;
}

interface LineChartProps {
  data: LineData[];
}

const LineChart: React.FC<LineChartProps> = ({ data }) => {
  const svgRef = useRef<SVGSVGElement | null>(null);
  const legendRef = useRef<HTMLDivElement | null>(null);
  const isMobile = useMediaQuery({ maxWidth: 767 });

  const [hasValidData, setHasValidData] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    if (!svgRef.current || !legendRef.current) return;

    const svg = d3.select(svgRef.current);
    const legendDiv = d3.select(legendRef.current);

    const width = isMobile ? 400 : 800;
    const height = isMobile ? 280 : 300;
    const margin = isMobile ? { top: 20, right: 30, bottom: 40, left: 0 } : { top: 10, right: 50, bottom: 30, left: 0 };

    svg.selectAll("*").remove();
    legendDiv.selectAll("*").remove();

    // 设置图表的宽高
    svg.attr("viewBox", `0 0 ${width} ${height}`);

    // 过滤掉空数据的线条，只处理有效的线条数据
    const validLines = data.filter(line => line.history && Array.isArray(line.history) && line.history.length > 0);

    // 合并所有有效折线的数据以确定x轴和y轴的比例尺，过滤掉无效数据点
    const allData = validLines.flatMap(line => {
      return line.history.filter(d => {
        // 支持字符串和数字类型的价格数据
        const pValue = typeof d.p === "string" ? parseFloat(d.p) : d.p;
        const tValue = typeof d.t === "string" ? parseFloat(d.t) : d.t;

        const isValid =
          d &&
          typeof d.t !== "undefined" &&
          typeof d.p !== "undefined" &&
          !isNaN(tValue) &&
          !isNaN(pValue) &&
          isFinite(tValue) &&
          isFinite(pValue);

        return isValid;
      });
    });

    const processedData = allData.map(d => {
      const timestamp = d.t * 1000;
      const date = new Date(timestamp);
      const value = typeof d.p === "string" ? parseFloat(d.p) : d.p;

      return {
        date,
        value,
      };
    });

    // 如果没有有效数据，设置错误状态
    if (processedData.length === 0) {
      setHasValidData(false);
      setErrorMessage("No valid data");
      return;
    }

    // 计算数据的实际范围
    const minValue = d3.min(processedData, d => d.value) as number;
    const maxValue = d3.max(processedData, d => d.value) as number;

    // 确保 min 和 max 值有效
    if (!isFinite(minValue) || !isFinite(maxValue)) {
      setHasValidData(false);
      setErrorMessage("Invalid data range");
      return;
    }
    const dataRange = maxValue - minValue;

    // 动态调整Y轴范围的逻辑
    let yMin: number, yMax: number;

    if (dataRange === 0) {
      yMin = Math.max(0, minValue - 0.1);
      yMax = Math.min(1, maxValue + 0.1);
    } else {
      const expandedMax = Math.min(1, maxValue * 2);
      yMin = 0;
      yMax = expandedMax;
    }

    // 设置x轴和y轴的比例尺
    const timeExtent = d3.extent(processedData, d => d.date) as [Date, Date];

    // 确保时间范围有效
    if (!timeExtent[0] || !timeExtent[1] || isNaN(timeExtent[0].getTime()) || isNaN(timeExtent[1].getTime())) {
      setHasValidData(false);
      setErrorMessage("Invalid time range");
      return;
    }

    // 如果到这里，说明数据有效
    setHasValidData(true);
    setErrorMessage("");

    const x = d3
      .scaleTime()
      .domain(timeExtent)
      .range([margin.left, width - margin.right]);

    const y = d3
      .scaleLinear()
      .domain([yMin, yMax])
      .nice()
      .range([height - margin.bottom, margin.top]);

    // 只保留横向点状虚线网格
    const yGrid = svg
      .append("g")
      .attr("class", "grid")
      .attr("transform", `translate(${margin.left},0)`)
      .call(
        d3
          .axisLeft(y)
          .ticks(5)
          .tickSize(-width + margin.left + margin.right)
          .tickFormat(() => ""),
      );

    // 设置为点状虚线样式
    yGrid
      .selectAll(".tick line")
      .attr("stroke", "#d1d5db")
      .attr("stroke-width", 1)
      .attr("stroke-dasharray", "2,4") // 点状虚线：2px实线，4px间隔
      .attr("opacity", 0.6);

    yGrid.selectAll(".domain").remove();

    // 创建美观的x轴和y轴
    const xAxis = svg
      .append("g")
      .attr("transform", `translate(0,${height - margin.bottom})`)
      .call(
        d3
          .axisBottom(x)
          .ticks(isMobile ? 3 : 6)
          .tickFormat(d3.timeFormat(isMobile ? "%m/%d" : "%m/%d") as any),
      );

    xAxis.selectAll(".domain").attr("stroke", "#d1d5db").attr("stroke-width", 1);
    xAxis.selectAll(".tick line").attr("stroke", "#d1d5db").attr("stroke-width", 1);
    xAxis
      .selectAll(".tick text")
      .attr("fill", "#6b7280")
      .style("font-size", isMobile ? "10px" : "12px")
      .style("font-weight", "500");

    const yAxis = svg
      .append("g")
      .attr("transform", `translate(${width - margin.right},0)`)
      .call(
        d3
          .axisRight(y)
          .ticks(isMobile ? 3 : 5)
          .tickFormat((d: any) => `${(d * 100).toFixed(0)}%`),
      );

    yAxis.selectAll(".domain").remove();
    yAxis.selectAll(".tick line").remove();
    yAxis
      .selectAll(".tick text")
      .attr("fill", "#6b7280")
      .style("font-size", isMobile ? "10px" : "12px")
      .style("font-weight", "600")
      .attr("dx", "0.5em");

    // 绘制折线图 - 改为直线连接，只绘制有效的线条
    validLines.forEach(lineData => {
      // 对历史数据按时间排序，严格过滤无效数据（支持字符串和数字类型）
      const sortedHistory = (lineData.history || [])
        .filter(d => {
          if (!d) return false;

          // 支持字符串和数字类型的数据
          const pValue = typeof d.p === "string" ? parseFloat(d.p) : d.p;
          const tValue = typeof d.t === "string" ? parseFloat(d.t) : d.t;

          return (
            typeof d.t !== "undefined" &&
            typeof d.p !== "undefined" &&
            !isNaN(tValue) &&
            !isNaN(pValue) &&
            isFinite(tValue) &&
            isFinite(pValue)
          );
        })
        .sort((a, b) => {
          const aTime = typeof a.t === "string" ? parseFloat(a.t) : a.t;
          const bTime = typeof b.t === "string" ? parseFloat(b.t) : b.t;
          return aTime - bTime;
        });

      // 如果没有有效数据，跳过这条线
      if (sortedHistory.length === 0) {
        return;
      }

      const line = d3
        .line<{ t: number | string; p: number | string }>()
        .defined(d => {
          const pValue = typeof d.p === "string" ? parseFloat(d.p) : d.p;
          const tValue = typeof d.t === "string" ? parseFloat(d.t) : d.t;
          return !isNaN(pValue) && !isNaN(tValue) && isFinite(pValue) && isFinite(tValue);
        })
        .x(d => {
          const tValue = typeof d.t === "string" ? parseFloat(d.t) : d.t;
          return x(new Date(tValue * 1000));
        })
        .y(d => {
          const pValue = typeof d.p === "string" ? parseFloat(d.p) : d.p;
          return y(pValue);
        })
        .curve(d3.curveMonotoneX);

      svg
        .append("path")
        .datum(sortedHistory)
        .attr("fill", "none")
        .attr("stroke", lineData.color)
        .attr("stroke-width", 2)
        .attr("stroke-linecap", "round")
        .attr("stroke-linejoin", "round")
        .attr("d", line);
    });

    // 只在非移动端添加交互效果
    if (!isMobile) {
      const tooltip = d3
        .select("body")
        .append("div")
        .attr("class", "tooltip")
        .style("position", "absolute")
        .style("background", "rgba(0, 0, 0, 0.9)")
        .style("color", "white")
        .style("padding", "12px")
        .style("border-radius", "6px")
        .style("font-size", "12px")
        .style("pointer-events", "none")
        .style("opacity", 0)
        .style("box-shadow", "0 4px 12px rgba(0, 0, 0, 0.15)")
        .style("backdrop-filter", "blur(10px)")
        .style("z-index", "9999");

      const verticalLine = svg
        .append("line")
        .attr("stroke", "#6366f1")
        .attr("stroke-width", 2)
        .attr("stroke-dasharray", "3,3")
        .attr("y1", margin.top)
        .attr("y2", height - margin.bottom)
        .style("opacity", 0);

      const focusElements = validLines.map(lineData => {
        const circle = svg
          .append("circle")
          .attr("r", 6)
          .attr("fill", lineData.color)
          .attr("stroke", "#ffffff")
          .attr("stroke-width", 2)
          .style("opacity", 0);

        return { circle };
      });

      svg
        .append("rect")
        .attr("width", width)
        .attr("height", height)
        .style("fill", "none")
        .style("pointer-events", "all")
        .on("mousemove", function (event) {
          const [mouseX] = d3.pointer(event);
          const xDate = x.invert(mouseX);

          let tooltipContent = `<div style="font-weight: 600; margin-bottom: 6px; font-size: 11px;">${d3.timeFormat(
            "%B %d, %Y at %-I:%M %p",
          )(xDate)}</div>`;

          validLines.forEach((lineData, index) => {
            if (!lineData || !lineData.history || lineData.history.length === 0) {
              return;
            }

            const closestData = lineData.history.reduce((a, b) => {
              const aTime = typeof a.t === "string" ? parseFloat(a.t) : a.t;
              const bTime = typeof b.t === "string" ? parseFloat(b.t) : b.t;
              return Math.abs(bTime * 1000 - xDate.getTime()) < Math.abs(aTime * 1000 - xDate.getTime()) ? b : a;
            });

            if (!closestData) return;

            const closestTime = typeof closestData.t === "string" ? parseFloat(closestData.t) : closestData.t;
            const closestPrice = typeof closestData.p === "string" ? parseFloat(closestData.p) : closestData.p;

            verticalLine
              .attr("x1", x(new Date(closestTime * 1000)))
              .attr("x2", x(new Date(closestTime * 1000)))
              .style("opacity", 0.8);

            const focusElement = focusElements[index];
            if (focusElement) {
              focusElement.circle
                .attr("cx", x(new Date(closestTime * 1000)))
                .attr("cy", y(closestPrice))
                .style("opacity", 1);
            }

            tooltipContent += `
              <div style="display: flex; align-items: center; margin-bottom: 3px;">
                <div style="width: 8px; height: 8px; background-color: ${
                  lineData.color
                }; border-radius: 50%; margin-right: 6px;"></div>
                <span style="font-weight: 500; font-size: 11px;">${lineData.name}:</span>
                <span style="margin-left: 4px; font-weight: 600; font-size: 11px;">${(closestPrice * 100).toFixed(
                  1,
                )}%</span>
              </div>
            `;
          });

          tooltip
            .style("opacity", 1)
            .html(tooltipContent)
            .style("left", event.pageX + 15 + "px")
            .style("top", event.pageY - 15 + "px");
        })
        .on("mouseout", () => {
          verticalLine.style("opacity", 0);
          focusElements.forEach(focusElement => {
            focusElement.circle.style("opacity", 0);
          });
          tooltip.style("opacity", 0);
        });
    }

    // 生成图例 - 只为有效的线条生成图例
    validLines.forEach(lineData => {
      const legendItem = legendDiv
        .append("div")
        .style("display", "flex")
        .style("align-items", "center")
        .style("margin", isMobile ? "2px" : "4px")
        .style("transition", "all 0.2s ease");

      const lastDataPoint = lineData.history[lineData.history.length - 1];
      const lastValue = lastDataPoint
        ? typeof lastDataPoint.p === "string"
          ? parseFloat(lastDataPoint.p)
          : lastDataPoint.p
        : 0;

      legendItem
        .append("div")
        .style("width", isMobile ? "6px" : "8px")
        .style("height", isMobile ? "6px" : "8px")
        .style("background-color", lineData.color)
        .style("border-radius", "50%")
        .style("margin-right", isMobile ? "4px" : "6px");

      legendItem
        .append("span")
        .style("font-size", isMobile ? "11px" : "13px")
        .style("font-weight", "500")
        .style("color", "#374151")
        .style("margin-right", isMobile ? "2px" : "4px")
        .text(lineData.name);

      legendItem
        .append("span")
        .style("font-size", isMobile ? "11px" : "13px")
        .style("font-weight", "500")
        .style("color", lineData.color)
        .text(`${(lastValue * 100).toFixed(1)}%`);
    });

    return () => {
      // 只在非移动端清理tooltip
      if (!isMobile) {
        d3.selectAll(".tooltip").remove();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  // 如果数据无效，显示错误信息
  if (!hasValidData) {
    return (
      <div className="bg-white rounded-xl">
        <div className="w-full h-64 flex items-center justify-center text-gray-500">{errorMessage}</div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl">
      <div className="w-full flex flex-col md:flex-row justify-between items-start md:items-center">
        <div ref={legendRef} className="flex flex-wrap items-center gap-1 mb-2 md:mb-0"></div>
        {!isMobile && (
          <div className="flex items-center gap-2">
            <Image className="h-6 w-6 md:h-8 md:w-8 rounded-full" src="/logo.png" alt="" width={32} height={32} />
            <span className="text-gray-500 text-sm md:text-lg font-semibold">PredictOne</span>
          </div>
        )}
      </div>
      <div className="bg-white rounded-lg p-1">
        <svg ref={svgRef} className="w-full"></svg>
      </div>
    </div>
  );
};

export default LineChart;
