import React, { useEffect, useRef, useState } from "react";
import { Link } from "@heroui/link";
import { Tooltip } from "@heroui/react";

export const Footer = () => {
  const [isVisible, setIsVisible] = useState(false);
  const lastScrollYRef = useRef(0);
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const lastScrollY = lastScrollYRef.current;

      if (currentScrollY > lastScrollY && currentScrollY > 50) {
        setIsVisible(true);
      }
      // 向上滚动时隐藏Footer
      else if (currentScrollY < lastScrollY) {
        setIsVisible(false);
      }

      lastScrollYRef.current = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div
      className={`fixed w-full bottom-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-t border-gray-200/50 shadow-2xl transition-transform duration-300 ease-in-out ${
        isVisible ? "translate-y-0" : "translate-y-full"
      }`}
    >
      <div className="flex items-center justify-between py-4 px-8 w-full mx-auto shadow-md">
        <div className="flex flex-wrap items-center gap-1 text-sm">
          <span className="text-gray-600">PredictOne © 2025</span>
          <span className="text-gray-400 mx-1">•</span>
          <Link href="/toe" className="text-gray-600 hover:text-blue-600 transition-colors" size="sm">
            Terms of Use
          </Link>
          <span className="text-gray-400 mx-1">•</span>
          <Tooltip content="<EMAIL>">
            <span className="text-gray-600 cursor-pointer">Contact Us</span>
          </Tooltip>
        </div>

        <div className="flex items-center gap-4">
          <Link
            href="mailto:<EMAIL>"
            className="text-gray-600 hover:text-blue-600 transition-colors"
            isExternal
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 20 20"
              fill="none"
              className="w-4 h-4"
            >
              <g clipPath="url(#clip0_3518_21)">
                <path
                  d="M0.76001 5.85791L9.38444 10.6156C9.76806 10.8271 10.232 10.8271 10.6156 10.6156L19.24 5.85791"
                  stroke="currentColor"
                  strokeWidth="1.8"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M3.30897 17.3284L16.6911 17.3284C18.0988 17.3284 19.24 16.1872 19.24 14.7794V5.22077C19.24 3.81302 18.0988 2.67181 16.6911 2.67181L3.30897 2.67181C1.90122 2.67181 0.760006 3.81302 0.760006 5.22077V14.7794C0.760006 16.1872 1.90122 17.3284 3.30897 17.3284Z"
                  stroke="currentColor"
                  strokeWidth="1.8"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </g>
              <defs>
                <clipPath id="clip0_3518_21">
                  <rect width="20" height="20" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </Link>

          <Link
            href="https://discord.com/invite/jPdfwmaC9n"
            className="text-gray-600 hover:text-blue-600 transition-colors"
            isExternal
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 20 20"
              fill="none"
              className="w-4 h-4"
            >
              <path
                d="M16.9308 3.70833C15.6561 3.14167 14.2844 2.73333 12.8436 2.5C12.6564 2.83333 12.4688 3.2 12.3542 3.475C10.7813 3.25833 9.225 3.25833 7.68725 3.475C7.57267 3.2 7.37558 2.83333 7.18725 2.5C5.74517 2.73333 4.3735 3.145 3.09883 3.71667C0.444833 7.58333 -0.277 11.35 0.0852167 15.0667C1.943 16.4583 3.74267 17.3583 5.501 17.9333C5.94392 17.3417 6.34308 16.7167 6.69458 16.0583C6.05975 15.8167 5.45692 15.5167 4.88983 15.1667C5.02567 15.0667 5.159 14.9583 5.28858 14.8417C8.38892 16.2583 11.7627 16.2583 14.8203 14.8417C14.9498 14.9583 15.0831 15.0667 15.219 15.1667C14.6519 15.5208 14.0479 15.8208 13.4131 16.0625C13.7646 16.7167 14.1621 17.3458 14.6065 17.9375C16.3673 17.3625 18.1669 16.4625 20.0252 15.0667C20.4448 10.7417 19.1769 7.00833 16.9308 3.70833ZM6.6975 12.6083C5.69892 12.6083 4.88608 11.675 4.88608 10.5333C4.88608 9.39167 5.68142 8.45833 6.6975 8.45833C7.71358 8.45833 8.52642 9.39167 8.50892 10.5333C8.51267 11.675 7.71358 12.6083 6.6975 12.6083ZM13.411 12.6083C12.4127 12.6083 11.5994 11.675 11.5994 10.5333C11.5994 9.39167 12.3948 8.45833 13.411 8.45833C14.4273 8.45833 15.2398 9.39167 15.2223 10.5333C15.2223 11.675 14.4273 12.6083 13.411 12.6083Z"
                fill="currentColor"
              />
            </svg>
          </Link>

          <Link
            href="https://x.com/Predict_one"
            className="text-gray-600 hover:text-blue-600 transition-colors"
            isExternal
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 20 20"
              fill="none"
              className="w-4 h-4"
            >
              <path
                d="M15.2719 1.58655H18.0831L11.9414 8.60612L19.1667 18.1582H13.5094L9.07837 12.3649L4.0083 18.1582H1.19537L7.76454 10.65L0.833344 1.58655H6.63427L10.6395 6.88182L15.2719 1.58655ZM14.2853 16.4755H15.843L5.78784 3.18082H4.11623L14.2853 16.4755Z"
                fill="currentColor"
              />
            </svg>
          </Link>
        </div>
      </div>
    </div>
  );
};
