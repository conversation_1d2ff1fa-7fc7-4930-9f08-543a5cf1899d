import { useEffect, useState } from "react";
import { signInWithSiwe } from "@/utils/signature/loginSignature";
import { Image, Modal, ModalBody, ModalContent, ModalHeader } from "@heroui/react";
import { RotateCw } from "lucide-react";
import { useAccount, useSignMessage } from "wagmi";

export default function CustomSignInModal({
  visible,
  onClose,
  setIsShowSignatureModal,
}: {
  visible: boolean;
  onClose: () => void;
  setIsShowSignatureModal: (value: boolean) => void;
}) {
  const [loadStatus, setLoadStatus] = useState("none"); // none || failed || loading
  const { address } = useAccount();
  const { signMessageAsync } = useSignMessage();

  useEffect(() => {
    if (visible && address && signMessageAsync) {
      signInWithSiwe(
        address, // address: string
        "metamask", // walletType: WalletType
        signMessageAsync, // signMessageAsync?: (params: { message: string }) => Promise<string>
        undefined, // didToken?: string (MetaMask 不需要)
        setIsShowSignatureModal, // setIsShowSignatureModal?: (value: boolean) => void
        onClose, // onClose?: () => void
        setLoadStatus, // setLoadStatus?: (value: string) => void
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  return (
    <Modal isOpen={visible} onClose={onClose}>
      <ModalContent>
        <ModalHeader className="w-full flex-center">MetaMask</ModalHeader>
        <ModalBody className="p-6">
          <div className="flex-center flex-col gap-2">
            <div className="flex-center relative">
              {loadStatus === "loading" && (
                <div className="absolute left-[-3px] top-[-3px] z-100">
                  <div className="spinner-square border-t-1 bg-blue-500 rounded-full w-2 h-2"></div>
                </div>
              )}
              <Image
                src="/metaMask_icon.svg"
                alt="metamask"
                width={90}
                height={90}
                className="relative rounded-xl mb-4"
              />
            </div>

            {loadStatus === "loading" && (
              <>
                <div className="text-lg font-medium">Requesting Signature</div>
                <div className="text-sm text-gray-500">Please sign to connect.</div>
              </>
            )}

            {loadStatus === "failed" && (
              <div
                className="size-8 z-100 ml-20 mb-4 absolute rounded-full bg-gray-300 cursor-pointer p-1"
                // onClick={onClickHandler}
              >
                <RotateCw className="size-6" />
              </div>
            )}
            {loadStatus === "failed" && (
              <>
                <div className="text-lg font-medium text-red-500">Request Cancelled</div>
                <div className="text-sm text-gray-500">You cancelled the request. Please try again.</div>
              </>
            )}
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
