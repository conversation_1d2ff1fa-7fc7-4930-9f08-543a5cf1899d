"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import VideoModal from "./learning-center/VideoModal";
import MysteryMenuItem from "./navbar/MysteryMenuItem";
// import { HeaderMenuLinks } from "./navbar/MenuLinks";
import { SearchBar } from "./navbar/SearchBar";
import { SubHeaderNav } from "./navbar/SubHeaderNav";
import { siteConfig } from "@/configs/site";
import { RainbowKitCustomConnectButton } from "~~/components/scaffold-eth";

export const Header = () => {
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  // How it works 视频数据
  const howItWorksVideo = {
    id: "UmVHNQ7QDcU",
    title: "How PredictOne Works",
    description: "Learn how to use PredictOne prediction market platform in this quick tutorial.",
    embedUrl: "https://www.youtube.com/embed/UmVHNQ7QDcU",
  };

  const handleHowItWorksClick = () => {
    setIsVideoModalOpen(true);
  };

  const handleCloseVideoModal = () => {
    setIsVideoModalOpen(false);
  };

  return (
    <div className="sticky navbar flex-col top-0 bg-base-100 z-20 px-2 py-0 sm:px-6 border-b border-gray-300">
      <div className="flex w-full min-h-20 items-center md:justify-normal justify-between flex-nowrap">
        <div className="flex-shrink-0">
          <Link href="/" className="flex items-center">
            <Image className="size-12 rounded-full" src="/logo.png" alt="" width={50} height={50} />
            <span className="flex flex-col ml-2 items-start bg-gradient-to-br from-blue-500 to-purple-700 bg-clip-text">
              <div className="text-2xl font-semibold text-transparent">{siteConfig.name}</div>
            </span>
          </Link>
        </div>

        <div className="flex-grow px-2 md:flex hidden items-center space-x-2">
          <SearchBar />
          <button
            onClick={handleHowItWorksClick}
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium text-sm whitespace-nowrap"
          >
            <span>How it works</span>
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 5v10l8-5-8-5z" />
            </svg>
          </button>
        </div>

        {/* PC */}
        <div className="flex-shrink-0 items-center justify-end md:flex hidden font-medium gap-2">
          {/* Mystery 入口 */}
          <MysteryMenuItem size="lg" />
          {/* <HeaderMenuLinks /> */}
          <RainbowKitCustomConnectButton />
        </div>

        {/* Mobile */}
        <div className="flex-shrink-0 items-center justify-end md:hidden flex font-medium gap-2">
          {/* Mystery 入口 */}
          {/* <HeaderMenuLinks /> */}
          <RainbowKitCustomConnectButton />
        </div>
      </div>

      <div className="w-full">
        <SubHeaderNav />
      </div>

      {/* How it works 视频 */}
      <VideoModal isOpen={isVideoModalOpen} onClose={handleCloseVideoModal} video={howItWorksVideo} />
    </div>
  );
};
