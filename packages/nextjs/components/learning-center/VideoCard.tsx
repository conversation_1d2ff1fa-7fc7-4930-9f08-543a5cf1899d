"use client";

import React from "react";

interface VideoCardProps {
  video: {
    id: string;
    title: string;
    description: string;
    embedUrl: string;
    thumbnailUrl?: string;
  };
  onClick: () => void;
}

const VideoCard: React.FC<VideoCardProps> = ({ video, onClick }) => {
  return (
    <div className="video-item relative bg-white rounded-2xl shadow-xl overflow-hidden cursor-pointer border-0 group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
      {/* 主要内容容器 */}
      <div className="relative bg-white rounded-2xl overflow-hidden">
        {/* 视频海报区域 */}
        <div
          className="video-poster relative aspect-video bg-gradient-to-br from-slate-100 via-blue-50 to-purple-50 overflow-hidden"
          onClick={onClick}
        >
          <div className="youtube-video relative w-full h-full rounded-t-2xl overflow-hidden transform group-hover:scale-105 transition-transform duration-700">
            <iframe
              className="iframe w-full h-full"
              src={`${video.embedUrl}?controls=0&showinfo=0&rel=0&modestbranding=1&mute=1`}
              title={video.title}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              style={{ pointerEvents: "none", border: "none" }}
            />
          </div>

          {/* 渐变覆盖层 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        </div>

        {/* 视频信息区域 */}
        <div className="video-info p-6 bg-gradient-to-b from-white to-gray-50/50 group-hover:from-blue-50/50 group-hover:to-purple-50/50 transition-all duration-500">
          {/* 标题 */}
          <div className="video-title text-lg font-bold text-gray-900 mb-3 line-clamp-2 leading-tight group-hover:text-blue-600 transition-colors duration-500">
            {video.title}
          </div>

          {/* 描述 */}
          <div className="video-desc text-sm text-gray-600 line-clamp-3 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
            {video.description}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoCard;
