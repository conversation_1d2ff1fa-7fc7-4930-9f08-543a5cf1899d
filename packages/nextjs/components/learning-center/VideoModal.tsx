"use client";

import React, { useEffect, useRef, useState } from "react";
import { Modal, ModalBody, ModalContent } from "@heroui/react";

interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  video: {
    id: string;
    title: string;
    description: string;
    embedUrl: string;
    thumbnailUrl?: string;
  } | null;
}

const VideoModal: React.FC<VideoModalProps> = ({ isOpen, onClose, video }) => {
  const [videoKey, setVideoKey] = useState(0);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 处理ESC键关闭模态框
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEsc);
      document.body.style.overflow = "hidden";
      // 重新生成视频key以强制重新加载iframe
      setVideoKey(prev => prev + 1);
    }

    return () => {
      document.removeEventListener("keydown", handleEsc);
      document.body.style.overflow = "";
    };
  }, [isOpen, onClose]);

  if (!video) return null;

  return (
    <Modal
      size="5xl"
      isOpen={isOpen}
      onClose={onClose}
      classNames={{
        base: "max-w-6xl mx-4",
        body: "p-0",
        header: "border-b border-gray-200 px-6 py-4",
        backdrop: "bg-black/50 backdrop-blur-sm",
      }}
      backdrop="blur"
      placement="center"
    >
      <ModalContent>
        <ModalBody>
          {/* 视频播放器 */}
          <div className="relative w-full bg-black" style={{ paddingBottom: "56.25%" }}>
            <iframe
              key={videoKey}
              ref={iframeRef}
              className="absolute top-0 left-0 w-full h-full"
              src={`${video.embedUrl}?autoplay=1&mute=0&rel=0&modestbranding=1&fs=1&cc_load_policy=0&iv_load_policy=3&enablejsapi=1&playsinline=1&controls=1&start=0`}
              title={video.title}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen"
              allowFullScreen
              style={{ border: "none" }}
            />
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default VideoModal;
