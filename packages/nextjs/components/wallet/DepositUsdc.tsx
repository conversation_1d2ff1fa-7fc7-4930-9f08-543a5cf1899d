import React from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@heroui/react";
import { TokenUSDC } from "@web3icons/react";
import { t } from "i18next";
import { useTranslation } from "react-i18next";

const ExchangePop: React.FC = () => {
  return (
    <Popover placement="top">
      <PopoverTrigger>
        <span className="underline cursor-pointer">{t("Wallet_Exchange")}</span>
      </PopoverTrigger>
      <PopoverContent className="px-4 py-2 bg-gray-800">
        <ul className="list-disc list-inside text-sm text-white">
          <li>Coinbase</li>
          <li>Robinhood</li>
          <li>Binance</li>
          <li>Crypto.com</li>
          <li>Gate.io</li>
          <li>Kucoin</li>
        </ul>
      </PopoverContent>
    </Popover>
  );
};

const DepositUsdc = () => {
  const { t } = useTranslation();

  return (
    <div className="w-full flex flex-col p-6 gap-5 border rounded-lg">
      <div className="flex items-center text-medium">
        <span className="flex-center h-4 text-xxs px-1 bg-blue-600 text-white rounded-md font-bold tracking-wider">
          {t("Wallet_EASIEST METHOD")}
        </span>
        <span className="text-xs px-1 text-gray-500 font-medium ml-2 tracking-widest">{t("Wallet_1_MINUTE_FREE")}</span>
      </div>

      <div className="flex items-center text-xl md:text-2xl font-bold">
        {t("Wallet_Deposit_USDC")}
        <TokenUSDC className="size-10 ml-2" variant="branded" />
      </div>

      {/* PC */}
      <div className="hidden sm:flex flex-col gap-4 text-sm font-normal">
        <div className="flex flex-col sm:flex-row gap-2 items-center">
          <div className="flex-center size-8 bg-blue-100 rounded-full font-semibold">1</div>
          <div className="flex flex-col sm:flex-row items-center sm:items-start">
            <span className="font-bold mr-2">{t("Wallet_Buy_USDC")}</span>
            <span className="mr-2">{t("Wallet_Buy_Path")}</span>
            <ExchangePop />
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 items-center">
          <div className="flex-center size-8 bg-blue-100 rounded-full font-semibold">2</div>
          <div className="flex flex-col sm:flex-row items-center">
            <span className="font-bold mr-2">{t("Wallet_Send_Withdraw")}</span>
            <span>{t("Wallet_Send_Text")}</span>
          </div>
        </div>
      </div>

      {/* mobile */}
      <div className="block sm:hidden flex-col text-sm font-normal">
        <div className="flex flex-wrap items-center mb-4">
          <span>1、</span>
          <span className="font-bold mr-2">{t("Wallet_Buy_USDC")}</span>
          <span className="">{t("Wallet_Buy_Path")}</span>
          <ExchangePop />
        </div>
        <div className="flex flex-row gap-2 items-center">
          <div className="flex flex-wrap items-center">
            <span>2、</span>
            <span className="font-bold mr-2">{t("Wallet_Send_Withdraw")}</span>
            <span>{t("Wallet_Send_Text")}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DepositUsdc;
