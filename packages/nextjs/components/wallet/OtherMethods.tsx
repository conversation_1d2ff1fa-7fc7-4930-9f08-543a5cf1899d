import React from "react";
import { NetworkPolygonPos, TokenUSDC, WalletCoinbase, WalletMetamask, WalletWalletConnect } from "@web3icons/react";
import { useTranslation } from "react-i18next";

const OtherMethods: React.FC = () => {
  const { t } = useTranslation();
  return (
    <div className="w-full flex flex-col gap-4">
      {/* <div className="w-full flex items-center justify-between">
        <div className="w-full flex items-center py-4 px-5 rounded-lg bg-gray-100">
          <div className="mr-2">No crypto?</div>
          <div className="flex-center h-8 px-4 py-2 bg-blue-600 text-white font-medium rounded-lg">Buy USDC</div>
        </div>
      </div> */}
      <div className="flex gap-4">
        <div className="flex-1 flex-center flex-col border rounded-lg py-6 px-4 gap-1 opacity-50">
          <div className="flex items-center gap-1">
            <WalletMetamask className="size-6" variant="branded" />
            <WalletCoinbase className="size-6" variant="branded" />
            <WalletWalletConnect className="size-6" variant="branded" />
          </div>
          <div className="font-semibold">{t("Normal_Wallets")}</div>
        </div>

        <div className="flex-1 flex-center flex-col border rounded-lg py-6 px-4 gap-1 opacity-50">
          <TokenUSDC className="size-6" variant="branded" />
          <div className="font-semibold">USDC</div>
        </div>

        <div className="flex-1 flex-center flex-col border rounded-lg py-6 px-4 gap-1 opacity-50">
          <NetworkPolygonPos variant="branded" />
          <div className="font-semibold">{t("Normal_Other")}</div>
        </div>
      </div>
    </div>
  );
};

export default OtherMethods;
