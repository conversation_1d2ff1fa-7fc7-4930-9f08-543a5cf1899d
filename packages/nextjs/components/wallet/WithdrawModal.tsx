import { useEffect, useState } from "react";
import { getOpenOrderData } from "@/api/order";
import { onClickWidthdraw } from "@/contracts/withdrawContract";
import { useGlobalState } from "@/services/store/store";
import { calculateOrderTotalWithFee, getItem, truncateToTwoDecimals } from "@/utils";
import { Button, Input, Modal, ModalBody, ModalContent, ModalHeader } from "@heroui/react";
// import { OAuthExtension } from "@magic-ext/oauth";
import { CircleCheck, ClockAlert, OctagonAlert } from "lucide-react";
// import { Magic } from "magic-sdk";
import { useSession } from "next-auth/react";
import { useTranslation } from "react-i18next";
import { NumericFormat as NumberFormat } from "react-number-format";

const AddressLabel = ({ onUseConnected }: { onUseConnected: () => void }) => {
  const { t } = useTranslation();
  return (
    <div className="w-full flex items-center justify-between font-medium">
      <div className="flex items-center">{t("Normal_Address")}</div>
      <div
        className="text-sm text-gray-500 hover:text-gray-800 font-medium underline cursor-pointer"
        onClick={onUseConnected}
      >
        {t("Withdraw_Modal_Use_Connected")}
      </div>
    </div>
  );
};

const AmountLabel = ({ balance, onClickMax }: { balance: string | null; onClickMax: () => void }) => {
  const { t } = useTranslation();
  return (
    <div className="w-full flex items-center justify-between font-medium">
      <div className="flex items-center">
        <div className="mr-1">{t("Normal_Amount")}</div>
        <div className="text-sm text-gray-500">{"($1 min)"}</div>
      </div>
      <div className="flex items-center text-sm text-gray-500 font-medium">
        <div className="mr-2">
          {balance} {t("Normal_Available")}
        </div>
        <div className="underline cursor-pointer hover:text-gray-800" onClick={onClickMax}>
          Max
        </div>
      </div>
    </div>
  );
};
const AmountInput = ({ value, onChange }: { value: string; onChange: (value: string) => void }) => {
  return (
    <NumberFormat
      value={value}
      thousandSeparator={true}
      decimalScale={4}
      fixedDecimalScale={false}
      allowNegative={false}
      prefix={"$"}
      onValueChange={values => {
        onChange(values.value);
      }}
      className="border rounded-medium w-full p-2 h-12"
    />
  );
};

const FailedWithdraw = ({ handleRetry, specialErrorText }: { handleRetry: any; specialErrorText: string }) => {
  const { t } = useTranslation();

  // 解析余额不足错误（现有逻辑）
  const extractBalanceNumbers = (text: string | null | undefined) => {
    if (!text || typeof text !== "string") {
      return null;
    }
    const matches = text.match(/-?\d+/g);
    if (matches && matches.length === 2) {
      const [leftOver, minBalance] = matches.map(num => (parseInt(num) / 1_000_000).toFixed(2));
      return { leftOver, minBalance };
    }
    return null;
  };

  // 解析促销活动要求错误
  const extractPromotionRequirements = (text: string | null | undefined) => {
    if (!text || typeof text !== "string") {
      return null;
    }

    const promotionMatch = text.match(
      /balance:\s*([0-9.]+),\s*volume:\s*([0-9.]+),\s*required balance:\s*([0-9.]+),\s*required volume:\s*([0-9.]+)/,
    );

    if (promotionMatch) {
      const [, currentBalance, currentVolume, requiredBalance, requiredVolume] = promotionMatch;
      return {
        currentBalance: parseFloat(currentBalance),
        currentVolume: parseFloat(currentVolume),
        requiredBalance: parseFloat(requiredBalance),
        requiredVolume: parseFloat(requiredVolume),
      };
    }
    return null;
  };

  // 错误类型判断和内容生成
  const getErrorContent = () => {
    if (!specialErrorText) {
      return t("Withdraw_Modal_Withdraw_Error");
    }

    // 账户被阻止或未启用
    if (specialErrorText === "not qualified for withdrawal, account is blocked or not enabled") {
      return t("Withdraw_Modal_Account_Blocked_Error");
    }

    // 促销活动要求未满足
    if (specialErrorText.includes("has not met the requirement for the promotion event")) {
      const promotionData = extractPromotionRequirements(specialErrorText);
      if (promotionData) {
        return t("Withdraw_Modal_Promotion_Requirement_Error", {
          currentBalance: promotionData.currentBalance,
          currentVolume: promotionData.currentVolume,
          requiredBalance: promotionData.requiredBalance,
          requiredVolume: promotionData.requiredVolume,
        });
      }
      return t("Withdraw_Modal_Promotion_General_Error");
    }

    // 余额不足错误（现有逻辑）
    const balanceNumbers = extractBalanceNumbers(specialErrorText);
    if (balanceNumbers) {
      return t("Withdraw_Modal_Balance_Insufficient_Error", {
        leftOver: balanceNumbers.leftOver,
        minBalance: balanceNumbers.minBalance,
      });
    }

    // 其他错误情况 - 显示原始错误信息或通用错误
    return specialErrorText.length > 100 ? t("Withdraw_Modal_Withdraw_Error") : specialErrorText;
  };

  return (
    <ModalContent>
      <ModalHeader className="flex-center gap-4">
        <div className="py-2">{t("Withdraw_Modal_Withdraw_Failed")}</div>
      </ModalHeader>
      <ModalBody className="px-8 pb-8">
        <div className="w-full flex-center flex-col gap-4">
          <ClockAlert className="size-12" />
          <div className="text-medium text-gray-500 text-center">{getErrorContent()}</div>

          <Button className="w-full" size="lg" color="primary" radius="sm" onPress={handleRetry}>
            {t("Normal_Back")}
          </Button>
        </div>
      </ModalBody>
    </ModalContent>
  );
};
const CompletedWithdraw = ({
  inputValue,
  onClose,
  handleRetry,
  transactionHash,
}: {
  inputValue: string;
  onClose: any;
  handleRetry: any;
  transactionHash: string;
}) => {
  const { t } = useTranslation();
  const targetUrl = `${process.env.NEXT_PUBLIC_BASESCAN}/${transactionHash}`;

  return (
    <ModalContent>
      <ModalHeader className="flex-center gap-4">
        <div className="py-2">{t("Withdraw_Modal_Withdraw_Completed")}</div>
      </ModalHeader>
      <ModalBody className="px-8 pb-8">
        <div className="w-full flex-center flex-col gap-4 text-medium">
          <CircleCheck className="size-16" color="#27AE60" />
          <div className="text-gray-500">{`${t("Withdraw_Modal_Your_Funds")} ($${inputValue}) ${t(
            "Withdraw_Modal_Were_Success",
          )}`}</div>
          <a href={targetUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500">
            View on Basescan
          </a>
          <Button
            className="w-full"
            size="lg"
            color="primary"
            radius="sm"
            onPress={() => {
              onClose();
              handleRetry();
            }}
          >
            {t("Normal_Done")}
          </Button>
        </div>
      </ModalBody>
    </ModalContent>
  );
};

const handleInputValueChange = (
  value: string,
  setInputValue: (value: string) => void,
  setIsValueValid: (isValid: boolean) => void,
  setErrorType: (errorType: string | null) => void,
  maxValue: string | null,
) => {
  if (value === "") {
    setInputValue("");
    setIsValueValid(false);
    setErrorType(null);
    return;
  }

  const numericValue = parseFloat(value.replace(/,/g, ""));
  if (isNaN(numericValue) && value !== ".") {
    return;
  }

  const maxNumericValue = maxValue ? parseFloat(maxValue) : Infinity;

  if (numericValue < 1) {
    setInputValue(value);
    setIsValueValid(false);
    setErrorType("min");
    return;
  }

  if (numericValue > maxNumericValue) {
    setInputValue(value);
    setIsValueValid(false);
    setErrorType("max");
    return;
  }

  const parts = value.split(".");
  const integerPart = parts[0].replace(/,/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  let decimalPart = parts[1] !== undefined ? `.${parts[1]}` : "";

  if (decimalPart.length > 5) {
    decimalPart = `.${parts[1].substring(0, 4)}`;
  }

  const formattedValue = integerPart + decimalPart;

  setInputValue(formattedValue);
  setIsValueValid(true);
  setErrorType(null);
};
interface WithdrawModalProps {
  balance: string | null;
  address: string;
  proxyWallet: string;
  visible: boolean;
  onClose: () => void;
  // magicUser: any;
}

const BULK_DATA_LIMIT = parseInt(process.env.NEXT_PUBLIC_BULK_DATA_LIMIT || "99999");

export default function WithdrawModal({ balance, address, proxyWallet, visible, onClose }: WithdrawModalProps) {
  const { t } = useTranslation();
  const { data: session } = useSession();
  const { walletType } = useGlobalState();

  const [inputAdress, setInputAdress] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [isValueValid, setIsValueValid] = useState(false);
  const [completedStatus, setCompletedStatus] = useState("unsubmit"); // unsubmit|success|failed
  const [buttonState, setButtonState] = useState("none"); // none|loading|success
  const [errorType, setErrorType] = useState<string | null>(null); // min|max|null
  const [transactionHash, setTransactionHash] = useState("");
  const [specialErrorText, setSpecialErrorText] = useState("");

  const [frozenValue, setFrozenValue] = useState(0);

  const clobApis = getItem("poly_clob_api_key_map");
  const handleRetry = () => {
    setCompletedStatus("unsubmit");
  };

  useEffect(() => {
    if (visible && clobApis) {
      const fetchOpenOrderData = async () => {
        try {
          const res = await getOpenOrderData(clobApis, walletType, {
            offset: 0,
            limit: BULK_DATA_LIMIT,
          });

          if (res?.data?.data && Array.isArray(res.data.data)) {
            let totalFrozenValue = 0;

            res.data.data.forEach((order: any) => {
              const { side, size_current, price } = order;
              if (side === "BUY") {
                const orderTotal = calculateOrderTotalWithFee(side, size_current, price);
                totalFrozenValue += orderTotal;
              }
            });

            setFrozenValue(totalFrozenValue);
          }
        } catch (error) {
          console.error("Error fetching open order data:", error);
          // 发生错误时设置为0，避免影响提现功能
          setFrozenValue(0);
        }
      };

      fetchOpenOrderData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, clobApis, walletType]);

  // 计算可提现金额
  const maxWithdrawable = balance ? truncateToTwoDecimals(parseFloat(balance) - frozenValue) : "0.00";

  return (
    <div>
      {clobApis && (
        <Modal isOpen={visible} onClose={onClose} size={"xl"}>
          {completedStatus === "success" && (
            <CompletedWithdraw
              inputValue={inputValue}
              onClose={onClose}
              handleRetry={handleRetry}
              transactionHash={transactionHash}
            />
          )}
          {completedStatus === "failed" && (
            <FailedWithdraw handleRetry={handleRetry} specialErrorText={specialErrorText} />
          )}
          {completedStatus === "unsubmit" && (
            <ModalContent>
              <ModalHeader className="flex-center gap-4">
                <div className="py-2">{t("Wallet_Withdraw")}</div>
              </ModalHeader>

              <ModalBody className="px-8 pb-8">
                <div className="w-full flex items-center px-4 py-3 bg-blue-50 text-sm rounded-md whitespace-normal">
                  <OctagonAlert className="size-4 mr-2" color="#0040ff" />
                  {t("Withdraw_Modal_Tips")}
                </div>
                <div className="flex flex-col gap-4">
                  <Input
                    label={
                      <AddressLabel
                        onUseConnected={() => {
                          setInputAdress(address);
                        }}
                      />
                    }
                    placeholder="0x..."
                    labelPlacement="outside"
                    radius="sm"
                    size="lg"
                    value={inputAdress}
                    onChange={e => {
                      const value = e.target.value;
                      setInputAdress(value);
                    }}
                    className="border rounded-medium"
                    classNames={{
                      inputWrapper: ["bg-transparent"],
                      label: ["w-full"],
                    }}
                  />
                  <AmountLabel
                    balance={maxWithdrawable}
                    onClickMax={() => {
                      handleInputValueChange(
                        maxWithdrawable || "",
                        setInputValue,
                        setIsValueValid,
                        setErrorType,
                        maxWithdrawable,
                      );
                    }}
                  />
                  <AmountInput
                    value={inputValue}
                    onChange={value =>
                      handleInputValueChange(value, setInputValue, setIsValueValid, setErrorType, maxWithdrawable)
                    }
                  />
                  {/* 错误信息显示 */}
                  {errorType === "min" && (
                    <div className="text-red-500 text-xs">{t("Withdraw_Modal_Min_Value_Error")}</div>
                  )}
                  {errorType === "max" && (
                    <div className="text-red-500 text-xs">{t("Withdraw_Modal_Max_Value_Error")}</div>
                  )}

                  <Button
                    size="lg"
                    color="primary"
                    radius="sm"
                    isLoading={buttonState === "loading"}
                    isDisabled={isValueValid === false || inputAdress === ""}
                    onPress={() =>
                      onClickWidthdraw(
                        proxyWallet,
                        address,
                        inputValue,
                        session?.cookie,
                        setCompletedStatus,
                        setButtonState,
                        setTransactionHash,
                        inputAdress,
                        setSpecialErrorText,
                        walletType,
                      )
                    }
                    className={`w-full font-semibold text-white mt-2`}
                  >
                    {t("Wallet_Withdraw")}
                  </Button>
                </div>
              </ModalBody>
            </ModalContent>
          )}
        </Modal>
      )}
    </div>
  );
}
