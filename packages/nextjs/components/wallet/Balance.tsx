import React, { useEffect, useRef, useState } from "react";
import WithdrawModal from "./WithdrawModal";
import DepositModal from "@/components/deposit/DepositModal";
import PreBuyModal from "@/components/details/signature/PreBuyModal";
import { useGlobalBalance } from "@/hooks/useGlobalBalance";
import { getProxyAndCheckApprove } from "@/utils";
import { Button } from "@heroui/react";
import { animated, useSpring } from "@react-spring/web";
import { MoreVertical, RefreshCcw } from "lucide-react";
import { useTranslation } from "react-i18next";
import { ArrowPathIcon } from "@heroicons/react/24/outline";

const BalanceComponent = (props: any) => {
  const { address, proxyWallet, isActiveWallet } = props;

  // 使用全局余额管理，作为从属组件，减少自动刷新但确保有数据
  const {
    balance,
    isLoading: isReloading,
    refreshBalance: refreshBalanceValue,
  } = useGlobalBalance(proxyWallet, {
    enableBlockWatch: false, // 从属组件不启用区块监听
    enableAutoRefresh: false, // 不启用定时刷新，依赖主组件
    skipInitialFetch: false, // 允许初始获取，确保有数据显示
  });

  const [isShowWithdrawModal, setIsShowWithdrawModal] = useState(false);
  const [isShowDepositModal, setIsShowDepositModal] = useState(false);
  const [isShowPreBuyModal, setIsShowPreBuyModal] = useState(false);
  const [animatedBalanceValue, setAnimatedBalanceValue] = useState<number>(0);

  const [isLoadingWithdraw, setIsLoadingWithdraw] = useState(false);
  const { t } = useTranslation();

  const prevBalanceRef = useRef<string | null>(null);

  const animatedBalance = useSpring({
    number: animatedBalanceValue,
    config: { duration: 300 },
    reset: true,
  });

  useEffect(() => {
    if (!proxyWallet) {
      setAnimatedBalanceValue(0);
      return;
    }

    if (balance !== null && !isReloading) {
      const numericBalance = parseFloat(balance);

      // 只有当 balance 值真正发生变化时才触发动画
      if (prevBalanceRef.current !== balance) {
        setAnimatedBalanceValue(0);
        setTimeout(() => {
          setAnimatedBalanceValue(numericBalance);
        }, 600);

        // 更新上一次的 balance 值
        prevBalanceRef.current = balance;
      }
    }
  }, [balance, isReloading, proxyWallet]);

  const handleWithdrawClick = async () => {
    if (address) {
      setIsLoadingWithdraw(true);
      await getProxyAndCheckApprove(address, setIsShowPreBuyModal);
      setIsShowWithdrawModal(true);
      setIsLoadingWithdraw(false);
    }
  };

  return (
    <div className="w-full flex flex-col p-5 border rounded-lg">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xs text-gray-500 font-medium tracking-widest">Wallet Balance</h1>
          <div className="text-4xl font-semibold flex items-center">
            <span>$</span>
            <animated.div>{animatedBalance.number.to(n => n.toFixed(2))}</animated.div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div
            className="flex-center size-10 border rounded-full cursor-pointer hover:bg-gray-100"
            onClick={() => refreshBalanceValue(true)}
          >
            <RefreshCcw className={`size-4 ${isReloading ? "animate-spin" : ""}`} />
          </div>
          <div className="flex-center size-10 border rounded-full cursor-not-allowed bg-gray-200">
            <MoreVertical className="size-4 text-gray-400" />
          </div>
        </div>
      </div>

      {isActiveWallet && (
        <div className="flex items-center gap-2 mt-4 text-gray-500">
          <Button
            size="md"
            variant="bordered"
            onPress={() => {
              setIsShowDepositModal(true);
            }}
            className="bg-black text-white hover:bg-gray-800 font-semibold text-sm md:text-base"
          >
            {t("Normal_Deposit")}
          </Button>

          <Button
            size="md"
            variant="bordered"
            onPress={handleWithdrawClick}
            className="bg-white hover:bg-gray-200 font-semibold text-sm md:text-base flex items-center gap-2"
          >
            {t("Normal_Withdraw")}
            {isLoadingWithdraw && <ArrowPathIcon className="w-4 h-4 animate-spin text-gray-500" />}
          </Button>
        </div>
      )}

      <WithdrawModal
        balance={balance || "0.00"}
        address={address}
        proxyWallet={proxyWallet}
        visible={isShowWithdrawModal && !isShowPreBuyModal}
        onClose={() => setIsShowWithdrawModal(false)}
        // magicUser={magicUser}
      />

      <DepositModal
        visible={isShowDepositModal}
        proxyWallet={proxyWallet}
        onClose={() => setIsShowDepositModal(false)}
      />

      <PreBuyModal address={address} visible={isShowPreBuyModal} onClose={() => setIsShowPreBuyModal(false)} />
    </div>
  );
};

export default BalanceComponent;
