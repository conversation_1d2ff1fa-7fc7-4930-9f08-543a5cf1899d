"use client";

import React, { useState } from "react";
import { useMagicStore } from "@/services/store/magicStore";
import { <PERSON>ton, Card, CardBody, Divider, Image, Link } from "@heroui/react";
import { useConnectModal } from "@rainbow-me/rainbowkit";
import { useTranslation } from "react-i18next";

const LoginStep = () => {
  const { t } = useTranslation();
  const { magic } = useMagicStore();
  const { openConnectModal } = useConnectModal();
  const [loadStatus, setLoadStatus] = useState<{
    google: "none" | "loading" | "failed";
    twitter: "none" | "loading" | "failed";
    discord: "none" | "loading" | "failed";
  }>({
    google: "none",
    twitter: "none",
    discord: "none",
  });

  const isAnyLoading =
    loadStatus.google === "loading" || loadStatus.twitter === "loading" || loadStatus.discord === "loading";

  const handleGoogleLogin = async () => {
    if (!magic) {
      console.error("Magic not initialized");
      return;
    }

    setLoadStatus(prev => ({ ...prev, google: "loading" }));
    try {
      await magic.oauth.loginWithRedirect({
        provider: "google",
        redirectURI: new URL("/auth/callback", window.location.origin).href,
      });
    } catch (error) {
      console.error("Google login failed:", error);
      setLoadStatus(prev => ({ ...prev, google: "failed" }));
      setTimeout(() => setLoadStatus(prev => ({ ...prev, google: "none" })), 3000);
    }
  };

  const handleTwitterLogin = async () => {
    if (!magic) {
      console.error("Magic not initialized");
      return;
    }

    setLoadStatus(prev => ({ ...prev, twitter: "loading" }));
    try {
      await magic.oauth.loginWithRedirect({
        provider: "twitter",
        redirectURI: new URL("/auth/callback", window.location.origin).href,
      });
    } catch (error) {
      console.error("Twitter login failed:", error);
      setLoadStatus(prev => ({ ...prev, twitter: "failed" }));
      setTimeout(() => setLoadStatus(prev => ({ ...prev, twitter: "none" })), 3000);
    }
  };

  const handleDiscordLogin = async () => {
    if (!magic) {
      console.error("Magic not initialized");
      return;
    }

    setLoadStatus(prev => ({ ...prev, discord: "loading" }));
    try {
      await magic.oauth.loginWithRedirect({
        provider: "discord",
        redirectURI: new URL("/auth/callback", window.location.origin).href,
      });
    } catch (error) {
      console.error("Discord login failed:", error);
      setLoadStatus(prev => ({ ...prev, discord: "failed" }));
      setTimeout(() => setLoadStatus(prev => ({ ...prev, discord: "none" })), 3000);
    }
  };

  const handleWalletConnect = () => {
    if (openConnectModal) {
      openConnectModal();
    }
  };

  const getButtonText = (provider: "google" | "twitter" | "discord") => {
    switch (loadStatus[provider]) {
      case "loading":
        return t("Normal_Login_Loading");
      case "failed":
        return t("Normal_Login_Failed");
      default:
        return provider === "google"
          ? t("Normal_Google_Login")
          : provider === "twitter"
          ? t("Normal_Twitter_Login")
          : t("Normal_Discord_Login");
    }
  };

  return (
    <div className="w-full flex flex-col p-6 gap-6 border rounded-lg">
      {/* Step 1 */}
      <div>
        <div className="text-xl font-semibold text-gray-800 mb-4">{t("login.wallet.step1.title")}</div>

        <div className="space-y-4">
          {/* Google 登录选项 */}
          <Card className="border border-gray-200 hover:shadow-sm transition-shadow">
            <CardBody className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-md">
                    <Image src="/google_logo.svg" alt="Google" width={24} height={24} className="rounded" />
                  </div>
                  <div>
                    <div className="font-medium">{t("login.google.title")}</div>
                    <div className="text-xs text-gray-500">{t("login.google.description")}</div>
                  </div>
                </div>
              </div>
              <Button
                className="w-full bg-blue-700 text-white hover:bg-blue-600"
                onPress={handleGoogleLogin}
                isLoading={loadStatus.google === "loading"}
                isDisabled={!magic || isAnyLoading}
              >
                {getButtonText("google")}
              </Button>
            </CardBody>
          </Card>

          <div className="flex items-center gap-3">
            <Divider className="flex-1" />
            <span className="text-xs text-gray-500 px-2">{t("login.modal.or")}</span>
            <Divider className="flex-1" />
          </div>

          {/* 三个选项的水平布局 */}
          <div className="flex items-center justify-between gap-4">
            {/* 钱包连接选项 */}
            <Card
              className="flex-1 border border-gray-200 hover:shadow-sm transition-shadow"
              isPressable
              onPress={handleWalletConnect}
              isDisabled={!openConnectModal || isAnyLoading}
            >
              <CardBody className="p-4 flex flex-col items-center gap-2">
                <div className="w-10 h-10 bg-gradient-to-br from-red-300 to-orange-500 rounded-xl flex items-center justify-center shadow-md">
                  <Image src="/metaMask_icon.svg" alt="MetaMask" width={24} height={24} className="rounded" />
                </div>
                <div className="text-center">
                  <div className="font-medium text-sm">{t("login.wallet.title")}</div>
                  <div className="text-xs text-gray-500">{t("login.wallet.description")}</div>
                </div>
              </CardBody>
            </Card>

            {/* Twitter 登录选项 */}
            <Card
              className="flex-1 border border-gray-200 hover:shadow-sm transition-shadow"
              isPressable
              onPress={handleTwitterLogin}
              isDisabled={!magic || isAnyLoading}
            >
              <CardBody className="p-4 flex flex-col items-center gap-2">
                <div className="w-10 h-10 bg-black rounded-xl flex items-center justify-center shadow-md">
                  <Image src="/x-light-icon.png" alt="Twitter" width={24} height={24} className="rounded" />
                </div>
                <div className="text-center">
                  <div className="font-medium text-sm">{t("login.twitter.title")}</div>
                  <div className="text-xs text-gray-500">{t("login.twitter.description")}</div>
                </div>
              </CardBody>
            </Card>

            {/* Discord 登录选项 */}
            <Card
              className="flex-1 border border-gray-200 hover:shadow-sm transition-shadow"
              isPressable
              onPress={handleDiscordLogin}
              isDisabled={!magic || isAnyLoading}
            >
              <CardBody className="p-4 flex flex-col items-center gap-2">
                <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md">
                  <Image src="/Discord_logo.svg" alt="Discord" width={24} height={24} className="rounded" />
                </div>
                <div className="text-center">
                  <div className="font-medium text-sm">{t("login.discord.title")}</div>
                  <div className="text-xs text-gray-500">{t("login.discord.description")}</div>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      </div>

      {/* Step 2 */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200">
        <CardBody className="p-4">
          <div className="text-lg font-semibold text-gray-800 mb-2">{t("login.wallet.step2.title")}</div>
          <div className="text-sm text-gray-600 leading-relaxed">
            {t("login.wallet.step2.description") ||
              "Next, after log in, you can see the deposit page. Deposit just 1 USDC to unlock an incredible additional 90 USDC free credit!"}
          </div>
        </CardBody>
      </Card>

      {/* 条款说明 */}
      <div className="pt-2 border-t border-gray-100">
        <div className="text-xs text-gray-500 text-center">
          <Link
            href="https://predict.one/toe"
            className="text-gray-500 hover:underline text-sm font-semibold cursor-pointer"
            isExternal
            showAnchorIcon={false}
          >
            {t("login.terms")}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LoginStep;
