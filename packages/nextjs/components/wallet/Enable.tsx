import React, { useState } from "react";
import { But<PERSON>, Image, Tooltip } from "@heroui/react";
import { NetworkEthereum } from "@web3icons/react";
import { useSession } from "next-auth/react";
import { useTranslation } from "react-i18next";
import { handleCreateProxyWallet } from "~~/utils/signature/createProxy";

const Enable = (props: any) => {
  const { proxyWallet, isDisableBtn, setIsCreatedProxy, setIsActiveWallet } = props;
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const { t } = useTranslation();
  const { data: session } = useSession();
  const isDisabled = !proxyWallet && (isBtnLoading || isDisableBtn);

  return (
    <div className="w-full sm:w-[660px] flex flex-col items-center justify-start p-5 gap-5 border rounded-lg">
      <div className="text-xl font-semibold">{t("Wallet_Enable_Trading")}</div>
      <div className="flex items-center gap-2">
        <div className="flex items-center border rounded-full font-semibold p-1">
          <Image src="/logo.png" alt="PredictionOne" className="rounded-full" width={40} height={40} />
        </div>
        <div className="text-lg text-gray-300 font-bold">---</div>
        <div className="flex items-center border rounded-full font-semibold p-1">
          <NetworkEthereum className="size-10" variant="branded" />
        </div>
      </div>

      <div className="w-full text-medium text-center">{t("Wallet_Enable_Trading_Text")}</div>
      <Tooltip content="Please disconnect and connect with the correct wallet" color="danger" isOpen={showTooltip}>
        <Button
          isLoading={isBtnLoading}
          isDisabled={isDisabled}
          className="w-full h-12 bg-blue-600 text-white font-medium rounded-md"
          onPress={() =>
            handleCreateProxyWallet(
              session,
              proxyWallet,
              setIsCreatedProxy,
              setIsBtnLoading,
              setIsActiveWallet,
              setShowTooltip,
            )
          }
        >
          {t("Normal_Continue")}
        </Button>
      </Tooltip>
    </div>
  );
};

export default Enable;
