"use client";

import React, { useState } from "react";
import { useMagicStore } from "@/services/store/magicStore";
import {
  Button,
  Card,
  CardBody,
  Divider,
  Image,
  Link,
  Modal,
  ModalBody,
  ModalContent,
  ModalHeader,
} from "@heroui/react";
import { useConnectModal } from "@rainbow-me/rainbowkit";
import { useTranslation } from "react-i18next";

interface CustomConnectModalProps {
  isOpen: boolean;
  onClose: () => void;
  showOnlyMagic?: boolean;
}

const CustomConnectModal: React.FC<CustomConnectModalProps> = ({ isOpen, onClose, showOnlyMagic = false }) => {
  const { t } = useTranslation();
  const { magic } = useMagicStore();
  const { openConnectModal } = useConnectModal();
  const [loadStatus, setLoadStatus] = useState<{
    google: "none" | "loading" | "failed";
    twitter: "none" | "loading" | "failed";
    discord: "none" | "loading" | "failed";
  }>({
    google: "none",
    twitter: "none",
    discord: "none",
  });

  const isAnyLoading =
    loadStatus.google === "loading" || loadStatus.twitter === "loading" || loadStatus.discord === "loading";

  const handleGoogleLogin = async () => {
    if (!magic) {
      console.error("Magic not initialized");
      return;
    }

    setLoadStatus(prev => ({ ...prev, google: "loading" }));
    try {
      await magic.oauth.loginWithRedirect({
        provider: "google",
        redirectURI: new URL("/auth/callback", window.location.origin).href,
      });
    } catch (error) {
      console.error("Google login failed:", error);
      setLoadStatus(prev => ({ ...prev, google: "failed" }));
      setTimeout(() => setLoadStatus(prev => ({ ...prev, google: "none" })), 3000);
    }
  };

  const handleTwitterLogin = async () => {
    if (!magic) {
      console.error("Magic not initialized");
      return;
    }

    setLoadStatus(prev => ({ ...prev, twitter: "loading" }));
    try {
      await magic.oauth.loginWithRedirect({
        provider: "twitter",
        redirectURI: new URL("/auth/callback", window.location.origin).href,
      });
    } catch (error) {
      console.error("Twitter login failed:", error);
      setLoadStatus(prev => ({ ...prev, twitter: "failed" }));
      setTimeout(() => setLoadStatus(prev => ({ ...prev, twitter: "none" })), 3000);
    }
  };

  const handleDiscordLogin = async () => {
    if (!magic) {
      console.error("Magic not initialized");
      return;
    }

    setLoadStatus(prev => ({ ...prev, discord: "loading" }));
    try {
      await magic.oauth.loginWithRedirect({
        provider: "discord",
        redirectURI: new URL("/auth/callback", window.location.origin).href,
      });
    } catch (error) {
      console.error("Discord login failed:", error);
      setLoadStatus(prev => ({ ...prev, discord: "failed" }));
      setTimeout(() => setLoadStatus(prev => ({ ...prev, discord: "none" })), 3000);
    }
  };

  const handleWalletConnect = () => {
    onClose();
    if (openConnectModal) {
      openConnectModal();
    }
  };

  const getGoogleButtonText = () => {
    switch (loadStatus.google) {
      case "loading":
        return t("Normal_Login_Loading");
      case "failed":
        return t("Normal_Login_Failed");
      default:
        return t("Normal_Google_Login");
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      placement="center"
      backdrop="opaque"
      classNames={{
        base: "max-w-md",
        header: "border-b border-gray-200",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-xl font-semibold text-center">{t("login.modal.title")}</h2>
        </ModalHeader>

        <ModalBody>
          <div className="space-y-4">
            {/* Google 登录选项 */}
            <Card className="border border-gray-200 hover:shadow-sm transition-shadow">
              <CardBody className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-md">
                      <Image src="/google_logo.svg" alt="Google" width={24} height={24} className="rounded" />
                    </div>
                    <div>
                      <div className="font-medium">{t("login.google.title")}</div>
                      <div className="text-xs text-gray-500">{t("login.google.description")}</div>
                    </div>
                  </div>
                </div>
                <Button
                  className="w-full bg-blue-700 text-white hover:bg-blue-600"
                  onPress={handleGoogleLogin}
                  isLoading={loadStatus.google === "loading"}
                  isDisabled={!magic || isAnyLoading}
                >
                  {getGoogleButtonText()}
                </Button>
              </CardBody>
            </Card>

            {!showOnlyMagic && (
              <>
                <div className="flex items-center gap-3">
                  <Divider className="flex-1" />
                  <span className="text-xs text-gray-500 px-2">{t("login.modal.or")}</span>
                  <Divider className="flex-1" />
                </div>

                <div className="flex items-center justify-between">
                  {/* 钱包连接选项 */}
                  <Card
                    className="border border-gray-200 hover:shadow-sm transition-shadow px-4"
                    isPressable
                    onPress={handleWalletConnect}
                    isDisabled={!openConnectModal || isAnyLoading}
                  >
                    <CardBody className="p-4 flex flex-center">
                      <div className="flex items-center justify-between">
                        <Image src="/metaMask_icon.svg" alt="MetaMask" width={36} height={36} className="rounded" />
                      </div>
                    </CardBody>
                  </Card>

                  {/* Twitter 登录选项 */}
                  <Card
                    className="border border-gray-200 hover:shadow-sm transition-shadow px-4"
                    isPressable
                    onPress={handleTwitterLogin}
                    isDisabled={!magic || isAnyLoading}
                  >
                    <CardBody className="p-4 flex flex-center">
                      <div className="flex items-center justify-between">
                        <Image src="/x-dark-icon.png" alt="Twitter" width={36} height={36} className="rounded" />
                      </div>
                    </CardBody>
                  </Card>

                  {/* Discord 登录选项 */}
                  <Card
                    className="border border-gray-200 hover:shadow-sm transition-shadow px-4"
                    isPressable
                    onPress={handleDiscordLogin}
                    isDisabled={!magic || isAnyLoading}
                  >
                    <CardBody className="p-4 flex flex-center">
                      <div className="flex items-center justify-between">
                        <Image src="/Discord_logo.svg" alt="Discord_logo" width={36} height={36} className="rounded" />
                      </div>
                    </CardBody>
                  </Card>
                </div>
              </>
            )}

            {showOnlyMagic && (
              <>
                <div className="flex items-center gap-3">
                  <Divider className="flex-1" />
                  <span className="text-xs text-gray-500 px-2">{t("login.modal.or")}</span>
                  <Divider className="flex-1" />
                </div>

                <div className="flex items-center justify-center gap-4">
                  {/* Twitter 登录选项 */}
                  <Card
                    className="border border-gray-200 hover:shadow-sm transition-shadow px-4"
                    isPressable
                    onPress={handleTwitterLogin}
                    isDisabled={!magic || isAnyLoading}
                  >
                    <CardBody className="p-4 flex flex-center">
                      <div className="flex items-center justify-between">
                        <Image src="/x-dark-icon.png" alt="Twitter" width={36} height={36} className="rounded" />
                      </div>
                    </CardBody>
                  </Card>

                  {/* Discord 登录选项 */}
                  <Card
                    className="border border-gray-200 hover:shadow-sm transition-shadow px-4"
                    isPressable
                    onPress={handleDiscordLogin}
                    isDisabled={!magic || isAnyLoading}
                  >
                    <CardBody className="p-4 flex flex-center">
                      <div className="flex items-center justify-between">
                        <Image src="/Discord_logo.svg" alt="Discord_logo" width={36} height={36} className="rounded" />
                      </div>
                    </CardBody>
                  </Card>
                </div>
              </>
            )}
          </div>

          {/* 条款说明 */}
          <div className="pt-2 border-gray-100">
            <div className="text-xs text-gray-500 text-center">
              <Link
                href="https://predict.one/toe"
                className="text-gray-500 hover:underline text-sm font-semibold cursor-pointer"
                isExternal
                showAnchorIcon={false}
              >
                {t("login.terms")}
              </Link>
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default CustomConnectModal;
