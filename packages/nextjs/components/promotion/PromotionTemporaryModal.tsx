import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { useTranslation } from "react-i18next";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface PromotionModalProps {
  visible: boolean;
  onClose: () => void;
}

const PromotionModal: React.FC<PromotionModalProps> = ({ visible, onClose }) => {
  const { t } = useTranslation();
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (visible) {
      setShow(visible);
      document.body.style.overflow = "hidden";
    } else {
      setShow(false);
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [visible]);

  if (!visible) return null;

  const offerList = [
    {
      icon: "🎁",
      gradient: "bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500",
      text: t("promotion.offers.offer1"),
      hasLink: true,
    },
    {
      icon: "⭐",
      gradient: "bg-gradient-to-r from-purple-500 via-violet-500 to-pink-500",
      text: t("promotion.offers.offer2"),
      hasLink: false,
    },
  ];

  const renderOfferContent = (offer: any) => {
    if (offer.hasLink) {
      return (
        <span className={`font-bold bg-clip-text text-sm md:text-xl text-transparent ${offer.gradient}`}>
          <Link
            href="/wallet"
            className="text-center underline text-sm md:text-xl mr-1 text-orange-500 hover:text-orange-600 font-bold bg-clip-text"
          >
            {offer.text}
          </Link>
        </span>
      );
    } else {
      return (
        <span className={`text-center font-bold bg-clip-text text-sm md:text-xl text-transparent ${offer.gradient}`}>
          {offer.text}
        </span>
      );
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-gray-900 bg-opacity-50 p-4">
      <div
        className={`
          relative mx-auto bg-gradient-to-br from-yellow-50 via-orange-50 to-red-50 shadow-2xl overflow-hidden rounded-2xl border-2 border-orange-200
          transition-transform duration-800
          ${show ? "scale-100" : "scale-50 opacity-0"}
          w-full max-w-md md:max-w-4xl max-h-[90vh] overflow-y-auto
        `}
        style={{ willChange: "transform, opacity" }}
      >
        {/* 背景装饰 - 移动端缩小 */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-2 right-4 md:top-4 md:right-8 text-3xl md:text-6xl animate-bounce">🎉</div>
          <div className="absolute bottom-3 left-4 md:bottom-6 md:left-8 text-2xl md:text-4xl animate-pulse">✨</div>
          <div className="absolute top-6 left-6 md:top-12 md:left-12 text-xl md:text-3xl animate-ping">🎊</div>
        </div>

        <div className="relative w-full h-full">
          <div className="relative flex flex-col items-center z-10 px-4 md:px-8 py-4 md:py-6">
            <Button
              onPress={onClose}
              isIconOnly
              variant="light"
              className="absolute size-5 md:size-6 top-2 right-3 md:right-6 text-gray-500 hover:text-gray-700 z-100"
              aria-label="Close"
            >
              <XMarkIcon className="w-4 h-4 md:w-6 md:h-6" />
            </Button>

            {/* 标题区域 */}
            <div className="w-full text-center mb-4 md:mb-6">
              <h2 className="text-xl md:text-3xl lg:text-5xl font-sans py-2 md:py-3 font-bold tracking-tight bg-gradient-to-r from-orange-600 via-red-500 to-pink-600 bg-clip-text text-transparent">
                {t("promotion.title")}
              </h2>
              <div className="text-sm md:text-lg lg:text-xl font-semibold mt-2 md:mt-3 text-gray-700">
                {t("promotion.subtitle")}
              </div>
            </div>

            {/* 恭喜消息 */}
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 md:p-6 mb-4 md:mb-6 border border-orange-200 shadow-lg">
              <p className="text-xs md:text-base lg:text-lg text-gray-800 leading-relaxed text-center">
                {t("promotion.congratsMessage")}
              </p>
            </div>

            {/* 优惠列表 */}
            <div className="grid grid-cols-1 gap-3 md:gap-4 w-full mb-4 md:mb-6">
              {offerList.map((offer, idx) => (
                <div
                  key={idx}
                  className="bg-white/90 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-orange-200 shadow-lg hover:shadow-xl transition-shadow"
                >
                  <div className="flex items-center justify-center text-center">
                    <span className="mr-2 md:mr-3 text-lg md:text-2xl">{offer.icon}</span>
                    {renderOfferContent(offer)}
                  </div>
                </div>
              ))}
            </div>

            {/* 详细说明 */}
            <div className="bg-gradient-to-r from-orange-100 to-yellow-100 rounded-xl p-4 md:p-6 border border-orange-200 shadow-lg">
              <p className="text-xs md:text-sm lg:text-base text-gray-700 leading-relaxed text-center">
                {t("promotion.details")}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromotionModal;
