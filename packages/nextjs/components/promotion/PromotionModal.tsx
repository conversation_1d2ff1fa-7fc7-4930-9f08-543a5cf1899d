import React, { useEffect, useState } from "react";
import { BackgroundBeamsWithCollision } from "./ui/RaindropBack";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { useTranslation } from "react-i18next";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface PromotionModalProps {
  visible: boolean;
  onClose: () => void;
}

const PromotionModal: React.FC<PromotionModalProps> = ({ visible, onClose }) => {
  const { t } = useTranslation();
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (visible) {
      setShow(visible);
      document.body.style.overflow = "hidden";
    } else {
      setShow(false);
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [visible]);

  if (!visible) return null;

  const offerList = [
    {
      icon: "💰",
      gradient: "bg-gradient-to-r from-blue-400 via-cyan-400 to-green-400",
      text: t("promotion.offers.offer1"),
      hasLink: true,
    },
    {
      icon: "💎",
      gradient: "bg-gradient-to-r from-purple-500 via-violet-500 to-pink-500",
      text: t("promotion.offers.offer2"),
      hasLink: false,
    },
  ];

  const whyUsList = [t("promotion.whyus_list.item1"), t("promotion.whyus_list.item2"), t("promotion.whyus_list.item3")];

  const renderOfferContent = (offer: any) => {
    if (offer.hasLink) {
      return (
        <span className={`font-bold bg-clip-text text-2xl text-transparent ${offer.gradient}`}>
          <Link
            href="/wallet"
            className="text-center underline text-2xl mr-1 text-blue-400 hover:text-blue-600 font-bold bg-clip-text"
          >
            {offer.text}
          </Link>
        </span>
      );
    } else {
      return <span className={`text-center font-bold bg-clip-text text-2xl text-pink-500`}>{offer.text}</span>;
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-gray-900 bg-opacity-50">
      <div
        className={`
          relative mx-auto bg-white shadow-2xl pt-6 overflow-hidden rounded-2xl
          transition-transform duration-800
          ${show ? "scale-100" : "scale-50 opacity-0"}
          w-full max-w-3xl
        `}
        style={{ willChange: "transform, opacity" }}
      >
        <div className="relative w-full h-full">
          <BackgroundBeamsWithCollision className="absolute inset-0 w-full h-auto z-0 t-0 mb-4">
            {null}
          </BackgroundBeamsWithCollision>
          <div className="relative flex flex-col items-center z-10 px-6 py-4 pb-3 max-w-3xl w-full">
            <Button
              onPress={onClose}
              isIconOnly
              variant="light"
              className="absolute size-6 top-0 right-4 text-gray-500 hover:text-gray-300 z-100"
              aria-label="Close"
            >
              <XMarkIcon className="w-6 h-6" />
            </Button>
            {/* 标题 */}
            <div className="w-full text-center">
              <h2 className="text-2xl md:text-4xl font-sans py-2 font-bold tracking-tight">{t("promotion.title")}</h2>
              <div className="text-lg md:text-xl font-semibold mt-2">{t("promotion.subtitle")}</div>
            </div>

            {/* 优惠列表 */}
            <ul className="mt-6 space-y-2 text-base md:text-lg font-medium w-auto mx-auto">
              {offerList.map((offer, idx) => (
                <li key={idx} className="flex items-start justify-center text-left">
                  <span className="mr-2 h-full flex flex-center mt-0.5">{offer.icon}</span>
                  {renderOfferContent(offer)}
                </li>
              ))}
            </ul>

            {/* Why Us */}
            <div className="mt-6 font-bold text-xl text-center w-full">{t("promotion.whyus")}</div>
            <ul className="mt-2 space-y-1 text-base w-full max-w-xl mx-auto text-center">
              {whyUsList.map((item, idx) => {
                const match = item.match(/^(\S+\s+[^!！]+[!！])\s*(.*)$/);
                return (
                  <li key={idx} className="text-center text-lg">
                    {match ? (
                      <>
                        <span className="font-bold">{match[1]}</span>
                        {match[2] && <span className="text-gray-600"> {match[2]}</span>}
                      </>
                    ) : (
                      <span>{item}</span>
                    )}
                  </li>
                );
              })}
            </ul>

            <div className="w-full flex justify-center my-4">
              <Link
                href="/wallet"
                className="px-6 py-2 text-lg font-bold bg-blue-500 hover:bg-blue-600 transition duration-200 rounded-xl text-white"
              >
                {t("promotion.buttons.cta_primary")}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromotionModal;
