"use client";

import React, { useEffect, useState } from "react";
import { getSportGamesEventsListData } from "@/api/sports";
import EventCard from "@/components/events-components/EventCard";
import { getCurrentTimestamp } from "@/utils";
import { COMBINEDMOCKLEFTNAVLIST } from "@/utils/mock";
import { useTranslation } from "react-i18next";

const MarketsCardList = (props: any) => {
  const { EventCardListData } = props;
  const { t } = useTranslation(); // 添加 i18n hook
  const timeStamp = getCurrentTimestamp();

  return (
    <div className="w-full flex flex-col items-center justify-center">
      {EventCardListData?.length > 0 ? (
        <div className="w-full gap-3 grid" style={{ gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))" }}>
          {EventCardListData?.map((data: any, index: any) => (
            <EventCard key={index} eventCardData={data} timeStamp={timeStamp} />
          ))}
        </div>
      ) : (
        <div className="flex justify-center py-8">
          <p className="text-gray-500">{t("sports.no_events_available")}</p>
        </div>
      )}
    </div>
  );
};

interface PropsContentProps {
  secondRouting: string;
}

const PropsContent: React.FC<PropsContentProps> = ({ secondRouting }) => {
  const [eventList, setEventList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation(); // 添加 i18n hook

  useEffect(() => {
    const fetchSportEvents = async () => {
      if (!secondRouting) {
        return;
      }

      const selectedNavItem = COMBINEDMOCKLEFTNAVLIST.find(item => item.key === secondRouting);

      if (!selectedNavItem) {
        return;
      }
      setLoading(true);

      try {
        // Props 只获取 tag_id 的数据
        if (!selectedNavItem.tag_id) {
          console.log("⚠️ No tag_id found for Props, setting empty event list");
          setEventList([]);
          return;
        }

        const response = await getSportGamesEventsListData({
          active: true,
          closed: false,
          offset: 0,
          limit: 20,
          tag_id: selectedNavItem.tag_id,
        });

        const events = response.data.events || [];
        setEventList(events);
      } catch (error) {
        console.error(`❌ Error fetching events for ${t(selectedNavItem.nameKey)}:`, error);
        setEventList([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSportEvents();
  }, [secondRouting, t]); // 添加 t 作为依赖项

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <p>{t("sports.loading_events")}</p>
      </div>
    );
  }

  return (
    <div className="flex relative h-screen flex-col">
      <div className="flex gap-4">
        <div className={`flex-1 flex flex-col overflow-hidden transition-all duration-300`}>
          <MarketsCardList EventCardListData={eventList} />
        </div>
      </div>
    </div>
  );
};

export default PropsContent;
