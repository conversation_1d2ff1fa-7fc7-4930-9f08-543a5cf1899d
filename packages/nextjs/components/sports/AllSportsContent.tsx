"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { getSportGamesEventsListData } from "@/api/sports";
import EventCard from "@/components/events-components/EventCard";
import { getCurrentTimestamp } from "@/utils";
import { COMBINEDMOCKLEFTNAVLIST } from "@/utils/mock";
import { useTranslation } from "react-i18next";

const SportSection = ({ sport, events, timeStamp, t }: any) => {
  if (!events || events.length === 0) return null;

  return (
    <div className="mb-8">
      <div className="flex items-center mb-4">
        <Image src={sport.icon} alt={t(sport.nameKey)} width={32} height={32} className="size-8 rounded-lg mr-3" />
        <h2 className="text-2xl font-bold">{t(sport.nameKey)}</h2>
        <span className="ml-2 text-gray-500">({events.length})</span>
      </div>

      <div className="w-full gap-3 grid" style={{ gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))" }}>
        {events.map((data: any, index: any) => (
          <EventCard key={`${sport.key}-${index}`} eventCardData={data} timeStamp={timeStamp} />
        ))}
      </div>

      <div className="mt-6 h-px bg-gray-200" />
    </div>
  );
};

const AllSportsContent: React.FC = () => {
  const [allSportsEvents, setAllSportsEvents] = useState<{ [key: string]: any[] }>({});
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation(); // 添加 i18n hook
  const timeStamp = getCurrentTimestamp();

  useEffect(() => {
    const fetchAllSportsEvents = async () => {
      setLoading(true);
      const eventsData: { [key: string]: any[] } = {};

      for (const sport of COMBINEDMOCKLEFTNAVLIST) {
        try {
          // 同时获取 tag_id 和 tag_game_id 的数据
          const requests = [];

          if (sport.tag_id) {
            requests.push(
              getSportGamesEventsListData({
                active: true,
                closed: false,
                offset: 0,
                limit: 20,
                tag_id: sport.tag_id,
              }),
            );
          }

          if (sport.tag_game_id) {
            requests.push(
              getSportGamesEventsListData({
                active: true,
                closed: false,
                offset: 0,
                limit: 20,
                tag_id: sport.tag_game_id,
              }),
            );
          }

          const responses = await Promise.all(requests);
          const allEvents = responses.reduce((acc, response) => {
            return acc.concat(response.data.events || []);
          }, []);

          eventsData[sport.key] = allEvents;
        } catch (error) {
          console.error(`Error fetching events for ${t(sport.nameKey)}:`, error);
          eventsData[sport.key] = [];
        }
      }

      setAllSportsEvents(eventsData);
      setLoading(false);
    };

    fetchAllSportsEvents();
  }, [t]);

  if (loading) {
    return <div className="flex justify-center py-8">Loading...</div>;
  }

  return (
    <div className="flex flex-col">
      {COMBINEDMOCKLEFTNAVLIST.map(sport => (
        <SportSection key={sport.key} sport={sport} events={allSportsEvents[sport.key]} timeStamp={timeStamp} t={t} />
      ))}
    </div>
  );
};

export default AllSportsContent;
