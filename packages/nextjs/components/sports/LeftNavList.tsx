import React from "react";
import { COMBINEDMOCKLEFTNAVLIST } from "@/utils/mock";
import { Divider, Image, Link } from "@heroui/react";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "react-responsive";

// 添加 i18n hook

const LeftNavList = (props: any) => {
  const { secondRouting } = props;
  const { t } = useTranslation(); // 使用 i18n
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const specialItems = COMBINEDMOCKLEFTNAVLIST.filter(sport => sport.isSpecial);
  const nonSpecialItems = COMBINEDMOCKLEFTNAVLIST.filter(sport => !sport.isSpecial);

  const allSportsItem = specialItems.find(item => item.key === "");

  return (
    <div>
      {isMobile && (
        <div className="flex overflow-x-auto whitespace-nowrap pt-4">
          {allSportsItem && (
            <Link
              key={allSportsItem.id}
              href={allSportsItem.href}
              className="inline-block text-sm font-semibold text-black mx-2"
            >
              <div
                className={`flex flex-col items-center p-2 rounded-lg cursor-pointer transition-all duration-500 ease-in-out ${
                  !secondRouting || secondRouting === "" ? "bg-gray-200" : ""
                } hover:bg-blue-200`}
              >
                <Image
                  src={allSportsItem.icon}
                  alt={t(allSportsItem.nameKey)}
                  className="size-6 rounded-lg"
                  width={24}
                  height={24}
                />
                {t(allSportsItem.nameKey)}
              </div>
            </Link>
          )}

          {specialItems
            .filter(item => item.key !== "")
            .concat(nonSpecialItems)
            .map(sport => (
              <Link
                key={sport.id}
                href={`/sports/${sport.key}`}
                className="inline-block text-sm font-semibold text-black mx-2"
              >
                <div
                  className={`flex flex-col items-center p-2 rounded-lg cursor-pointer transition-all duration-500 ease-in-out ${
                    secondRouting === sport.key ? "bg-gray-200" : ""
                  } hover:bg-blue-200`}
                >
                  <Image src={sport.icon} alt={t(sport.nameKey)} className="size-6 rounded-lg" width={24} height={24} />
                  {t(sport.nameKey)}
                </div>
              </Link>
            ))}
        </div>
      )}

      {!isMobile && (
        <div className="min-w-[150px] h-full overflow-y-auto scrollbar-hide py-8">
          {allSportsItem && (
            <div className="mb-4">
              <Link href={allSportsItem.href} className="w-full text-sm font-semibold text-black">
                <div
                  className={`flex gap-2 w-full items-center p-4 rounded-lg cursor-pointer transition-all duration-500 ease-in-out ${
                    !secondRouting || secondRouting === "" ? "bg-gray-200" : ""
                  } hover:bg-blue-200`}
                >
                  <Image
                    src={allSportsItem.icon}
                    alt={t(allSportsItem.nameKey)}
                    className="size-6 rounded-lg"
                    width={24}
                    height={24}
                  />
                  {t(allSportsItem.nameKey)}
                </div>
              </Link>
            </div>
          )}

          {specialItems.filter(item => item.key !== "").length > 0 && (
            <>
              <div>
                {specialItems
                  .filter(item => item.key !== "")
                  .map(sport => (
                    <Link
                      key={sport.id}
                      href={`/sports/${sport.key}`}
                      className="w-full text-sm font-semibold text-black"
                    >
                      <div
                        className={`flex gap-2 w-full items-center p-4 rounded-lg cursor-pointer transition-all duration-500 ease-in-out ${
                          secondRouting === sport.key ? "bg-gray-200" : ""
                        } hover:bg-blue-200`}
                      >
                        <Image
                          src={sport.icon}
                          alt={t(sport.nameKey)}
                          className="size-6 rounded-lg"
                          width={24}
                          height={24}
                        />
                        {t(sport.nameKey)}
                      </div>
                    </Link>
                  ))}
              </div>
              <Divider className="my-4 bg-gray-200" />
            </>
          )}

          <div>
            {nonSpecialItems.map(sport => (
              <Link key={sport.id} href={`/sports/${sport.key}`} className="w-full text-sm font-medium text-black">
                <div
                  className={`flex gap-2 w-full items-center p-4 rounded-lg cursor-pointer transition-all duration-500 ease-in-out ${
                    secondRouting === sport.key ? "bg-gray-200" : ""
                  } hover:bg-blue-200`}
                >
                  <Image src={sport.icon} alt={t(sport.nameKey)} className="size-6 rounded-lg" width={24} height={24} />
                  {t(sport.nameKey)}
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LeftNavList;
