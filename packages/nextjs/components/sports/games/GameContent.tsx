"use client";

import React, { useCallback, useEffect, useState } from "react";
import GameList from "./GameList";
import { getSportGamesEventsListData } from "@/api/sports";
import OrderCard from "@/components/details/orderCard/OrderCard";
import { useClobApiOptimized } from "@/hooks/secret/useClobApiOptimized";

// import { sortAndSliceMarkets, sortMarketsBylastTradePrice } from "@/utils";

interface GamesContentProps {
  tagGameId?: number;
}

const GamesContent: React.FC<GamesContentProps> = ({ tagGameId }) => {
  const { clobApis } = useClobApiOptimized();
  const [eventList, setEventList] = useState<any[]>([]);
  const [currentEvent, setCurrentEvent] = useState<any>({});
  // const [chartDataList, setChartDataList] = useState<any[]>([]);
  // const [marketsDataList, setMarketsDataList] = useState<any[]>([]);
  // const { outcome_prices = [] } = marketsDataList[0]?.question_market || {};
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [currentSelectedItem, setCurrentSelectedItem] = useState<string>("Buy");
  const [selectedButton, setSelectedButton] = useState<"yes" | "no">("yes");
  const [isShowPreBuyModal, setIsShowPreBuyModal] = useState(false);
  const [btnPriceData, setBtnPriceData] = useState({ YesPrice: 0, NoPrice: 0 });
  const [allMarketsPriceData, setAllMarketsPriceData] = useState<any>({});
  // const [isShowViewOrList, setIsShowViewOrList] = useState("list"); // list, view
  const [selectedButtonType, setSelectedButtonType] = useState<"yes" | "no">("yes");

  // 处理按钮状态变化，用于同步 GameListItem 和 OrderCard
  const handleButtonStateChange = useCallback((buttonState: any) => {
    const { marketId, buttonType } = buttonState;
    // 更新选中的市场和按钮类型
    setSelectedItemId(marketId.toString());
    setSelectedButton(buttonType);
  }, []);
  useEffect(() => {
    // 如果没有传入 tagGameId，则不请求数据
    if (!tagGameId) {
      setEventList([]);
      setCurrentEvent({});
      return;
    }

    getSportGamesEventsListData({
      active: true,
      closed: false,
      offset: 0,
      limit: 20,
      tag_id: tagGameId, // 使用传入的 tag_game_id
    })
      .then(res => {
        setEventList(res.data.events);

        // 设置第一个事件作为当前事件
        if (res.data.events && res.data.events.length > 0) {
          setCurrentEvent(res.data.events[0]);
        }
      })
      .catch(error => {
        console.error("Error fetching game events:", error);
        setEventList([]);
      });
  }, [tagGameId]);

  useEffect(() => {
    if (currentEvent?.event_markets) {
      // setChartDataList(sortAndSliceMarkets(currentEvent.event_markets));
      // setMarketsDataList(sortMarketsBylastTradePrice(currentEvent.event_markets));
      setSelectedItemId(currentEvent.event_markets[0]?.question_market?.id);
    }
  }, [currentEvent]);

  // 当 selectedItemId 变化时，从 eventList 中找到对应的事件并设置为 currentEvent
  useEffect(() => {
    if (selectedItemId && eventList.length > 0) {
      const selectedEvent = eventList.find(event =>
        event.event_markets?.some((market: any) => market.question_market?.id === Number(selectedItemId)),
      );
      if (selectedEvent && selectedEvent !== currentEvent) {
        setCurrentEvent(selectedEvent);
      }
    }
  }, [selectedItemId, eventList, currentEvent]);

  return (
    <div className="flex flex-col lg:flex-row gap-2 lg:gap-3 h-screen overflow-hidden">
      {/* 左侧游戏列表区域 - 占据剩余空间，但保证右侧卡片有足够宽度 */}
      <div className="flex-1 lg:max-w-[calc(100%-320px)] h-full overflow-y-auto scrollbar-hide">
        <div className="max-w-4xl mx-auto">
          <GameList
            marketsDataList={eventList}
            selectedItemId={selectedItemId}
            setSelectedItemId={setSelectedItemId}
            selectedButton={selectedButton}
            setSelectedButton={setSelectedButton}
            currentSelectedItem={currentSelectedItem}
            // 传递按钮价格数据更新回调
            onBtnDataUpdate={setBtnPriceData}
            // 传递所有市场价格数据更新回调
            onAllMarketsPriceUpdate={setAllMarketsPriceData}
            // 传递按钮状态变化回调，用于同步 OrderCard
            onButtonStateChange={handleButtonStateChange}
          />
        </div>
      </div>

      {/* 右侧订单卡片区域 - PC端始终显示，移动端隐藏 */}
      <div className="hidden lg:block lg:w-88 lg:min-w-[350px] h-full overflow-y-auto flex-shrink-0">
        {currentEvent?.event_markets ? (
          <div className="sticky top-0 p-0.5 rounded-xl overflow-hidden">
            <OrderCard
              clobApis={clobApis}
              cardType="single-sided"
              key={selectedItemId}
              setSelectedItemId={setSelectedItemId}
              marketsDataList={currentEvent?.event_markets}
              selectedItemId={Number(selectedItemId)}
              selectedButtonType={selectedButtonType}
              setSelectedButtonType={setSelectedButtonType}
              currentSelectedItem={currentSelectedItem}
              setCurrentSelectedItem={setCurrentSelectedItem}
              isShowPreBuyModal={isShowPreBuyModal}
              setIsShowPreBuyModal={setIsShowPreBuyModal}
              btnPriceData={btnPriceData}
              allMarketsPriceData={allMarketsPriceData}
              onButtonStateChange={handleButtonStateChange}
            />
          </div>
        ) : null}
      </div>

      {/* 移动端浮动订单卡片 - 保持不变 */}
      {currentEvent?.event_markets && (
        <div className="lg:hidden fixed bottom-0 left-0 right-0 z-50 bg-white border-t shadow-lg max-h-[50vh] overflow-y-auto">
          <OrderCard
            clobApis={clobApis}
            cardType="single-sided"
            key={selectedItemId}
            setSelectedItemId={setSelectedItemId}
            marketsDataList={currentEvent?.event_markets}
            selectedItemId={Number(selectedItemId)}
            selectedButtonType={selectedButtonType}
            setSelectedButtonType={setSelectedButtonType}
            currentSelectedItem={currentSelectedItem}
            setCurrentSelectedItem={setCurrentSelectedItem}
            isShowPreBuyModal={isShowPreBuyModal}
            setIsShowPreBuyModal={setIsShowPreBuyModal}
            btnPriceData={btnPriceData}
            allMarketsPriceData={allMarketsPriceData}
            onButtonStateChange={handleButtonStateChange}
          />
        </div>
      )}
    </div>
  );
};

export default GamesContent;
