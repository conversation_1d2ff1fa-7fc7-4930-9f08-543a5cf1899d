import React, { useEffect, useState } from "react";
import Image from "next/image";
import SingleSidedButton from "@/components/ui/SingleSidedButton";
import {
  base64DecodeByLanguage,
  formatGameButtonPrice,
  formatLargeAmountWithCommas,
  formatLastTradePrice,
  formatTime12HourClock,
} from "@/utils";
import OrderBooksList from "~~/components/details/marketItem/OrderBooksList";
import { useGlobalState } from "~~/services/store/store";

const GameListItem = (props: any) => {
  const {
    item,
    selectedButton,
    selectedItemId,
    onButtonClick,
    currentSelectedItem,
    eventBtnDataList,
    onButtonStateChange,
  } = props;
  const { current_language } = useGlobalState().nativeCurrency;
  const [currentLanguage, setCurrentLanguage] = useState<string>("en");

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const lng = urlParams.get("lng");
    if (lng) {
      setCurrentLanguage(lng);
    } else {
      setCurrentLanguage(current_language);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [current_language]);

  // 处理多个 questions
  const [questionsData, setQuestionsData] = useState<any[]>([]);

  useEffect(() => {
    if (item.event_markets && item.event_markets.length > 0) {
      const processedQuestions = item.event_markets.map((market: any) => {
        const { id, icon, volumeclob, lasttradeprice, question } = market.question_market;
        return {
          id,
          icon,
          volumeclob,
          lasttradeprice,
          question,
          title: base64DecodeByLanguage(question, currentLanguage),
        };
      });
      setQuestionsData(processedQuestions);
    }
  }, [item.event_markets, currentLanguage]);

  const handleButtonClick = (questionId: string, buttonType: string) => {
    onButtonClick(questionId, buttonType);
  };

  // 处理点击非按钮区域关闭图表
  const handleCardClick = (e: React.MouseEvent) => {
    // 如果点击的是按钮，不处理
    if ((e.target as HTMLElement).closest("button")) {
      return;
    }
    // 如果当前有选中的项目，则取消选中（关闭图表）
    if (selectedItemId) {
      onButtonClick(null, ""); // 传递 null 来取消选中
    }
  };

  // 获取第一个 question 的总交易量用于显示
  const totalVolume = questionsData.length > 0 ? formatLargeAmountWithCommas(questionsData[0].volumeclob) : "0";

  // 格式化结束时间
  const formattedEndTime = item.end_date ? formatTime12HourClock(item.end_date) : "TBD";

  return (
    <div
      className="flex w-full flex-col bg-white hover:bg-gradient-to-br hover:bg-gray-50
                 rounded-xl border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md
                 transition-all duration-200 py-3 sm:py-4 px-3 sm:px-5 cursor-pointer overflow-y-auto"
      onClick={handleCardClick}
    >
      {/* 顶部时间和总交易量 */}
      <div className="flex w-full relative gap-2 text-sm mb-3">
        <div className="flex items-center gap-2 sm:gap-3 max-w-md">
          <div className="font-semibold bg-gradient-to-r from-blue-500 to-blue-600 text-white px-2 sm:px-3 py-1 sm:py-1.5 rounded-lg shadow-sm text-xs whitespace-nowrap">
            {formattedEndTime}
          </div>
          <div className="font-medium text-gray-700 bg-gray-100 px-2 py-1 rounded-md text-xs whitespace-nowrap">
            ${totalVolume} Vol.
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="w-full flex flex-col gap-2 sm:gap-3">
        {questionsData.map((questionData, index) => {
          const isSelected = Number(selectedItemId) === questionData.id;
          // 从 eventBtnDataList 中获取对应市场的按钮数据
          const marketBtnData = eventBtnDataList?.find((data: any) => data.marketId === questionData.id);
          const { YesPrice } = (marketBtnData?.btnData &&
            formatGameButtonPrice(marketBtnData.btnData, currentSelectedItem)) || {
            YesPrice: 0,
          };

          return (
            <div key={questionData.id} className="flex items-center gap-3 sm:gap-4">
              <Image
                className="h-8 w-8 sm:h-10 sm:w-10 object-contain overflow-hidden rounded-sm flex-shrink-0"
                src={questionData.icon || "/image_alt.jpg"}
                alt=""
                width={40}
                height={40}
                priority
              />
              <div className="flex flex-col flex-1 min-w-0">
                <span className="text-sm sm:text-base font-medium truncate leading-tight">{questionData.title}</span>
                <div className="text-xs text-gray-600 mt-0.5">
                  ${formatLargeAmountWithCommas(questionData.volumeclob)} Vol.
                </div>
              </div>
              <div
                onClick={e => {
                  // 阻止事件冒泡，防止触发外部容器的点击事件
                  e.stopPropagation();
                }}
              >
                <SingleSidedButton
                  title={questionData.title}
                  price={YesPrice || "0"}
                  isSelected={isSelected}
                  questionIndex={index}
                  onClick={() => {
                    handleButtonClick(questionData.id, "yes");
                    // 通知外部组件按钮状态变化，用于同步 OrderCard
                    if (onButtonStateChange) {
                      onButtonStateChange({
                        marketId: questionData.id,
                        buttonType: "yes",
                        marketData: questionData,
                      });
                    }
                  }}
                />
              </div>
            </div>
          );
        })}
      </div>

      {/* OrderBooksList - 显示当前选中市场的订单簿 */}
      {selectedItemId && eventBtnDataList && (
        <div className="mt-2 max-h-64 overflow-y-auto">
          {questionsData.map(questionData => {
            const isSelected = Number(selectedItemId) === questionData.id;
            if (!isSelected) return null;

            const marketBtnData = eventBtnDataList?.find((data: any) => data.marketId === questionData.id);
            const chanceNum = formatLastTradePrice(questionData.lasttradeprice);

            // 对于 Game 卡片，始终显示 Yes 的订单簿数据
            const bookData = marketBtnData?.btnData?.yesBookData;

            return (
              <OrderBooksList
                key={questionData.id}
                bookData={bookData}
                chanceNum={chanceNum}
                selectedButtonType={selectedButton}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};

export default GameListItem;
