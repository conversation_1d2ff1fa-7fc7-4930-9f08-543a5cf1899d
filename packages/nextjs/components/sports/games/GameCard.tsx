"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { base64DecodeByLanguage, formatLargeAmountWithCommas } from "@/utils";
import { Button } from "@heroui/react";
import { ChevronRightIcon, StarIcon } from "@heroicons/react/24/outline";
import { useGlobalState } from "~~/services/store/store";

interface GameCardProps {
  eventCardData: any;
  timeStamp: number;
}

const GameCard: React.FC<GameCardProps> = ({ eventCardData, timeStamp }) => {
  const { current_language } = useGlobalState().nativeCurrency;

  const [title, setTitle] = useState("");
  const [gameTime, setGameTime] = useState("");
  const [volume, setVolume] = useState(0);

  useEffect(() => {
    // 解码标题
    const decodedTitle = base64DecodeByLanguage(eventCardData.title);
    setTitle(decodedTitle);

    // 解析比赛时间
    if (eventCardData.end_data) {
      const startDate = new Date(eventCardData.end_data);
      const timeString = startDate.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });
      setGameTime(timeString);
    }

    // 计算交易量
    const totalVolume = eventCardData.volume_24hr || 0;
    setVolume(totalVolume);
  }, [eventCardData, current_language]);

  const handleGameViewClick = () => {
    // 导航到游戏详情页面
    const slug = base64DecodeByLanguage(eventCardData.slug);
    const url = `/event/${slug}?tid=${eventCardData.id}&lng=${current_language}&timeStamp=${timeStamp}`;
    window.location.href = url;
  };

  const handlePinClick = () => {
    // 处理收藏功能
    console.log("Pin game:", eventCardData.id);
  };

  return (
    <div className="group relative flex w-full h-max m-0 p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-100 mb-4">
      {/* 左侧：事件信息 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部信息行 */}
        <div className="flex items-center gap-3 mb-3">
          {/* 比赛时间 */}
          <div className="bg-blue-100 text-blue-600 px-2 py-1 rounded text-sm font-medium">{gameTime || "3:30 AM"}</div>
          {/* 交易量 */}
          <p className="text-sm font-medium text-gray-600">${formatLargeAmountWithCommas(volume)} Vol.</p>
          {/* 收藏按钮 */}
          <Button
            size="sm"
            variant="flat"
            className="bg-gray-100 hover:bg-gray-200 text-blue-600 min-w-8 px-2"
            onPress={handlePinClick}
          >
            <StarIcon className="w-4 h-4" />
          </Button>
        </div>

        {/* 事件标题和图标 */}
        <div className="flex items-center gap-3">
          <div className="relative w-12 h-12">
            <Image
              src={eventCardData.icon || eventCardData.image || "/image_alt.jpg"}
              alt={title}
              fill
              className="object-cover rounded"
              onError={e => {
                e.currentTarget.src = "/image_alt.jpg";
              }}
            />
          </div>
          <div className="flex flex-col flex-1">
            <p className="text-lg font-medium text-gray-900 truncate">{title}</p>
            <p className="text-sm text-gray-600">{eventCardData.event_markets?.length || 0} markets available</p>
          </div>
        </div>
      </div>

      {/* 右侧：队伍选择按钮 */}
      <div className="flex flex-col gap-2 ml-4 min-w-[200px]">
        {eventCardData.event_markets && eventCardData.event_markets.length > 0 ? (
          eventCardData.event_markets.slice(0, 2).map((market: any, index: number) => {
            const marketData = market.question_market;
            const teamName = base64DecodeByLanguage(marketData.description || marketData.question);

            return (
              <Button
                key={marketData.id}
                className="w-full bg-blue-50 hover:bg-blue-100 text-blue-600 border border-blue-200 h-12"
                // onPress={() => handleTeamClick(marketData, teamName, index === 0 ? "yes" : "no")}
              >
                <div className="flex justify-between items-center w-full">
                  <span className="text-sm font-medium truncate">{teamName}</span>
                  <span className="text-sm font-semibold ml-2">{index === 0 ? "39¢" : "63¢"}</span>
                </div>
              </Button>
            );
          })
        ) : (
          <Button
            className="w-full bg-gray-100 text-gray-600 border border-gray-200 h-12"
            onPress={handleGameViewClick}
          >
            View Details
          </Button>
        )}

        {/* Game view 按钮 */}
        <Button
          size="sm"
          variant="flat"
          className="bg-gray-100 hover:bg-gray-200 text-gray-700 mt-2"
          onPress={handleGameViewClick}
          endContent={<ChevronRightIcon className="w-4 h-4" />}
        >
          Game view
        </Button>
      </div>
    </div>
  );
};

export default GameCard;
