import React, { useCallback, useEffect, useMemo } from "react";
import GameListItem from "./GameListltem";
import useGameBtnList from "@/hooks/scaffold-pdone/useGameBtnList";
import { useWebSocketBooksList } from "@/hooks/useWebSocket";
import { formatGameButtonPrice } from "@/utils";

// import useWebSocketBooksList from "@/hooks/useWebSocket";

const GameList = (props: any) => {
  const {
    marketsDataList,
    currentSelectedItem,
    selectedItemId,
    setSelectedItemId,
    selectedButton,
    setSelectedButton,
    onBtnDataUpdate, // 新增：按钮数据更新回调
    onAllMarketsPriceUpdate, // 新增：所有市场价格数据更新回调
    onButtonStateChange, // 新增：按钮状态变化回调
  } = props;

  const handleButtonClick = useCallback(
    (itemId: string | null, buttonType: string) => {
      setSelectedItemId(itemId);
      setSelectedButton(buttonType);
    },
    [setSelectedItemId, setSelectedButton],
  );

  // 对于 Game 卡片，获取所有市场的按钮数据
  const { btnData, btnDataList, setBtnDataList } = useGameBtnList(marketsDataList, selectedButton, selectedItemId);

  // websocket 图表数据 - 适配 Game 卡片的多市场结构
  const assetsIds = useMemo(() => {
    return marketsDataList
      .flatMap(
        (event: any) =>
          event.event_markets?.map((market: any) => market.question_market.clob_token_ids?.[0] || "") || [],
      )
      .filter(Boolean);
  }, [marketsDataList]);

  // 启用 WebSocket 连接获取实时数据
  useWebSocketBooksList(`${process.env.NEXT_PUBLIC_WSS}/ws`, assetsIds, setBtnDataList);

  // 同步按钮数据到 OrderCard
  useEffect(() => {
    if (btnData && onBtnDataUpdate) {
      // 对于 Game 卡片，只计算 Yes 的价格
      const { YesPrice } = formatGameButtonPrice(btnData, currentSelectedItem);

      // 更新 OrderCard 的按钮价格数据 - 只传递 Yes 价格
      onBtnDataUpdate({
        YesPrice,
        NoPrice: 0, // Game 卡片不需要 No 价格
      });
    }
  }, [btnData, onBtnDataUpdate, currentSelectedItem]);

  // 计算所有市场的价格数据并传递给父组件
  useEffect(() => {
    if (btnDataList.length > 0 && onAllMarketsPriceUpdate) {
      const allPrices: any = {};

      btnDataList.forEach((marketData: any) => {
        const { marketId } = marketData;
        const { YesPrice } = formatGameButtonPrice(marketData, currentSelectedItem);
        allPrices[marketId] = { YesPrice, NoPrice: 0 };
      });

      onAllMarketsPriceUpdate(allPrices);
    }
  }, [btnDataList, currentSelectedItem, onAllMarketsPriceUpdate]);

  return (
    <div className="w-full h-screen flex flex-col gap-2 overflow-y-hidde">
      {marketsDataList.map((item: any) => {
        // 对于 Game 卡片，为每个事件的所有市场提供按钮数据
        const eventBtnDataList =
          item.event_markets?.map((market: any) => {
            const marketBtnData = btnDataList.find((data: any) => data.marketId === market.question_market.id);
            return {
              marketId: market.question_market.id,
              btnData: marketBtnData,
            };
          }) || [];

        return (
          <GameListItem
            item={item}
            key={item.id}
            selectedButton={selectedButton}
            selectedItemId={selectedItemId}
            onButtonClick={handleButtonClick}
            // 传递所有市场的按钮数据，而不是单个匹配的数据
            eventBtnDataList={eventBtnDataList}
            btnData={btnData} // 保留原有的 btnData 作为兼容
            isExpanded={"orderbook"}
            currentSelectedItem={currentSelectedItem}
            onButtonStateChange={onButtonStateChange} // 传递按钮状态变化回调
          />
        );
      })}
    </div>
  );
};

export default GameList;
