import React, { useEffect, useState } from "react";
import OrderItem from "./Items/Orderltem";
import { cancelOrders, getInformationListData, getOpenOrderData } from "@/api/order";
import ConfirmModal from "@/components/other/ConfirmModal";
import NotificationCard from "@/components/other/NotificationCard";
import useFrozen from "@/hooks/useFrozen";
import { usePagination } from "@/hooks/usePagination";
import { useGlobalState } from "@/services/store/store";
import { Divider } from "@heroui/divider";
import { Spinner } from "@heroui/react";
import { useTranslation } from "react-i18next";

const OpenOrderList = (props: any) => {
  const { clobApis } = props;
  const { walletType } = useGlobalState();
  const { refreshFrozenValue } = useFrozen(clobApis);
  const [informationList, setInformationList] = useState([]);
  const [showNotification, setShowNotification] = useState(false);
  const [currentCancelOrderQusetion, setCurrentCancelOrderQusetion] = useState("");
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [currentOrderId, setCurrentOrderId] = useState<string | null>(null);

  const { t } = useTranslation();

  const {
    data: openOrderListData,
    loading,
    hasMore,
    isInitialLoad,
    observerRef,
    refetch,
  } = usePagination({
    fetchFunction: getOpenOrderData,
    limit: 10,
    initialParams: {},
    fetchParams: {
      creds: clobApis,
      walletType: walletType,
    },
    dependencies: [clobApis, walletType],
  });

  const currentOrderData = openOrderListData;
  const currentLoading = loading;

  useEffect(() => {
    if (currentOrderData.length > 0) {
      const fetchData = async () => {
        try {
          const markets = currentOrderData.map(item => item.market);
          const uniqueMarkets = [...new Set(markets)];

          const informationListData = await getInformationListData(uniqueMarkets);
          setInformationList(informationListData.data.question_market);
        } catch (error) {
          console.error("Error fetching information data:", error);
        }
      };

      fetchData();
    } else {
      setInformationList([]);
    }
  }, [currentOrderData]);

  const handleCancelOrder = async () => {
    if (currentOrderId) {
      try {
        const isLastOrder = currentOrderData.length === 1;
        await cancelOrders(clobApis, currentOrderId, setShowNotification, walletType);

        setShowNotification(true);
        setShowConfirmModal(false);

        const refreshSequence = async () => {
          // 1. Refresh order list first
          refetch();

          // 2. Wait for a while to ensure data synchronization
          await new Promise(resolve => setTimeout(resolve, 500));

          // 3. Choose refresh method based on whether it's the last order
          if (isLastOrder) {
            await refreshFrozenValue(clobApis, true);
          } else {
            await refreshFrozenValue(clobApis);
          }
        };

        setTimeout(refreshSequence, 1000);
      } catch (error) {
        console.error("Error cancelling order:", error);
        setShowConfirmModal(false);
      }
    }
  };

  return (
    <div className="w-full flex flex-col mt-2">
      <div className="hidden sm:flex w-full items-center justify-start font-semibold text-sm my-1 gap-2">
        <span className="w-[35%]">{t("Portfolio_Tabel_MARKET")}</span>
        <span className="w-[8%]">{t("Portfolio_Tabel_Side")}</span>
        <span className="w-[8%]">{t("Portfolio_Tabel_Position")}</span>
        <span className="w-[8%]">{t("Portfolio_Tabel_Price")}</span>
        <span className="w-[8%]">{t("Portfolio_Tabel_Filled")}</span>
        <span className="w-[8%]">{t("Portfolio_Tabel_Total")}</span>
        <span className="w-[20%]">{t("Portfolio_Tabel_Expiration")}</span>
        <span className="w-[2%]"></span>
      </div>

      <Divider className="my-2 bg-gray-200" />

      {currentOrderData?.map((item, index) => (
        <div key={`${item.id}_${index}`}>
          <OrderItem
            item={item}
            informationList={informationList}
            setCurrentCancelOrderQusetion={setCurrentCancelOrderQusetion}
            onCancel={() => {
              setCurrentOrderId(item.id);
              setShowConfirmModal(true);
            }}
          />
          <Divider className="my-2 bg-gray-200" />
        </div>
      ))}

      <div ref={observerRef} className="w-full py-4 flex justify-center">
        {currentLoading && (
          <div className="flex items-center gap-2">
            <Spinner size="sm" />
            <span className="text-sm text-gray-500">{t("Portfolio_Loading")}</span>
          </div>
        )}
        {!hasMore && !isInitialLoad && currentOrderData.length > 0 && !currentLoading && (
          <span className="text-sm text-gray-400">{t("Portfolio_No_More_Data")}</span>
        )}
        {!currentLoading && currentOrderData.length === 0 && !isInitialLoad && (
          <span className="text-sm text-gray-400">{t("Portfolio_No_Data")}</span>
        )}
      </div>

      {showNotification && (
        <NotificationCard
          title={"Orders cancelled"}
          content={currentCancelOrderQusetion}
          footer="orders were cancelled"
          setShowNotification={setShowNotification}
        />
      )}

      <ConfirmModal
        visible={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleCancelOrder}
      />
    </div>
  );
};

export default OpenOrderList;
