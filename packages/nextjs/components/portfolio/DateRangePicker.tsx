import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>vide<PERSON> } from "@heroui/react";
import { CalendarDays } from "lucide-react";
import { DayPicker } from "react-day-picker";
import "react-day-picker/dist/style.css";
import { useTranslation } from "react-i18next";

export default function DateRangePicker(props: any) {
  const { fetchActivityListData, historyParams } = props;
  const [visible, setVisible] = useState(false);
  const [range, setRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  });
  const { t } = useTranslation();

  const predefinedRanges = [
    { label: "Portfolio_Date_Filter_Today", range: { from: new Date(), to: new Date() } },
    {
      label: "Portfolio_Date_Filter_Yesterday",
      range: { from: new Date(Date.now() - 86400000), to: new Date(Date.now() - 86400000) },
    },
    { label: "Portfolio_Date_Filter_Last_Week", range: { from: new Date(Date.now() - 7 * 86400000), to: new Date() } },
    {
      label: "Portfolio_Date_Filter_Last_Month",
      range: { from: new Date(new Date().setMonth(new Date().getMonth() - 1)), to: new Date() },
    },
    {
      label: "Portfolio_Date_Filter_Last_3_Months",
      range: { from: new Date(new Date().setMonth(new Date().getMonth() - 3)), to: new Date() },
    },
    {
      label: "Portfolio_Date_Filter_Year_to_Date",
      range: { from: new Date(new Date().getFullYear(), 0, 1), to: new Date() },
    },
    {
      label: "Portfolio_Date_Filter_Last_Year",
      range: { from: new Date(new Date().getFullYear() - 1, 0, 1), to: new Date(new Date().getFullYear() - 1, 11, 31) },
    },
    { label: "Portfolio_Date_Filter_All", range: { from: undefined, to: undefined } },
  ];

  const handleRangeSelect = (selectedRange: any) => {
    if (selectedRange?.from === undefined && selectedRange?.to === undefined) {
      setRange({ from: undefined, to: undefined });
    } else if (selectedRange?.from && selectedRange?.to) {
      setRange(selectedRange);
    } else {
      setRange({ from: new Date(), to: new Date() });
    }
  };

  const handleDayPickerSelect = (selectedRange: any) => {
    setRange(selectedRange || { from: undefined, to: undefined });
  };

  const onClickCancel = () => {
    setVisible(false);
    setRange({ from: undefined, to: undefined });
  };

  const onClickSetDates = () => {
    const { from, to } = range;

    const params = { ...historyParams };

    if (from && to) {
      const start = Math.floor(from.getTime() / 1000);
      const end = Math.floor(to.getTime() / 1000);
      params.start = start;
      params.end = end;
    } else {
      delete params.start;
      delete params.end;
    }
    setVisible(false);
    fetchActivityListData(params);
  };

  return (
    <div>
      <Button size="lg" variant="ghost" className="border flex-center px-2" onClick={() => setVisible(!visible)}>
        <CalendarDays className="size-5" />
        {t("Portfolio_Date_Filter")}
      </Button>
      {visible && (
        <div className="flex flex-col absolute mt-4 bg-white border rounded-lg z-10">
          <div className="flex h-[260px] w-[410px]">
            <div className="w-[150px] pl-4 py-4">
              {predefinedRanges.map(range => (
                <div key={range.label} className="mb-2 text-sm text-gray-600">
                  <div
                    className="hover:bg-slate-200 cursor-pointer text-nowrap"
                    onClick={() => handleRangeSelect(range.range)}
                  >
                    {t(range.label)}
                  </div>
                </div>
              ))}
            </div>

            <Divider orientation="vertical" className="h-auto mx-4" />

            <DayPicker
              className="w-full h-full p-2 scale-80 origin-top-left"
              mode="range"
              selected={range}
              onSelect={handleDayPickerSelect}
            />
          </div>
          <Divider className="w-auto mb-2" />
          <div className="flex items-center justify-end gap-2 text-sm mb-2 mr-2">
            <div
              className="border px-4 py-2 border-blue-600 text-blue-600 rounded-xl cursor-pointer"
              onClick={onClickCancel}
            >
              Cancel
            </div>
            <div
              className="border px-4 py-2 text-white bg-blue-600 rounded-xl cursor-pointer"
              onClick={onClickSetDates}
            >
              Set Dates
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
