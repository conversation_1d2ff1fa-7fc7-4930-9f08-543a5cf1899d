import React, { useEffect, useMemo, useState } from "react";
import PositionItem from "./Items/Positionltem";
import { getInformationListData, getPositionsListDataInPofolio } from "@/api/order";
import { usePagination } from "@/hooks/usePagination";
import { useGlobalState } from "@/services/store/store";
import { processNumber } from "@/utils";
import { Divider } from "@heroui/divider";
import { Spinner, Switch } from "@heroui/react";
import { useTranslation } from "react-i18next";

interface MarketInfo {
  event_markets?: Array<{
    condition_id: string;
  }>;
}
const PositionsList = (props: any) => {
  const { clobApis } = props;
  const { walletType } = useGlobalState();
  const { t } = useTranslation();
  const [showLessThanOneDollar, setShowLessThanOneDollar] = useState<boolean>(true);
  const [informationList, setInformationList] = useState<MarketInfo[]>([]);

  const {
    data: positionsListData,
    loading,
    hasMore,
    isInitialLoad,
    observerRef,
  } = usePagination({
    fetchFunction: getPositionsListDataInPofolio,
    limit: 10,
    initialParams: {},
    fetchParams: {
      creds: clobApis,
      walletType: walletType,
    },
    dependencies: [clobApis, walletType],
  });

  // 获取市场信息
  useEffect(() => {
    if (positionsListData.length > 0) {
      const fetchInformationData = async () => {
        try {
          const markets = positionsListData.map(item => item.conditionId?.String).filter(id => id); // 过滤掉空值
          const uniqueMarkets = [...new Set(markets)];

          if (uniqueMarkets.length > 0) {
            const informationListData = await getInformationListData(uniqueMarkets);
            setInformationList(informationListData.data.question_market);
          }
        } catch (error) {
          console.error("Error fetching information data:", error);
        }
      };

      fetchInformationData();
    }
  }, [positionsListData]);

  // 合并持仓数据和市场信息
  const enrichedPositionsData = useMemo(() => {
    if (!positionsListData.length || !informationList.length) {
      return positionsListData;
    }

    return positionsListData.map(positionItem => {
      const conditionId = positionItem.conditionId?.String;
      const matchedInfo = informationList.find(
        info =>
          info.event_markets && info.event_markets.length > 0 && info.event_markets[0].condition_id === conditionId,
      );

      return {
        ...positionItem,
        marketInfo: matchedInfo || null,
      };
    });
  }, [positionsListData, informationList]);

  const filteredPositionsListData = showLessThanOneDollar
    ? enrichedPositionsData
    : enrichedPositionsData.filter(item => {
        const initialValue = item.initialValue ? parseFloat(item.initialValue) : 0;
        const bet = processNumber(initialValue, 6);
        return Number(bet) >= 1;
      });

  return (
    <div className="w-full flex flex-col mt-2">
      <div className="flex items-center justify-between">
        {/* placeholder */}
        <div />

        <div className="flex flex-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-semibold">{t("Portfolio_Filter_Hidden_Less_Dollar")}</span>
            <Switch checked={showLessThanOneDollar} onChange={() => setShowLessThanOneDollar(prev => !prev)} />
          </div>
        </div>
      </div>

      <div className="hidden sm:flex w-full items-center justify-between tracking-widest text-xs text-gray-400 font-sm my-2">
        <span className="w-[46%]">{t("Portfolio_Tabel_MARKET")}</span>
        <span className="w-[10%]">{t("Portfolio_Tabel_LATEST")}</span>
        <span className="w-[10%]">{t("Portfolio_Tabel_BET")}</span>
        <span className="w-[12%]">{t("Portfolio_Tabel_TO_WIN")}</span>
        <span className="w-[20%]"></span>
      </div>

      <Divider className="my-2 bg-gray-200" />

      {filteredPositionsListData.map((item, index) => (
        <React.Fragment key={`${item.id?.Int64 || index}_${index}`}>
          {item && <PositionItem item={item} />}
        </React.Fragment>
      ))}

      {/* 加载更多触发器 */}
      <div ref={observerRef} className="w-full py-4 flex justify-center">
        {loading && (
          <div className="flex items-center gap-2">
            <Spinner size="sm" />
            <span className="text-sm text-gray-500">{t("Portfolio_Loading")}</span>
          </div>
        )}
        {!hasMore && !isInitialLoad && filteredPositionsListData.length > 0 && (
          <span className="text-sm text-gray-400">{t("Portfolio_No_More_Data")}</span>
        )}
        {!loading && filteredPositionsListData.length === 0 && !isInitialLoad && (
          <span className="text-sm text-gray-400">{t("Portfolio_No_Data")}</span>
        )}
      </div>
    </div>
  );
};

export default PositionsList;
