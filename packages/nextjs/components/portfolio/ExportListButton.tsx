import React from "react";
import { Button } from "@heroui/react";
import { Download } from "lucide-react";
import { useTranslation } from "react-i18next";

const convertToCSV = (data: any[]) => {
  const headers = ["marketName", "action", "usdcAmount", "tokenAmount", "tokenName", "timestamp", "hash"];
  const csvRows = [headers.join(",")];

  for (const row of data) {
    const values = [row.title, row.side, row.usdcSize, row.size, row.outcome, row.timestamp, row.transactionHash];
    csvRows.push(values.join(","));
  }

  return csvRows.join("\n");
};

const downloadCSV = (data: any[]) => {
  const csvData = convertToCSV(data);
  const blob = new Blob([csvData], { type: "text/csv" });
  const url = window.URL.createObjectURL(blob);
  const now = new Date();
  const formattedDate = now
    .toLocaleString("en-GB", {
      timeZone: "Asia/Shanghai",
      weekday: "short",
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
      timeZoneName: "long",
    })
    .replace(/[:]/g, "_")
    .replace(/,/g, "");
  const fileName = `Polymarket-Transaction-History-${formattedDate}.csv`;

  const a = document.createElement("a");
  a.setAttribute("hidden", "");
  a.setAttribute("href", url);
  a.setAttribute("download", fileName);
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

export default function ExportListButton(props: any) {
  const { data } = props;
  const { t } = useTranslation();

  return (
    <div>
      <Button
        size="lg"
        variant="ghost"
        className="flex-center border border-blue-600 text-blue-600 px-4"
        onClick={() => downloadCSV(data)}
      >
        <Download color="#023ef2" className="size-4" />
        {t("Portfolio_Drop_Export")}
      </Button>
    </div>
  );
}
