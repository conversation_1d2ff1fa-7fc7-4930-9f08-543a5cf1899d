import React, { useEffect, useState } from "react";
import { getPositionsListDataInPofolio } from "@/api/order";
import { useClobApiOptimized } from "@/hooks/secret/useClobApiOptimized";
import useFrozen from "@/hooks/useFrozen";
import { useGlobalBalance } from "@/hooks/useGlobalBalance";
import { useUserAddress } from "@/hooks/useUserAddress";
import { getProviderAndSigner, processNumber } from "@/utils";
import { animated, useSpring } from "@react-spring/web";
import { DollarSign, RotateCcw, TrendingUp, Wallet } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useGlobalState } from "~~/services/store/store";

const glassCard =
  "relative flex-1 min-w-[220px] max-w-[400px] rounded-2xl p-6 m-2 flex flex-col items-center justify-center overflow-hidden border border-gray-200 bg-white";
const iconStyle = "mb-2 text-4xl";

const HeaderContent = (props: any) => {
  const { proxyWallet, clobApis } = props;
  const { t } = useTranslation();
  const { frozenValue } = useFrozen(clobApis);

  const { clobApis: optimizedClobApis } = useClobApiOptimized();
  const { walletType } = useGlobalState();
  const { address } = useUserAddress();

  // HeaderContent 作为从属组件，消费 RainbowKit 提供的余额数据
  const {
    balance,
    isLoading: balanceLoading,
    refreshBalance: reloadBalance,
  } = useGlobalBalance(proxyWallet, {
    enableBlockWatch: false, // 📊 从属组件不启用区块监听
    enableAutoRefresh: false, // 📊 从属组件不启用定时刷新
    skipInitialFetch: true, // 📊 依赖主组件获取数据
  });

  const [positionsListData, setPositionsListData] = useState<any[]>([]);

  const [portfolioValueNum, setPortfolioValueNum] = useState(0);
  const [cashNum, setCashNum] = useState(0);
  const [balanceNum, setBalanceNum] = useState(0);

  const portfolioSpring = useSpring({ number: portfolioValueNum, config: { duration: 400 } });
  const cashSpring = useSpring({ number: cashNum, config: { duration: 400 } });
  const balanceSpring = useSpring({ number: balanceNum, config: { duration: 400 } });

  useEffect(() => {
    const fetchData = async () => {
      // Check all required conditions
      if (!address || !optimizedClobApis || !walletType) {
        setPositionsListData([]);
        return;
      }

      try {
        // Check if signer is available before making the API call
        const signer = await getProviderAndSigner(walletType);
        if (!signer) {
          setPositionsListData([]);
          return;
        }

        const res = await getPositionsListDataInPofolio(optimizedClobApis, walletType, {
          offset: 0,
          limit: parseInt(process.env.NEXT_PUBLIC_BULK_DATA_LIMIT || "99999"),
        });

        if (res?.data?.data && Array.isArray(res.data.data)) {
          const normalizedPositions = res.data.data.map((position: any) => ({
            asset: position.asset?.String || position.asset,
            conditionId: position.conditionId?.String || position.conditionId,
            avgprice: position.avgPrice,
            ...position,
          }));

          setPositionsListData(normalizedPositions);
        } else {
          setPositionsListData([]);
        }
      } catch (error) {
        console.error("Error fetching positions:", error);
        setPositionsListData([]);
      }
    };

    fetchData();
  }, [optimizedClobApis, walletType, address]);

  useEffect(() => {
    let num = 0;
    if (positionsListData?.length) {
      positionsListData.forEach(item => {
        const avgPrice = Number(item.avgprice || item.avgPrice) || 0;
        const size = Number(item.size) || 0;
        const bet = Number(processNumber(avgPrice * size, 18));
        num += bet;
      });
    }
    setPortfolioValueNum(num);
  }, [positionsListData]);

  useEffect(() => {
    if (
      frozenValue !== undefined &&
      frozenValue !== null &&
      balance !== null &&
      balance !== undefined &&
      !balanceLoading
    ) {
      const balanceNum = Number(balance);
      const frozenNum = Number(frozenValue);

      const availableCash = Math.max(0, balanceNum - frozenNum);
      const formattedCash = Math.round(availableCash * 100) / 100;

      setCashNum(formattedCash);
    } else {
      setCashNum(0);
    }
  }, [frozenValue, balance, balanceLoading]);

  useEffect(() => {
    if (!balanceLoading && balance !== null) {
      setBalanceNum(Number(balance));
    } else {
      setBalanceNum(0);
    }
  }, [balance, balanceLoading]);

  const ReloadIcon = ({ onClick, loading }: { onClick: () => void; loading: boolean }) => (
    <div className="cursor-pointer hover:bg-gray-100 rounded-full p-2 transition-colors" onClick={onClick}>
      <RotateCcw className={`text-4xl text-gray-500 ${loading ? "animate-spin" : "hover:text-gray-700"}`} />
    </div>
  );

  const shouldShowLoadingState = balanceLoading;
  const shouldShowErrorState = !balanceLoading && balance === null;

  return (
    <div className="w-full flex flex-col md:flex-row justify-center items-stretch gap-4 py-2">
      {/* Portfolio */}
      <div className={glassCard}>
        <TrendingUp className={iconStyle + " text-purple-500"} />
        <div className="text-xl font-bold tracking-wide mb-1">{t("Normal_Portfolio")}</div>
        <div className="text-4xl font-extrabold text-purple-600">
          $
          <animated.span>
            {portfolioSpring.number.to(n =>
              n.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
            )}
          </animated.span>
        </div>
      </div>

      {/* Cash */}
      <div className={glassCard}>
        <DollarSign className={iconStyle + " text-green-500"} />
        <div className="text-xl font-bold tracking-wide mb-1">{t("Normal_Cash")}</div>

        {shouldShowErrorState ? (
          <div className="flex flex-col items-center">
            <ReloadIcon onClick={reloadBalance} loading={balanceLoading} />
            <div className="text-sm text-red-500 font-semibold">
              {t("portfolio.click_to_reload", "Click to reload")}
            </div>
          </div>
        ) : shouldShowLoadingState ? (
          <div className="flex flex-col items-center">
            <RotateCcw className="text-4xl text-gray-400 animate-spin" />
          </div>
        ) : (
          <>
            <div className="text-4xl font-extrabold text-green-600">
              $
              <animated.span>
                {cashSpring.number.to(n =>
                  n.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
                )}
              </animated.span>
            </div>
            {frozenValue > 0 && (
              <div className="text-xs text-gray-500 mt-1">
                {t("portfolio.frozen", "Frozen")}: ${Number(frozenValue).toFixed(2)}
              </div>
            )}
          </>
        )}
      </div>

      {/* Balance */}
      <div className={glassCard}>
        <Wallet className={iconStyle + " text-blue-500"} />
        <div className="text-xl font-bold tracking-wide mb-1">{t("Normal_Balance")}</div>

        {shouldShowErrorState ? (
          <div className="flex flex-col items-center">
            <ReloadIcon onClick={reloadBalance} loading={balanceLoading} />
            <div className="text-sm text-red-500 font-semibold">
              {t("portfolio.click_to_reload", "Click to reload")}
            </div>
          </div>
        ) : shouldShowLoadingState ? (
          <div className="flex flex-col items-center">
            <RotateCcw className="text-4xl text-gray-400 animate-spin" />
          </div>
        ) : (
          <div className="text-4xl font-extrabold text-blue-600">
            $
            <animated.span>
              {balanceSpring.number.to(n =>
                n.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
              )}
            </animated.span>
          </div>
        )}
      </div>
    </div>
  );
};

export default HeaderContent;
