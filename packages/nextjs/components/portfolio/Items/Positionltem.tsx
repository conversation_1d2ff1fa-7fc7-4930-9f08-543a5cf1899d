import React, { useEffect, useState } from "react";
import Image from "next/image";
import { base64DecodeByEn, base64DecodeByLanguage, getCurrentTimestamp, processNumber } from "@/utils";
import { Divider } from "@heroui/divider";
import { <PERSON><PERSON>, <PERSON>, Link } from "@heroui/react";
import { useTranslation } from "react-i18next";
import { useGlobalState } from "~~/services/store/store";

const PositionItem = (props: any) => {
  const { item } = props;
  const { t } = useTranslation();
  const [title, setTitle] = useState<string>("");
  const [slug, setSlug] = useState<string>("");
  const [defaultConditionId, setDefaultConditionId] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const { current_language } = useGlobalState().nativeCurrency;

  const { asset, conditionId, size, curPrice, title: titleData, slug: slugData, marketInfo, avgPrice } = item || {};

  const timeStamp = getCurrentTimestamp();

  const assetId = asset?.String || "";
  const condition = conditionId?.String || "";
  const curPriceValue = curPrice ? parseFloat(curPrice) : 0;

  const bet = avgPrice && size ? processNumber(avgPrice * size, 18) : "0";
  const numberAvgPrice = avgPrice ? Number(Math.round(avgPrice * 100).toFixed(1)) : 0;
  const shares = size ? Number(processNumber(size, 18)) : 0;
  const numberCurrentPrice = (curPriceValue * 100).toFixed(1);
  const toWin = Math.floor(shares);

  const outcomeText = marketInfo?.clob_token_ids
    ? marketInfo.clob_token_ids.indexOf(assetId) === 0
      ? "Yes"
      : marketInfo.clob_token_ids.indexOf(assetId) === 1
      ? "No"
      : "Unknown"
    : "Unknown";

  const chipBaseClass = outcomeText === "Yes" ? "bg-green-100 p-1" : "bg-red-100 p-1";
  const chipContentClass = outcomeText === "Yes" ? "text-green-600 font-semibold" : "text-red-600 font-semibold";

  useEffect(() => {
    if (marketInfo?.question) {
      setTitle(base64DecodeByLanguage(marketInfo.question));
    } else if (titleData?.String && titleData.Valid) {
      setTitle(base64DecodeByLanguage(titleData.String));
    }

    if (marketInfo?.event_markets?.[0]?.event?.slug) {
      const newSlug = base64DecodeByEn(marketInfo.event_markets[0].event.slug);
      setSlug(newSlug + "-" + current_language);
    } else if (slugData?.String && slugData.Valid) {
      const newSlug = base64DecodeByEn(slugData.String);
      setSlug(newSlug + "-" + current_language);
    }

    if (condition) {
      setDefaultConditionId(condition);
    }
  }, [current_language, titleData, slugData, condition, marketInfo]);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  if (!assetId || !condition) {
    return null;
  }

  const event_id = marketInfo?.event_markets?.[0]?.event_id;
  const question_id = marketInfo?.id;
  const outcome_prices = marketInfo?.outcome_prices;

  const iconUrl = marketInfo?.icon || "/image_alt.jpg";
  const canClaim = Array.isArray(outcome_prices) && outcome_prices.length > 1;

  return (
    <div className="w-full flex flex-col text-sm">
      <div className="flex items-center gap-2" onClick={toggleExpand}>
        <div className="flex w-full sm:w-[46%] items-center flex-row">
          <Image
            className="h-10 w-10 object-cover rounded-md"
            src={iconUrl}
            alt="/image_alt.jpg"
            width={40}
            height={40}
          />
          <div className="w-full flex flex-col justify-start ml-4 gap-2">
            <Link
              href={`/event/${slug}?tid=${event_id}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}&qid=${question_id}`}
              className="text-sm font-semibold text-inherit"
            >
              <span
                className="font-semibold hover:underline cursor-pointer w-full sm:w-auto"
                style={{
                  display: "-webkit-box",
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: "vertical",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                }}
              >
                {title || "undefined"}
              </span>
            </Link>
            <div className="flex items-center gap-2 text-xs">
              {outcomeText && outcomeText !== "Unknown" && numberAvgPrice >= 0 && (
                <Chip
                  size="sm"
                  radius="sm"
                  color="success"
                  classNames={{
                    base: chipBaseClass,
                    content: chipContentClass,
                  }}
                >
                  <div className="flex items-center gap-1">
                    <div>{outcomeText}</div>
                    <div>{numberAvgPrice}¢</div>
                  </div>
                </Chip>
              )}

              {numberAvgPrice < 0 && (
                <Chip
                  size="sm"
                  radius="sm"
                  color="success"
                  classNames={{
                    base: "bg-blue-100 p-1",
                    content: "text-blue-600 font-semibold",
                  }}
                >
                  <div className="flex items-center gap-1">
                    <div>{t("Portfolio_Profit")}</div>
                    <div>{Math.abs(numberAvgPrice)}¢/share</div>
                  </div>
                </Chip>
              )}

              <div className="text-gray-600">{shares} shares</div>
              <div className="sm:hidden">{isExpanded ? "▲" : "▼"}</div>
            </div>
          </div>
        </div>

        <div className="hidden sm:flex w-[10%] font-sans">{numberCurrentPrice}¢</div>
        <div className="hidden sm:flex w-[10%]">${bet}</div>
        <div className="hidden sm:flex w-[12%]">${toWin}</div>

        <div className="hidden sm:flex w-full sm:w-[20%] gap-2 justify-end">
          <div className="flex w-full gap-2">
            <Link
              href={`/event/${slug}?tid=${event_id}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}&qid=${question_id}`}
              className="text-sm font-semibold text-inherit"
            >
              <Button
                className={`flex-1 text-white font-medium rounded-md ${canClaim ? "bg-green-600" : "bg-blue-600"}`}
              >
                {canClaim ? "Claim" : "Trade"}
              </Button>
            </Link>

            <Button className="flex-1 font-medium rounded-md" isDisabled>
              Share
            </Button>
          </div>
        </div>
      </div>

      {isExpanded && (
        <div className="flex w-full gap-2 mt-2 sm:hidden">
          <Button className={`flex-1 text-white font-medium rounded-md ${canClaim ? "bg-green-600" : "bg-blue-600"}`}>
            <Link
              href={`/event/${slug}?tid=${event_id}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}&qid=${question_id}`}
              className="text-sm font-semibold text-inherit"
            >
              {canClaim ? "Claim" : "Trade"}
            </Link>
          </Button>
          <Button className="flex-1 font-medium rounded-md">Share</Button>
        </div>
      )}

      <Divider className="my-2 bg-gray-200" />
    </div>
  );
};

export default PositionItem;
