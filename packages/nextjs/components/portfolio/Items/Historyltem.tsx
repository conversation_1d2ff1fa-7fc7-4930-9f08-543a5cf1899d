import React, { useEffect, useMemo, useState } from "react";
import Image from "next/image";
import {
  base64DecodeByEn,
  base64DecodeByLanguage,
  calculateOrderFilled,
  calculateTimeToNow,
  getCurrentTimestamp,
} from "@/utils";
import { Divider } from "@heroui/divider";
import { Chip, Link } from "@heroui/react";
import { useGlobalState } from "~~/services/store/store";

interface HistoryItemProps {
  item: any;
  marketInfo: any;
}

const HistoryItem: React.FC<HistoryItemProps> = ({ item, marketInfo }) => {
  const { current_language } = useGlobalState().nativeCurrency;

  const { status, original_size, price, side, asset_id, created_at, size_current } = item;

  const [currentTitle, setCurrentTitle] = useState<string>("");
  const [currentSlug, setCurrentSlug] = useState<string | null>(null);
  const [defaultConditionId, setDefaultConditionId] = useState<string | null>(null);

  const currentPrice = useMemo(() => Math.round(parseFloat(price) * 100), [price]);

  const currentValue = useMemo(() => {
    const sizeCurrentNum = parseFloat(size_current);
    const filledAmount =
      side === "BUY" ? Math.round(sizeCurrentNum / parseFloat(price) / 10 ** 6) : Math.round(sizeCurrentNum / 10 ** 6);
    const priceNum = parseFloat(price);

    return (filledAmount * priceNum).toFixed(2);
  }, [size_current, side, price]);

  const timeStamp = getCurrentTimestamp();
  const currentFilled = calculateOrderFilled(side, size_current, original_size, price); // histroy 第二个参数需要用 size_current 算

  const question_id = marketInfo?.id;
  const currentTimeString = useMemo(() => calculateTimeToNow(created_at, true), [created_at]);

  useEffect(() => {
    if (marketInfo) {
      if (marketInfo.question) {
        setCurrentTitle(base64DecodeByLanguage(marketInfo.question));
      }
      if (marketInfo.event_markets?.[0]?.event?.slug) {
        const newSlug = base64DecodeByEn(marketInfo.event_markets[0].event.slug);
        setCurrentSlug(newSlug + "-" + current_language);
      }
      if (marketInfo.event_markets?.[0]?.condition_id) {
        setDefaultConditionId(marketInfo.event_markets[0].condition_id);
      }
    }
  }, [current_language, marketInfo]);

  const tokenOutcome = useMemo(() => {
    if (!marketInfo?.clob_token_ids) return null;

    const { clob_token_ids } = marketInfo;
    if (clob_token_ids[0] === asset_id) {
      return "YES";
    } else if (clob_token_ids[1] === asset_id) {
      return "NO";
    }
    return null;
  }, [marketInfo, asset_id]);

  const getOutcomeStatus = () => {
    if (status === "Filled") {
      return "FILLED";
    }
    return "PENDING";
  };

  const getOutcomeChipColor = (status: string) => {
    if (status === "Filled") {
      return "bg-green-100 text-green-600";
    }
    return "bg-yellow-100 text-yellow-600";
  };

  const outcome = getOutcomeStatus();
  const event_id = marketInfo?.event_markets?.[0]?.event_id;
  const icon = marketInfo?.icon;

  return (
    <>
      <div className="w-full flex flex-col sm:flex-row items-start sm:items-center justify-start gap-2 text-sm">
        {/* mobile */}
        <div className="block sm:hidden w-full">
          <div className="flex items-center gap-2">
            <Image
              className="h-10 w-10 object-cover rounded-md"
              src={icon || "/image_alt.jpg"}
              alt=""
              width={40}
              height={40}
            />
            <Link
              href={`/event/${currentSlug}?tid=${event_id}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}&qid=${question_id}`}
              className="text-inherit"
            >
              <span className="font-semibold hover:underline whitespace-normal overflow-hidden text-ellipsis line-clamp-2 truncate">
                {currentTitle}
              </span>
            </Link>
          </div>

          <div className="flex justify-between gap-2 mt-2">
            <div className="w-[20%]">
              <Chip
                size="sm"
                radius="sm"
                className={`${side === "SELL" ? "bg-red-400" : side === "BUY" ? "bg-green-400" : ""}`}
                style={{ color: "white" }}
              >
                {side}
                {tokenOutcome && <span>{" - " + tokenOutcome}</span>}
              </Chip>
            </div>
            <div className="w-[10%] font-sans">{currentPrice}¢</div>
            <div className="w-[10%]">{currentFilled}</div>
            <div className="w-[20%]">${currentValue}</div>
            <div className="w-[40%] text-gray-600">{currentTimeString}</div>
          </div>
        </div>

        {/* PC */}
        <div className="hidden sm:flex w-full items-center justify-start gap-2 text-sm">
          <div className="w-[10%] flex items-center gap-2">
            <Chip
              size="sm"
              radius="sm"
              className={`${side === "SELL" ? "bg-red-400" : side === "BUY" ? "bg-green-400" : ""}`}
              style={{ color: "white" }}
            >
              {side}
              {tokenOutcome && <span>{" - " + tokenOutcome}</span>}
            </Chip>
          </div>
          <div className="flex w-[38%] items-center pr-4">
            <Image
              className="h-10 w-10 object-cover rounded-md"
              src={icon || "/image_alt.jpg"}
              alt=""
              width={40}
              height={40}
            />
            <div className="w-full flex flex-col justify-start ml-4 gap-2">
              {currentTitle && (
                <Link
                  href={`/event/${currentSlug}?tid=${event_id}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}&qid=${question_id}`}
                  className="text-inherit"
                >
                  <span className="font-semibold hover:underline whitespace-normal overflow-hidden text-ellipsis line-clamp-2">
                    {currentTitle}
                  </span>
                </Link>
              )}
            </div>
          </div>
          <div className="w-[8%]">
            <Chip size="sm" radius="sm" color="success" className={`${getOutcomeChipColor(status)} font-semibold`}>
              {outcome}
            </Chip>
          </div>
          <div className="w-[8%] font-sans">{currentPrice}¢</div>
          <div className="w-[8%]">{currentFilled}</div>
          <div className="w-[8%]">${currentValue}</div>
          <div className="flex w-[20%] justify-end text-gray-600">{currentTimeString}</div>
        </div>
      </div>
      <Divider className="my-2 bg-gray-200" />
    </>
  );
};

export default HistoryItem;
