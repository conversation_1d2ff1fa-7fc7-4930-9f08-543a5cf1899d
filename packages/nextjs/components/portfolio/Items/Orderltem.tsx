import React, { useEffect, useState } from "react";
import Image from "next/image";
import {
  base64DecodeByEn,
  base64DecodeByLanguage,
  calculateOrderFilled,
  calculateOrderTotal,
  findOrderInformation,
  getCurrentTimestamp,
} from "@/utils";
import { Chip, Link } from "@heroui/react";
import { X } from "lucide-react";
import { useGlobalState } from "~~/services/store/store";

const OrderItem = (props: any) => {
  const { item, informationList, setCurrentCancelOrderQusetion, onCancel } = props;
  const { current_language } = useGlobalState().nativeCurrency;
  const timeStamp = getCurrentTimestamp();
  const [question, setQuestion] = useState<string>("");
  const [eventId, setEventId] = useState<string | null>(null);
  const [eventSlug, setEventSlug] = useState<string | null>(null);
  const [questionPosition, setQuestionPosition] = useState<string | null>(null);
  const [defaultConditionId, setDefaultConditionId] = useState<string | null>(null);

  const { asset_id, side, price, size_current } = item;
  const information = findOrderInformation(informationList, asset_id);

  const chipBaseClass = questionPosition === "Yes" ? "bg-green-100 p-1" : "bg-red-100 p-1";
  const chipContentClass = questionPosition === "Yes" ? "text-green-600 font-semibold" : "text-red-600 font-semibold";

  const { size_matched, original_size } = item;

  useEffect(() => {
    if (information?.question) {
      setQuestion(base64DecodeByLanguage(information.question));
    }
    if (information?.event_markets[0]?.event_id) {
      setEventId(information.event_markets[0].event_id);
    }
    if (information?.event_markets[0]?.event?.slug) {
      const newSlug = base64DecodeByEn(information?.event_markets[0]?.event?.slug);
      setEventSlug(newSlug + "-" + current_language);
    }
    if (information?.event_markets[0]?.condition_id) {
      setDefaultConditionId(information.event_markets[0]?.condition_id);
    }
    if (information?.clob_token_ids) {
      const position = information.clob_token_ids[0] === asset_id ? "Yes" : "No";
      setQuestionPosition(position);
    }
  }, [current_language, information, asset_id]);

  const currentPrice = Math.round(price * 100);
  const totalValue = calculateOrderTotal(side, size_current.toString(), price.toString());
  const currentFilled = calculateOrderFilled(side, size_matched, original_size, price);
  const question_id = information?.id;

  return (
    <div className="w-full flex flex-col sm:flex-row items-start sm:items-center justify-start text-sm gap-2">
      {/* mobile */}
      <div className="block sm:hidden w-full">
        <div className="flex items-center gap-2">
          <div className="flex-shrink-0 w-10 h-10">
            {information?.icon && (
              <Image
                className="w-full h-full object-cover rounded-md"
                src={information?.icon}
                alt=""
                width={40}
                height={40}
              />
            )}
          </div>
          <Link
            href={`/event/${eventSlug}?tid=${eventId}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}&qid=${question_id}`}
            className="text-inherit flex-1"
          >
            <span className="font-semibold hover:underline whitespace-normal overflow-hidden text-ellipsis line-clamp-2 truncate">
              {question}
            </span>
          </Link>
        </div>

        <div className="flex gap-2 mt-2">
          <div className="w-full">{side}</div>
          <div className="w-full font-sans">{currentPrice}¢</div>
          <div className="w-full">{currentFilled}</div>
          <div className="w-full">${totalValue}</div>
          <div
            className="flex items-center size-4 cursor-pointer"
            onClick={() => {
              setCurrentCancelOrderQusetion(question);
              onCancel();
            }}
          >
            <X color="#E64800" strokeWidth={2.5} className="size-4" />
          </div>
        </div>
      </div>

      {/* PC */}
      <div className="hidden sm:flex w-full items-center justify-start text-sm gap-2">
        <div className="flex w-[35%] items-center pr-4">
          <div className="flex-shrink-0 w-10 h-10">
            {information?.icon && (
              <Image
                className="w-full h-full object-cover rounded-md"
                src={information?.icon}
                alt=""
                width={40}
                height={40}
              />
            )}
          </div>
          <div className="w-full flex flex-col justify-start ml-4 gap-2">
            <Link
              href={`/event/${eventSlug}?tid=${eventId}&conditionid=${defaultConditionId}&lng=${current_language}&timeStamp=${timeStamp}&qid=${question_id}`}
              className="text-inherit"
            >
              <span className="font-semibold hover:underline whitespace-normal overflow-hidden text-ellipsis line-clamp-2 truncate">
                {question}
              </span>
            </Link>
          </div>
        </div>

        <div className="w-[8%]">{side}</div>
        <div className="w-[8%]">
          {questionPosition && (
            <Chip
              size="sm"
              radius="sm"
              color="success"
              classNames={{
                base: chipBaseClass,
                content: chipContentClass,
              }}
            >
              {questionPosition}
            </Chip>
          )}
        </div>
        <div className="w-[8%] font-sans">{currentPrice}¢</div>
        <div className="w-[8%]">{currentFilled}</div>
        <div className="w-[8%]">${totalValue}</div>
        <div className="w-[20%] text-gray-400">Until Cancelled</div>
        <div
          className="flex items-center size-4 cursor-pointer"
          onClick={() => {
            setCurrentCancelOrderQusetion(question);
            onCancel();
          }}
        >
          <X color="#E64800" strokeWidth={2.5} className="size-4" />
        </div>
      </div>
    </div>
  );
};

export default OrderItem;
