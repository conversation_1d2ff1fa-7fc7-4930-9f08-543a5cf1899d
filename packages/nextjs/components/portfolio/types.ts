export interface PositionsListParams {
  user?: string; // proxyWallet
  sizeThreshold?: number; // constant: .1
  limit?: number;
  offset?: number;
  sortBy?: "CURRENT" | "INITIAL" | "CASHPNL" | "PERCENTPNL" | "TOKENS" | "TITLE" | "RESOLVING";
  sortDirection?: "DESC"; // constant: "DESC"
  title?: string;
}
export interface ActivityListDataParams {
  user?: string; // proxyWallet
  limit?: number;
  offset?: number;
  sortBy?: "CURRENT" | "INITIAL" | "CASHPNL" | "PERCENTPNL" | "TOKENS" | "TITLE" | "RESOLVING" | "TIMESTAMP" | "CASH";
  sortDirection?: "DESC" | "ASC";
  title?: string;
  type?: "TRADE" | "MERGE" | "REDEEM" | "REWARD" | "SPLIT" | "CONVERSION" | null;
  side?: "BUY" | "SELL";
}
