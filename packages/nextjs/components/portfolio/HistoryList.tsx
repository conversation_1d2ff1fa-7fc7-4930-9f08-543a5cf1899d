import React, { useEffect, useMemo, useState } from "react";
import HistoryItem from "./Items/Historyltem";
import { getHistoryListData, getInformationListData } from "@/api/order";
import { usePagination } from "@/hooks/usePagination";
import { useGlobalState } from "@/services/store/store";
import { Divider } from "@heroui/divider";
import { Spinner } from "@heroui/react";
import { useTranslation } from "react-i18next";

interface MarketInfo {
  event_markets?: Array<{
    condition_id: string;
  }>;
}

const HistoryList = (props: any) => {
  const { clobApis } = props;
  const { walletType } = useGlobalState();
  const { t } = useTranslation();

  const [informationList, setInformationList] = useState<MarketInfo[]>([]);

  const {
    data: historyListData,
    loading,
    hasMore,
    isInitialLoad,
    observerRef,
  } = usePagination({
    fetchFunction: getHistoryListData,
    limit: 10,
    initialParams: {},
    fetchParams: {
      creds: clobApis,
      walletType: walletType,
    },
    dependencies: [clobApis, walletType],
  });

  useEffect(() => {
    if (historyListData.length > 0) {
      const fetchInformationData = async () => {
        try {
          const markets = historyListData.map(item => item.market);
          const uniqueMarkets = [...new Set(markets)];

          const informationListData = await getInformationListData(uniqueMarkets);
          setInformationList(informationListData.data.question_market);
        } catch (error) {
          console.error("Error fetching information data:", error);
        }
      };

      fetchInformationData();
    }
  }, [historyListData]);

  const enrichedHistoryData = useMemo(() => {
    if (!historyListData.length || !informationList.length) {
      return [];
    }

    return historyListData.map(historyItem => {
      const matchedInfo = informationList.find(
        info =>
          info.event_markets &&
          info.event_markets.length > 0 &&
          info.event_markets[0].condition_id === historyItem.market,
      );

      return {
        ...historyItem,
        marketInfo: matchedInfo || null,
      };
    });
  }, [historyListData, informationList]);

  return (
    <div className="w-full flex flex-col mt-2">
      <div className="hidden sm:flex w-full items-center justify-start font-semibold text-sm my-1 gap-2">
        <span className="w-[10%]">{t("Portfolio_Tabel_Type")}</span>
        <span className="w-[38%]">{t("Portfolio_Tabel_MARKET")}</span>
        <span className="w-[8%]">{t("Portfolio_Tabel_Outcome")}</span>
        <span className="w-[8%]">{t("Portfolio_Tabel_Price")}</span>
        <span className="w-[8%]">{t("Portfolio_Tabel_Shares")}</span>
        <span className="w-[8%]">{t("Portfolio_Tabel_Value")}</span>
        <span className="flex w-[20%] justify-end">{t("Portfolio_Tabel_Date")}</span>
      </div>

      <Divider className="my-2 bg-gray-200" />

      {enrichedHistoryData.map((item, index) => (
        <React.Fragment key={`${item.id || index}_${index}`}>
          {item && item.marketInfo && <HistoryItem item={item} marketInfo={item.marketInfo} />}
        </React.Fragment>
      ))}

      <div ref={observerRef} className="w-full py-4 flex justify-center">
        {loading && (
          <div className="flex items-center gap-2">
            <Spinner size="sm" />
            <span className="text-sm text-gray-500">{t("Portfolio_Loading")}</span>
          </div>
        )}
        {!hasMore && !isInitialLoad && enrichedHistoryData.length > 0 && (
          <span className="text-sm text-gray-400">{t("Portfolio_No_More_Data")}</span>
        )}
        {!loading && enrichedHistoryData.length === 0 && !isInitialLoad && (
          <span className="text-sm text-gray-400">{t("Portfolio_No_Data")}</span>
        )}
      </div>
    </div>
  );
};

export default HistoryList;
