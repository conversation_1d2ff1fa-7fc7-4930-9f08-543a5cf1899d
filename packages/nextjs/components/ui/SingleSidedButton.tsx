"use client";

import React from "react";
import { Button } from "@heroui/react";

interface SingleSidedButtonProps {
  title: string;
  price: string;
  isSelected?: boolean;
  questionIndex?: number; // 用于生成不同的配色
  onClick?: () => void;
  className?: string;
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
}

// 预定义的配色方案，增强立体感效果
const colorSchemes = [
  {
    // 绿色系 - 默认
    selected:
      "bg-green-500 text-white border-green-600 shadow-[0_6px_0_0_rgb(22,163,74)] hover:shadow-[0_1px_0_0_rgb(22,163,74)] hover:translate-y-[5px]",
    unselected:
      "bg-green-50 text-green-700 border-green-200 shadow-[0_6px_0_0_rgb(187,247,208)] hover:shadow-[0_1px_0_0_rgb(187,247,208)] hover:translate-y-[5px]",
  },
  {
    // 蓝色系
    selected:
      "bg-blue-500 text-white border-blue-600 shadow-[0_6px_0_0_rgb(37,99,235)] hover:shadow-[0_1px_0_0_rgb(37,99,235)] hover:translate-y-[5px]",
    unselected:
      "bg-blue-50 text-blue-700 border-blue-200 shadow-[0_6px_0_0_rgb(191,219,254)] hover:shadow-[0_1px_0_0_rgb(191,219,254)] hover:translate-y-[5px]",
  },
  {
    // 紫色系
    selected:
      "bg-purple-500 text-white border-purple-600 shadow-[0_6px_0_0_rgb(147,51,234)] hover:shadow-[0_1px_0_0_rgb(147,51,234)] hover:translate-y-[5px]",
    unselected:
      "bg-purple-50 text-purple-700 border-purple-200 shadow-[0_6px_0_0_rgb(233,213,255)] hover:shadow-[0_1px_0_0_rgb(233,213,255)] hover:translate-y-[5px]",
  },
  {
    // 橙色系
    selected:
      "bg-orange-500 text-white border-orange-600 shadow-[0_6px_0_0_rgb(234,88,12)] hover:shadow-[0_1px_0_0_rgb(234,88,12)] hover:translate-y-[5px]",
    unselected:
      "bg-orange-50 text-orange-700 border-orange-200 shadow-[0_6px_0_0_rgb(255,237,213)] hover:shadow-[0_1px_0_0_rgb(255,237,213)] hover:translate-y-[5px]",
  },
  {
    // 红色系
    selected:
      "bg-red-500 text-white border-red-600 shadow-[0_6px_0_0_rgb(220,38,38)] hover:shadow-[0_1px_0_0_rgb(220,38,38)] hover:translate-y-[5px]",
    unselected:
      "bg-red-50 text-red-700 border-red-200 shadow-[0_6px_0_0_rgb(254,202,202)] hover:shadow-[0_1px_0_0_rgb(254,202,202)] hover:translate-y-[5px]",
  },
  {
    // 青色系
    selected:
      "bg-cyan-500 text-white border-cyan-600 shadow-[0_6px_0_0_rgb(8,145,178)] hover:shadow-[0_1px_0_0_rgb(8,145,178)] hover:translate-y-[5px]",
    unselected:
      "bg-cyan-50 text-cyan-700 border-cyan-200 shadow-[0_6px_0_0_rgb(165,243,252)] hover:shadow-[0_1px_0_0_rgb(165,243,252)] hover:translate-y-[5px]",
  },
];

const SingleSidedButton: React.FC<SingleSidedButtonProps> = ({
  title,
  price,
  isSelected = false,
  questionIndex = 0,
  onClick,
  className = "",
  size = "lg",
  disabled = false,
}) => {
  // 根据问题索引选择配色方案
  const colorScheme = colorSchemes[questionIndex % colorSchemes.length];
  const buttonClass = isSelected ? colorScheme.selected : colorScheme.unselected;

  // 根据尺寸设置不同的样式
  const sizeClasses = {
    sm: "w-[120px] text-xs",
    md: "w-[130px] text-sm",
    lg: "w-[140px] text-sm sm:text-medium",
  };

  return (
    <Button
      size="lg"
      radius="sm"
      onPress={onClick}
      disabled={disabled}
      className={`
        ${sizeClasses[size]}
        ${buttonClass}
        font-semibold
        border-2
        transition-all
        duration-150
        ease-in-out
        active:translate-y-[5px]
        active:shadow-[0_1px_0_0_currentColor]
        disabled:opacity-50
        disabled:cursor-not-allowed
        disabled:transform-none
        disabled:shadow-none
        ${className}
      `}
    >
      <div className="flex flex-col items-center justify-center w-full">
        {/* 问题标题 */}
        <span className="font-semibold truncate w-full text-center" title={title}>
          {title}
        </span>

        {/* 价格 */}
        <span className="text-xs">{price}¢</span>
      </div>
    </Button>
  );
};

export default SingleSidedButton;
