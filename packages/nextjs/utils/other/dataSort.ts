const sortAndFilterMarkets = (marketsDataList: any[]) => {
  return marketsDataList
    .filter((item: any) => item.question_market.clob_token_ids && item.question_market.clob_token_ids.length > 0)
    .sort((a: any, b: any) => {
      const aPrice = a.question_market.lasttradeprice;
      const bPrice = b.question_market.lasttradeprice;
      if (aPrice === null) return 1;
      if (bPrice === null) return -1;
      return bPrice - aPrice;
    });
};

const sortEventMarkets = (eventCardData: any[]) => {
  return eventCardData.sort((a: any, b: any) => {
    const aPrice = a.question_market.lasttradeprice;
    const bPrice = b.question_market.lasttradeprice;
    if (aPrice === null) return 1;
    if (bPrice === null) return -1;
    return bPrice - aPrice;
  });
};

export { sortAndFilterMarkets, sortEventMarkets };
