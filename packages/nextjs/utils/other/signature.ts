import { WalletType } from "@/services/store/store";
import { OAuthExtension } from "@magic-ext/oauth";
import { <PERSON>rowser<PERSON>rovider } from "ethers";
import { Magic } from "magic-sdk";

type SignatureType = "personal_sign" | "eth_signTypedData_v4";

async function metaMaskSignature(
  message: string | object,
  signatureType: SignatureType = "personal_sign",
): Promise<string> {
  if (typeof window.ethereum !== "undefined") {
    try {
      const accounts = await window.ethereum.request({ method: "eth_requestAccounts" });
      const account = accounts[0];

      let signature: string;
      if (signatureType === "personal_sign") {
        signature = await window.ethereum.request({
          method: "personal_sign",
          params: [message, account],
        });
      } else if (signatureType === "eth_signTypedData_v4") {
        signature = await window.ethereum.request({
          method: "eth_signTypedData_v4",
          params: [account, typeof message === "string" ? message : JSON.stringify(message)],
        });
      } else {
        throw new Error("Unsupported signature type");
      }

      return signature;
    } catch (error) {
      console.error(error);
      throw new Error("User denied signature request");
    }
  } else {
    throw new Error("MetaMask is not installed");
  }
}

async function magicSignature(
  message: string | object,
  signatureType: SignatureType = "personal_sign",
): Promise<string> {
  const magicApiKey = process.env.NEXT_PUBLIC_MAGIC_API_KEY;
  if (!magicApiKey) {
    throw new Error("Magic API key is not defined");
  }

  try {
    const magic = new Magic(magicApiKey, {
      extensions: [new OAuthExtension()],
    });

    const provider = new BrowserProvider(magic.rpcProvider as any);
    const signer = await provider.getSigner();

    const userInfo = await magic.user.getInfo();
    if (!userInfo.publicAddress) {
      throw new Error("User not logged in");
    }

    let signature: string;
    if (signatureType === "personal_sign") {
      signature = await provider.send("personal_sign", [message, userInfo.publicAddress]);
    } else if (signatureType === "eth_signTypedData_v4") {
      signature = await provider.send("eth_signTypedData_v4", [
        await signer.getAddress(),
        typeof message === "string" ? message : JSON.stringify(message),
      ]);
    } else {
      throw new Error("Unsupported signature type");
    }

    return signature;
  } catch (error) {
    console.error(error);
    throw new Error("User denied signature request");
  }
}

async function getSignature(
  message: string | object,
  walletType: WalletType,
  signatureType: SignatureType = "personal_sign",
): Promise<string> {
  let signature: string;
  if (walletType == "metamask") {
    signature = await metaMaskSignature(message, signatureType);
  } else if (walletType == "magic") {
    signature = await magicSignature(message, signatureType);
  } else {
    throw new Error("Unsupported wallet type");
  }
  return signature;
}

export { metaMaskSignature, magicSignature, getSignature };
export type { SignatureType };
