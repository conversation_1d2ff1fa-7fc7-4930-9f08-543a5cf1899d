import { getGeoInfo } from "@/api/other";

const isBrowser = typeof window !== "undefined";

export const setItem = (key: string, value: any): void => {
  if (!isBrowser) return;
  try {
    const serializedValue = JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error("设置 localStorage 项目时出错:", error);
  }
};

export const getItem = (key: string): any | null => {
  if (!isBrowser) return null;
  try {
    const serializedValue = localStorage.getItem(key);
    if (serializedValue === null) {
      return null;
    }
    return JSON.parse(serializedValue);
  } catch (error) {
    console.error("获取 localStorage 项目时出错:", error);
    return null;
  }
};

export const removeItem = (key: string): void => {
  if (!isBrowser) return;
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error("移除 localStorage 项目时出错:", error);
  }
};

export const clearStorage = (): void => {
  if (!isBrowser) return;
  try {
    localStorage.clear();
  } catch (error) {
    console.error("清空 localStorage 时出错:", error);
  }
};

export const checkGeolocation = async () => {
  if (!isBrowser) return;

  try {
    const response = await getGeoInfo();
    const { Country } = response.data;
    const blockedCountries = process.env.NEXT_PUBLIC_BLOCKED_COUNTRIES?.split(",") || [];
    const isBlocked = Country ? blockedCountries.includes(Country) : false;
    const currentRegionAllowed = getItem("isRegionAllowed");

    const isAllowed = !isBlocked;

    if (currentRegionAllowed !== isAllowed) {
      setItem("isRegionAllowed", isAllowed);
    }
    return isAllowed;
  } catch (error) {
    console.error("Error getting geolocation:", error);
  }
};
