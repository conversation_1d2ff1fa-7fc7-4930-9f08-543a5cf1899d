import { NewOrder, OrderType, Side } from "./types";
import { SignedOrder, Side as UtilsSide } from "@polymarket/order-utils";

const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = { weekday: "short", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("en-US", options);
};
const formatAmount = (amount: number): string => {
  const integerPart = Math.floor(amount);

  return integerPart.toLocaleString("en-US");
};

const formatLargeAmount = (amount: number): string => {
  if (amount >= 1e12) {
    return (amount / 1e12).toFixed(1) + "T";
  } else if (amount >= 1e9) {
    return (amount / 1e9).toFixed(1) + "B";
  } else if (amount >= 1e6) {
    return (amount / 1e6).toFixed(1) + "M";
  } else if (amount >= 1e3) {
    return (amount / 1e3).toFixed(1) + "K";
  } else {
    return amount.toFixed(1);
  }
};

const formatLargeAmountWithCommas = (amount: number): string => {
  const roundedAmount = Math.floor(amount);
  return new Intl.NumberFormat().format(roundedAmount);
};

const formatLasttradeprice = (lasttradeprice: number): string => {
  const percentage = lasttradeprice ? Math.round(lasttradeprice * 100) + "%" : "<1%";
  return percentage;
};

const formatButtonPrice = (btnData: any = {}, currentSelectedItem: string): any => {
  const { yesBookData, noBookData } = btnData;
  let YesPrice = 0;
  let NoPrice = 0;

  if (currentSelectedItem === "Buy") {
    YesPrice =
      yesBookData?.asks.length > 0 ? Math.min(...yesBookData.asks.map((ask: any) => parseFloat(ask.price))) : 0;
    NoPrice = noBookData?.asks.length > 0 ? Math.min(...noBookData.asks.map((ask: any) => parseFloat(ask.price))) : 0;
  } else if (currentSelectedItem === "Sell") {
    YesPrice =
      yesBookData?.bids.length > 0 ? Math.max(...yesBookData.bids.map((bid: any) => parseFloat(bid.price))) : 0;
    NoPrice = noBookData?.bids.length > 0 ? Math.max(...noBookData.bids.map((bid: any) => parseFloat(bid.price))) : 0;
  }

  YesPrice = YesPrice ? parseFloat((YesPrice * 100).toFixed(1)) : 0;
  NoPrice = NoPrice ? parseFloat((NoPrice * 100).toFixed(1)) : 0;

  return { YesPrice, NoPrice };
};

// Price formatting function specifically designed for Game cards, only handles Yes case
const formatGameButtonPrice = (btnData: any = {}, currentSelectedItem: string): any => {
  const { yesBookData } = btnData;
  let YesPrice = 0;
  let hasOrderBook = false;

  if (currentSelectedItem === "Buy") {
    // Show minimum ask price when buying
    if (yesBookData?.asks?.length > 0) {
      YesPrice = Math.min(...yesBookData.asks.map((ask: any) => parseFloat(ask.price)));
      hasOrderBook = true;
    }
  } else if (currentSelectedItem === "Sell") {
    // Show maximum bid price when selling
    if (yesBookData?.bids?.length > 0) {
      YesPrice = Math.max(...yesBookData.bids.map((bid: any) => parseFloat(bid.price)));
      hasOrderBook = true;
    }
  }

  YesPrice = YesPrice ? parseFloat((YesPrice * 100).toFixed(1)) : 0;

  return { YesPrice, NoPrice: 0, hasOrderBook }; // Return indicator of whether order book data exists
};

const formatDynamicButtonPrice = (
  bookData: any = {},
  currentSelectedItem: "Buy" | "Sell",
  selectedButtonType: "yes" | "no",
): any => {
  let YesPrice = 0;
  let NoPrice = 0;

  if (currentSelectedItem === "Buy") {
    if (selectedButtonType === "yes") {
      YesPrice = bookData?.asks.length > 0 ? Math.min(...bookData.asks.map((ask: any) => parseFloat(ask.price))) : 0;
      NoPrice = bookData?.bids.length > 0 ? Math.min(...bookData.bids.map((bid: any) => 1 - parseFloat(bid.price))) : 0;
    }
    if (selectedButtonType === "no") {
      NoPrice = bookData?.asks.length > 0 ? Math.min(...bookData.asks.map((ask: any) => parseFloat(ask.price))) : 0;
      YesPrice =
        bookData?.bids.length > 0 ? Math.min(...bookData.bids.map((bid: any) => 1 - parseFloat(bid.price))) : 0;
    }
  } else if (currentSelectedItem === "Sell") {
    if (selectedButtonType === "yes") {
      YesPrice = bookData?.bids.length > 0 ? Math.max(...bookData.bids.map((bid: any) => parseFloat(bid.price))) : 0;
      NoPrice = bookData?.asks.length > 0 ? Math.max(...bookData.asks.map((ask: any) => 1 - parseFloat(ask.price))) : 0;
    }
    if (selectedButtonType === "no") {
      NoPrice = bookData?.bids.length > 0 ? Math.max(...bookData.bids.map((bid: any) => parseFloat(bid.price))) : 0;
      YesPrice =
        bookData?.asks.length > 0 ? Math.max(...bookData.asks.map((ask: any) => 1 - parseFloat(ask.price))) : 0;
    }
  }

  YesPrice = YesPrice ? parseFloat((YesPrice * 100).toFixed(1)) : 0;
  NoPrice = NoPrice ? parseFloat((NoPrice * 100).toFixed(1)) : 0;

  return { YesPrice, NoPrice };
};

const formatLastTradePrice = (lastTradePrice: number): number => {
  const formattedNum = lastTradePrice * 100;
  return Math.trunc(formattedNum);
};

const truncateToTwoDecimals = (value: number): string => {
  const strValue = value.toString();
  const decimalIndex = strValue.indexOf(".");
  if (decimalIndex === -1) return strValue;

  return strValue.substring(0, decimalIndex + 3);
};

const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  };
  return date.toLocaleDateString("en-US", options).replace(",", "");
};
const formatTime12HourClock = (dateString: string): string => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  };
  return date.toLocaleTimeString("en-US", options);
};

const roundToInteger = (value: number): string => {
  const roundedValue = Math.round(value);
  return roundedValue.toLocaleString("en-US");
};

function orderToJson<T extends OrderType>(order: SignedOrder, owner: string, orderType: T): NewOrder<T> {
  let side = Side.BUY;
  if (order.side == UtilsSide.BUY) {
    side = Side.BUY;
  } else {
    side = Side.SELL;
  }

  return {
    order: {
      salt: parseInt(order.salt, 10),
      maker: order.maker,
      signer: order.signer,
      taker: order.taker,
      tokenId: order.tokenId,
      makerAmount: order.makerAmount,
      takerAmount: order.takerAmount,
      side,
      expiration: order.expiration,
      nonce: order.nonce,
      feeRateBps: order.feeRateBps,
      signatureType: order.signatureType,
      signature: order.signature,
    },
    owner,
    orderType,
  } as NewOrder<T>;
}

const clearCookie = (cookie: string): string => {
  return cookie.replace("predictiononesession=", "").split(";")[0];
};

// Tag 去重
const removeDuplicatesByTagSlug = (items: any[]): any[] => {
  const seenSlugs = new Set<string>();
  return items.filter(item => {
    if (seenSlugs.has(item.slug)) {
      return false;
    } else {
      seenSlugs.add(item.slug);
      return true;
    }
  });
};

interface Tag {
  region: string;
  slug: string;
  id: number;
}
function filterTagsByLanguage(current_language: string, tagList: Tag[]): Tag[] {
  return tagList.filter(
    tag =>
      tag.region &&
      tag.region
        .split(",")
        .map(lang => lang.trim())
        .includes(current_language),
  );
}
function filterEventsByLanguage(current_language: string, eventList: any): any[] {
  return eventList.filter((event: any) => {
    return event.event_tags.some((eventTag: any) => {
      if (!eventTag?.tag?.region) {
        return false;
      }

      try {
        const regions = eventTag.tag.region.split(",").map((lang: any) => lang.trim());
        const hasCurrentLanguage = regions.includes(current_language);

        if (hasCurrentLanguage) {
          return true;
        }

        return false; // 当前标签不匹配，继续检查下一个标签
      } catch (error) {
        console.error("Error parsing tag region:", eventTag, error);
        return false; // 出错的标签跳过，继续检查下一个
      }
    });
  });
}

// 获取需要排除的 tag_id 列表
function getExcludedTagIds(): number[] {
  const excludedIds = process.env.NEXT_PUBLIC_EXCLUDED_TAG_IDS || "";
  return excludedIds
    .split(",")
    .map(id => parseInt(id.trim(), 10))
    .filter(id => !isNaN(id));
}

// 过滤掉特定 tag_id 的标签 (适用于 Tag 类型，id 为 number)
function filterTagsByExcludedIds(tagList: Tag[]): Tag[] {
  const EXCLUDED_TAG_IDS = getExcludedTagIds();
  return tagList.filter(tag => !EXCLUDED_TAG_IDS.includes(tag.id));
}

// 过滤掉特定 tag_id 的标签 (适用于 TagTypes 类型，id 为 string)
function filterTagTypesByExcludedIds<T extends { id: string | number }>(tagList: T[]): T[] {
  const EXCLUDED_TAG_IDS = getExcludedTagIds();
  return tagList.filter(tag => {
    const tagId = typeof tag.id === "string" ? parseInt(tag.id, 10) : tag.id;
    return !isNaN(tagId) && !EXCLUDED_TAG_IDS.includes(tagId);
  });
}

// 检查事件是否包含被排除的标签
function hasExcludedTags(event: any): boolean {
  const EXCLUDED_TAG_IDS = getExcludedTagIds();

  if (!event?.event_tags || !Array.isArray(event.event_tags)) {
    return false;
  }

  return event.event_tags.some((eventTag: any) => {
    const tagId = eventTag?.tag_id;
    return tagId && EXCLUDED_TAG_IDS.includes(tagId);
  });
}

// 过滤掉包含被排除标签的事件列表
function filterEventsByExcludedTags(eventList: any[]): any[] {
  if (!Array.isArray(eventList)) {
    return [];
  }

  return eventList.filter(event => !hasExcludedTags(event));
}

// 检查事件是否包含creator标签
function hasCreatorTags(event: any): boolean {
  if (!event?.event_tags || !Array.isArray(event.event_tags)) {
    return false;
  }

  return event.event_tags.some((eventTag: any) => {
    const tagSlug = eventTag?.tag?.slug?.toLowerCase();
    return tagSlug === "creator";
  });
}

// 过滤掉包含creator标签的事件列表
function filterOutCreatorEvents(eventList: any[]): any[] {
  if (!Array.isArray(eventList)) {
    return [];
  }

  return eventList.filter(event => !hasCreatorTags(event));
}

// 只保留包含creator标签的事件列表
function filterOnlyCreatorEvents(eventList: any[]): any[] {
  if (!Array.isArray(eventList)) {
    return [];
  }

  return eventList.filter(event => hasCreatorTags(event));
}

// 检查事件是否包含指定的标签ID
function hasSpecificTag(event: any, tagId: string): boolean {
  if (!event?.event_tags || !Array.isArray(event.event_tags)) {
    return false;
  }

  return event.event_tags.some((eventTag: any) => {
    const tag = eventTag?.tag;
    if (!tag) return false;

    // 尝试多种可能的ID字段
    const possibleIds = [
      tag.id?.toString(),
      tag.tag_id?.toString(),
      eventTag.tag_id?.toString(),
      eventTag.id?.toString(),
    ].filter(Boolean);

    return possibleIds.some(id => id === tagId.toString());
  });
}

// 过滤creator事件并按指定标签筛选
function filterCreatorEventsByTag(eventList: any[], tagId: string): any[] {
  if (!Array.isArray(eventList)) {
    return [];
  }

  return eventList.filter(event => {
    const hasCreator = hasCreatorTags(event);

    // 如果是"all"，只显示包含creator标签的事件
    if (tagId === "all") {
      return hasCreator;
    }

    // 对于特定标签，必须同时包含creator标签和指定标签
    const hasSpecific = hasSpecificTag(event, tagId);
    return hasCreator && hasSpecific;
  });
}

// 过滤掉creator相关的标签选项
function filterOutCreatorTags(tags: any[], includeAdvancedFilters = false): any[] {
  if (!Array.isArray(tags)) {
    return [];
  }

  return tags.filter((tag: any) => {
    const slug = tag.slug?.toLowerCase() || "";
    const label = tag.label?.toLowerCase() || "";

    if (includeAdvancedFilters) {
      // 高级过滤：过滤掉creator、community相关的标签
      return !(slug === "creator" || slug === "community" || label.includes("creator") || label.includes("community"));
    } else {
      // 基础过滤：只过滤creator标签
      return slug !== "creator";
    }
  });
}

// 过滤掉没有事件的空标签
function filterEmptyTags(tags: any[]): any[] {
  if (!Array.isArray(tags)) {
    return [];
  }

  return tags.filter((tag: any) => {
    // 检查是否有事件数量信息
    if (tag.events_aggregate?.aggregate?.count !== undefined) {
      return tag.events_aggregate.aggregate.count > 0;
    }

    // 检查是否有event_tags数量信息
    if (tag.event_tags_aggregate?.aggregate?.count !== undefined) {
      return tag.event_tags_aggregate.aggregate.count > 0;
    }

    // 如果没有数量信息，保留标签（向后兼容）
    return true;
  });
}

// 组合过滤：同时过滤空标签和creator标签
function filterTagsWithEventCount(
  tags: any[],
  options?: {
    filterEmpty?: boolean;
    filterCreator?: boolean;
    includeAdvancedFilters?: boolean;
  },
): any[] {
  if (!Array.isArray(tags)) {
    return [];
  }

  let filteredTags = tags;

  // 过滤空标签
  if (options?.filterEmpty !== false) {
    filteredTags = filterEmptyTags(filteredTags);
  }

  // 过滤creator标签
  if (options?.filterCreator !== false) {
    filteredTags = filterOutCreatorTags(filteredTags, options?.includeAdvancedFilters);
  }

  return filteredTags;
}

const processNumber = (value: number | string, digits?: number): number | string => {
  if (typeof value === "string") {
    value = Number(value);
  }
  if (digits === 18) {
    value = value / 1e6;
  }
  value = Math.abs(value);

  return value.toFixed(2);
};

export {
  formatDate,
  formatAmount,
  formatLargeAmount,
  formatLasttradeprice,
  formatLargeAmountWithCommas,
  formatLastTradePrice,
  formatTime,
  roundToInteger,
  orderToJson,
  clearCookie,
  removeDuplicatesByTagSlug,
  processNumber,
  filterTagsByLanguage,
  formatButtonPrice,
  formatGameButtonPrice,
  formatDynamicButtonPrice,
  filterEventsByLanguage,
  filterTagsByExcludedIds,
  filterTagTypesByExcludedIds,
  filterEventsByExcludedTags,
  hasExcludedTags,
  getExcludedTagIds,
  formatTime12HourClock,
  truncateToTwoDecimals,
  hasCreatorTags,
  filterOutCreatorEvents,
  filterOnlyCreatorEvents,
  hasSpecificTag,
  filterCreatorEventsByTag,
  filterOutCreatorTags,
  filterEmptyTags,
  filterTagsWithEventCount,
};
