import { WalletType } from "@/services/store/store";
import { Web3Provider } from "@ethersproject/providers";
import { OAuthExtension } from "@magic-ext/oauth";
import backoff from "backoff";
import { ethers } from "ethers";
import { Magic } from "magic-sdk";

const getProviderAndSigner = async (type: WalletType) => {
  if (type == "metamask") {
    await window.ethereum.request({ method: "eth_requestAccounts" });
    const provider = new ethers.BrowserProvider(window.ethereum);
    const signer = await provider.getSigner();
    return signer;
  }
  if (type == "magic") {
    const magicApiKey = process.env.NEXT_PUBLIC_MAGIC_API_KEY;
    if (!magicApiKey) {
      throw new Error("NEXT_PUBLIC_MAGIC_API_KEY is not defined");
    }

    const magic = new Magic(magicApiKey, {
      extensions: [new OAuthExtension()],
    });
    const provider = new Web3Provider(magic.rpcProvider as any);
    const signer = provider.getSigner();
    return signer;
  }
};

// 带重试机制的 getProviderAndSigner 函数 (使用 backoff 库)
const getProviderAndSignerWithRetry = async (type: WalletType): Promise<any> => {
  // 如果钱包类型是 'none' 或无效，直接抛出错误，不需要重试
  if (type === "none" || !type) {
    const error = new Error(`No wallet connected. Please connect your wallet first. (type: ${type})`);
    throw error;
  }

  return new Promise((resolve, reject) => {
    // 创建指数退避策略
    const exponentialBackoff = backoff.exponential({
      randomisationFactor: 0.1,
      initialDelay: 1000, // 初始延迟 1 秒
      maxDelay: 5000, // 最大延迟 5 秒
    });

    // 设置最大重试次数
    exponentialBackoff.failAfter(3); // 最多重试 3 次

    let attemptCount = 0;

    // 定义重试函数
    const attemptGetSigner = async () => {
      attemptCount++;

      try {
        // 调用原始函数
        const signer = await getProviderAndSigner(type);

        // 验证 signer 是否有效
        if (!signer) {
          throw new Error(`Failed to get signer from ${type}`);
        }

        resolve(signer);
      } catch (error) {
        // 触发 backoff 重试
        exponentialBackoff.backoff(error);
      }
    };

    // 监听重试事件
    exponentialBackoff.on("backoff", (number, delay) => {
      console.log(`⏳ getProviderAndSignerWithRetry retrying in ${delay}ms... (attempt ${number + 1})`, {
        type,
        delay,
        nextAttempt: number + 1,
      });
    });

    // 监听准备重试事件
    exponentialBackoff.on("ready", () => {
      attemptGetSigner();
    });

    // 监听最终失败事件
    exponentialBackoff.on("fail", () => {
      const finalError = new Error(`Failed to get signer after ${attemptCount} attempts`);
      console.error(`💥 getProviderAndSignerWithRetry failed after ${attemptCount} attempts`, {
        type,
        finalError: finalError.message,
        timestamp: new Date().toISOString(),
      });
      reject(finalError);
    });

    // 开始第一次尝试
    attemptGetSigner();
  });
};

function getUrlParams(url: string): { [key: string]: string } {
  const urlObj = new URL(url);
  const params = new URLSearchParams(urlObj.search);
  const result: { [key: string]: string } = {};

  params.forEach((value, key) => {
    result[key] = value;
  });

  return result;
}

const getCurrentTimestamp = (): number => {
  return Math.floor(Date.now() / 1000);
};

const getTimestampPlus7Days = (): number => {
  const currentTime = Date.now();
  const sevenDaysInMilliseconds = 7 * 24 * 60 * 60 * 1000; // 7天的毫秒数
  const futureTime = currentTime + sevenDaysInMilliseconds;
  return Math.floor(futureTime / 1000); // 转换为秒级时间戳
};

const getConditionId = (marketList: any[], selectedId: number) => {
  for (const market of marketList) {
    if (market.question_market.id == selectedId) {
      return market.question_market.condition_id;
    }
  }
  return null; // 如果没有找到匹配的id，返回null
};

const getPositionSide = (asset: string, clobTokenIds: string[]) => {
  if (asset && clobTokenIds) {
    if (asset === clobTokenIds[0]) {
      return "Yes";
    } else if (asset === clobTokenIds[1]) {
      return "No";
    }
  }
  return null;
};

const generateRandomNonce = (length = 16): string => {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let nonce = "";
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    nonce += characters[randomIndex];
  }
  return nonce;
};

export {
  getProviderAndSigner,
  getProviderAndSignerWithRetry,
  getUrlParams,
  getCurrentTimestamp,
  generateRandomNonce,
  getTimestampPlus7Days,
  getConditionId,
  getPositionSide,
};
