import computeProxyAddress from "@/contracts/computeProxyAddress";
import { hasApproved } from "@/contracts/encodeMultiSendTransaction";
import { getItem } from "@/utils";

const validateOrder = (
  price: number,
  sharesValue: number,
  order_min_size: number,
  currentSelectedItem: string,
  setPriceError: (msg: string) => void,
  setSharesError: (msg: string) => void,
): boolean => {
  let hasError = false;
  const total = ((price * sharesValue) / 100) * 10 ** 6;
  const minTotal = order_min_size / 10 ** 6;
  const minTotalText = Number.isInteger(minTotal) ? minTotal.toString() : minTotal.toFixed(2);

  if (price <= 0) {
    setPriceError("Invalid price quantity");
    hasError = true;
  } else if (price > 100) {
    setPriceError("Price cannot exceed 100¢");
    hasError = true;
  } else {
    setPriceError("");
  }

  // Shares validation
  if (sharesValue < 5) {
    setSharesError(`Must place at least 5 shares`);
    hasError = true;
  } else if (total < order_min_size) {
    setSharesError(`Market ${currentSelectedItem} must be greater than $${minTotalText}`);
    hasError = true;
  } else {
    setSharesError("");
  }

  return hasError;
};

const getApprovalData = async (address: string) => {
  const signatureStatus = await getItem(`login_signed_${address}`);
  const proxyWallet = getItem(`login_proxyWallet`);
  if (!signatureStatus) return { hasCurrentApi: false, approved: false };

  const clobApis = getItem("poly_clob_api_key_map") || {};
  const hasCurrentApi = clobApis.hasOwnProperty(address);
  const approved = (await hasApproved(proxyWallet)) === "success";

  return { hasCurrentApi, approved };
};

const getProxyAndCheckApprove = async (address: string | undefined, setIsShowPreBuyModal: (value: boolean) => void) => {
  if (address) {
    const { hasCurrentApi, approved } = await getApprovalData(address);
    setIsShowPreBuyModal(!hasCurrentApi || !approved);
  }
};

const checkProxyAndCheckApprove = async (
  address: string | undefined,
  setIsShowPreBuyModal: (value: boolean) => void,
  setNotification: (props: {
    title: string;
    content?: string;
    footer?: string;
    notiStatus?: "success" | "failure";
  }) => void,
): Promise<boolean> => {
  if (address) {
    const realProxyWallet = await computeProxyAddress(address);

    const proxyWallet = getItem(`login_proxyWallet`);

    if (realProxyWallet !== proxyWallet) {
      setNotification({
        title: "Proxy Wallet Mismatch",
        content: "Your proxy wallet does not match with the website. Please log in again.",
        notiStatus: "failure",
      });
      return false;
    }
    const { hasCurrentApi, approved } = await getApprovalData(address);
    setIsShowPreBuyModal(!hasCurrentApi || !approved);
    return hasCurrentApi && approved;
  }
  return false;
};

const checkApprovalStatus = async (address: string): Promise<boolean> => {
  if (!address) return false;
  const { hasCurrentApi, approved } = await getApprovalData(address);
  return hasCurrentApi && approved;
};

export { validateOrder, getProxyAndCheckApprove, checkProxyAndCheckApprove, checkApprovalStatus, getApprovalData };
