/**
 * RPC 管理器 - 最佳实践方案
 * 特性：连接池、健康检查、负载均衡、故障转移
 */

interface RpcNode {
  url: string;
  isHealthy: boolean;
  lastCheck: number;
  consecutiveFailures: number;
  responseTime: number;
  weight: number; // 权重，用于负载均衡

  // 新增智能管理字段
  chainId?: string; // 节点的链ID
  isChainValid?: boolean; // 链ID是否匹配期望值
  networkType?: string; // 网络类型（如 'sepolia', 'base', 'mainnet'）
  avgResponseTime: number; // 平均响应时间
  successRate: number; // 成功率 (0-1)
  totalRequests: number; // 总请求数
  successfulRequests: number; // 成功请求数
  lastSuccessTime: number; // 最后成功时间
  priority: number; // 优先级 (1-10, 10最高)
}

interface RpcManagerConfig {
  healthCheckInterval: number; // 健康检查间隔
  maxConsecutiveFailures: number; // 最大连续失败次数
  requestTimeout: number; // 请求超时时间
  retryDelay: number; // 重试延迟
  maxRetries: number; // 最大重试次数
  fastFailoverEnabled: boolean; // 是否启用快速故障转移
  fastFailoverTimeout: number; // 快速故障检测超时时间（毫秒）
  immediateRetryOnFailure: boolean; // 失败时是否立即重试其他节点
}

class RpcManager {
  private nodes: Map<string, RpcNode> = new Map();
  private config: RpcManagerConfig;
  private healthCheckTimer: NodeJS.Timeout | null = null;
  private isInitialized = false;
  // 新增：节点状态更新锁，防止竞态条件
  private nodeUpdateLocks: Map<string, Promise<void>> = new Map();
  // 新增：快速健康检查去重，防止检查风暴
  private fastHealthCheckPromises: Map<string, Promise<void>> = new Map();

  constructor(config: Partial<RpcManagerConfig> = {}) {
    this.config = {
      healthCheckInterval: 30000, // 30秒检查一次
      maxConsecutiveFailures: 3,
      requestTimeout: 5000,
      retryDelay: 1000,
      maxRetries: 3,
      fastFailoverEnabled: true, // 默认启用快速故障转移
      fastFailoverTimeout: 2000, // 2秒快速检测超时
      immediateRetryOnFailure: true, // 默认启用立即重试
      ...config,
    };

    // 配置验证和安全限制
    this.validateAndSanitizeConfig();
  }

  /**
   * 验证和清理配置
   */
  private validateAndSanitizeConfig() {
    const c = this.config;

    // 超时时间限制
    if (c.requestTimeout < 1000 || c.requestTimeout > 30000) {
      console.warn(`⚠️ requestTimeout ${c.requestTimeout}ms is out of safe range [1000-30000ms], using 5000ms`);
      c.requestTimeout = 5000;
    }

    if (c.fastFailoverTimeout < 500 || c.fastFailoverTimeout > 10000) {
      console.warn(
        `⚠️ fastFailoverTimeout ${c.fastFailoverTimeout}ms is out of safe range [500-10000ms], using 2000ms`,
      );
      c.fastFailoverTimeout = 2000;
    }

    // 重试次数限制
    if (c.maxRetries < 1 || c.maxRetries > 10) {
      console.warn(`⚠️ maxRetries ${c.maxRetries} is out of safe range [1-10], using 3`);
      c.maxRetries = 3;
    }

    if (c.maxConsecutiveFailures < 1 || c.maxConsecutiveFailures > 10) {
      console.warn(`⚠️ maxConsecutiveFailures ${c.maxConsecutiveFailures} is out of safe range [1-10], using 3`);
      c.maxConsecutiveFailures = 3;
    }

    // 健康检查间隔限制
    if (c.healthCheckInterval < 5000 || c.healthCheckInterval > 300000) {
      console.warn(
        `⚠️ healthCheckInterval ${c.healthCheckInterval}ms is out of safe range [5000-300000ms], using 30000ms`,
      );
      c.healthCheckInterval = 30000;
    }

    // 快速故障转移超时不能大于请求超时
    if (c.fastFailoverTimeout >= c.requestTimeout) {
      console.warn(`⚠️ fastFailoverTimeout should be less than requestTimeout, adjusting to ${c.requestTimeout / 2}ms`);
      c.fastFailoverTimeout = Math.floor(c.requestTimeout / 2);
    }

    console.log("✅ RPC Manager config validated:", {
      requestTimeout: c.requestTimeout,
      fastFailoverTimeout: c.fastFailoverTimeout,
      maxRetries: c.maxRetries,
      fastFailoverEnabled: c.fastFailoverEnabled,
    });
  }

  /**
   * 重置 RPC 管理器状态
   */
  reset() {
    console.log("🔄 Resetting RPC Manager...");
    this.isInitialized = false;
    this.nodes.clear();
    this.nodeUpdateLocks.clear();
    this.fastHealthCheckPromises.clear();

    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
  }

  /**
   * 初始化 RPC 节点
   */
  async initialize(rpcUrls: string[]) {
    if (this.isInitialized) return;

    // 初始化节点
    rpcUrls.forEach((url, index) => {
      // 根据 URL 推断网络类型和优先级
      const networkType = this.inferNetworkType(url);
      const priority = this.calculateInitialPriority(url, index);

      this.nodes.set(url, {
        url,
        isHealthy: true,
        lastCheck: 0,
        consecutiveFailures: 0,
        responseTime: 0,
        weight: 1,
        // 新增字段初始化
        chainId: undefined,
        isChainValid: undefined,
        networkType,
        avgResponseTime: 0,
        successRate: 1.0,
        totalRequests: 0,
        successfulRequests: 0,
        lastSuccessTime: 0,
        priority,
      });
    });

    // 初始健康检查
    await this.performHealthCheck();

    // 启动定期健康检查
    this.startHealthCheck();

    this.isInitialized = true;
  }

  /**
   * 获取最佳 RPC 节点（智能选择算法）
   */
  getBestNode(): string | null {
    // 首先过滤出健康且链ID有效的节点
    const validNodes = Array.from(this.nodes.values()).filter(
      node => node.isHealthy && (node.isChainValid === true || node.isChainValid === undefined),
    );

    if (validNodes.length === 0) {
      console.warn("⚠️ No valid RPC nodes available");
      return null;
    }

    // 计算综合评分并排序
    const scoredNodes = validNodes.map(node => {
      let score = 0;

      // 优先级权重 (40%)
      score += (node.priority / 10) * 40;

      // 成功率权重 (30%)
      score += node.successRate * 30;

      // 响应时间权重 (20%) - 响应时间越短分数越高
      const maxResponseTime = 5000; // 5秒作为最大响应时间
      const responseTimeScore = Math.max(0, (maxResponseTime - node.avgResponseTime) / maxResponseTime);
      score += responseTimeScore * 20;

      // 最近成功时间权重 (10%) - 最近成功的节点优先
      const timeSinceLastSuccess = Date.now() - node.lastSuccessTime;
      const maxTimeSinceSuccess = 300000; // 5分钟
      const recentSuccessScore = Math.max(0, (maxTimeSinceSuccess - timeSinceLastSuccess) / maxTimeSinceSuccess);
      score += recentSuccessScore * 10;

      // 快速故障转移加分：如果节点最近刚恢复，给予额外加分
      if (this.config.fastFailoverEnabled && node.lastSuccessTime > 0) {
        const timeSinceRecovery = Date.now() - node.lastSuccessTime;
        if (timeSinceRecovery < 10000) {
          // 10秒内恢复的节点
          score += 5; // 额外5分
        }
      }

      return { node, score };
    });

    // 按分数排序，分数高的优先
    scoredNodes.sort((a, b) => b.score - a.score);

    const bestNode = scoredNodes[0].node;

    console.log(`🎯 Selected best RPC node:`, {
      url: bestNode.url.split("/").pop(),
      network: bestNode.networkType,
      chainId: bestNode.chainId,
      score: scoredNodes[0].score.toFixed(1),
      priority: bestNode.priority,
      successRate: `${(bestNode.successRate * 100).toFixed(1)}%`,
      avgResponseTime: `${bestNode.avgResponseTime.toFixed(0)}ms`,
    });

    return bestNode.url;
  }

  /**
   * 执行 RPC 请求（带重试和故障转移）
   */
  async executeRequest<T>(method: string, params: any[] = [], maxRetries?: number): Promise<T> {
    const retries = maxRetries ?? this.config.maxRetries;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      const nodeUrl = this.getBestNode();

      if (!nodeUrl) {
        throw new Error("No healthy RPC nodes available");
      }

      try {
        const result = await this.makeRequest<T>(nodeUrl, method, params);

        // 请求成功，重置失败计数
        const node = this.nodes.get(nodeUrl);
        if (node) {
          node.consecutiveFailures = 0;
          node.isHealthy = true;
        }

        return result;
      } catch (error) {
        lastError = error as Error;

        // 立即标记节点失败
        await this.markNodeFailure(nodeUrl, lastError);

        // 如果启用了快速故障转移，立即触发健康检查
        if (this.config.fastFailoverEnabled) {
          console.log(`⚡ Fast failover triggered for ${nodeUrl}`);
          // 异步执行快速健康检查，使用去重机制防止检查风暴
          this.triggerFastHealthCheck(nodeUrl);
        }

        // 如果启用立即重试，减少延迟
        if (attempt < retries) {
          const delay = this.config.immediateRetryOnFailure
            ? Math.min(this.config.retryDelay, 500) // 立即重试时最多延迟500ms
            : this.config.retryDelay * Math.pow(2, attempt);

          await this.delay(delay);
        }
      }
    }

    throw lastError || new Error("All RPC requests failed");
  }

  /**
   * 执行单个 RPC 请求
   */
  private async makeRequest<T>(nodeUrl: string, method: string, params: any[]): Promise<T> {
    const startTime = Date.now();
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), this.config.requestTimeout);

    try {
      const response = await fetch(nodeUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          jsonrpc: "2.0",
          method,
          params,
          id: Date.now(),
        }),
        signal: controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(`RPC Error: ${data.error.message}`);
      }

      // 更新响应时间和统计信息
      const responseTime = Date.now() - startTime;
      const node = this.nodes.get(nodeUrl);
      if (node) {
        node.responseTime = responseTime;
        node.lastCheck = Date.now();
      }

      // 更新统计信息
      this.updateNodeStats(nodeUrl, true, responseTime);

      return data.result;
    } catch (error: any) {
      // 特别处理 CORS 错误
      if (
        error.message?.includes("CORS") ||
        error.message?.includes("Access-Control-Allow-Origin") ||
        (error.name === "TypeError" && error.message?.includes("Failed to fetch"))
      ) {
        console.warn(`🚫 RPC node ${nodeUrl} blocked by CORS policy or network error`);
      }
      throw error;
    } finally {
      clearTimeout(timeout);
    }
  }

  /**
   * 标记节点失败（带锁保护）
   */
  private async markNodeFailure(nodeUrl: string, error?: Error) {
    // 使用锁防止竞态条件
    await this.withNodeLock(nodeUrl, async () => {
      const node = this.nodes.get(nodeUrl);
      if (!node) return;

      // 检查是否是 429 错误，如果是则立即标记为不健康
      if (error && (error.message?.includes("429") || error.message?.includes("Too Many Requests"))) {
        console.warn(`⏰ RPC node ${nodeUrl} rate limited (429), marking as unhealthy immediately`);
        node.isHealthy = false;
        node.consecutiveFailures = this.config.maxConsecutiveFailures; // 直接设置为最大失败次数
      } else {
        node.consecutiveFailures++;

        // 连续失败超过阈值，标记为不健康
        if (node.consecutiveFailures >= this.config.maxConsecutiveFailures) {
          node.isHealthy = false;
          console.warn(`🚫 RPC node marked as unhealthy: ${nodeUrl} (failures: ${node.consecutiveFailures})`);
        }
      }

      node.lastCheck = Date.now();
      // 更新失败统计
      this.updateNodeStats(nodeUrl, false, 0);
    });
  }

  /**
   * 节点状态更新锁
   */
  private async withNodeLock<T>(nodeUrl: string, operation: () => Promise<T> | T): Promise<T> {
    // 等待现有的锁释放
    const existingLock = this.nodeUpdateLocks.get(nodeUrl);
    if (existingLock) {
      await existingLock;
    }

    // 创建新的锁
    const lockPromise = (async () => {
      try {
        return await operation();
      } finally {
        // 操作完成后移除锁
        this.nodeUpdateLocks.delete(nodeUrl);
      }
    })();

    this.nodeUpdateLocks.set(nodeUrl, lockPromise as Promise<void>);
    return lockPromise;
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck() {
    const expectedChainId = process.env.NEXT_PUBLIC_CHAIN_ID;

    const checkPromises = Array.from(this.nodes.entries()).map(async ([url, node]) => {
      const startTime = Date.now();

      try {
        const chainId = await this.makeRequest(url, "eth_chainId", []);
        const responseTime = Date.now() - startTime;

        if (typeof chainId === "string") {
          const actualChainId = parseInt(chainId, 16).toString();
          node.chainId = actualChainId;

          // 验证链ID是否匹配期望值
          if (expectedChainId) {
            const expectedChainIdStr = expectedChainId.toString();
            const isChainValid = actualChainId === expectedChainIdStr;
            node.isChainValid = isChainValid;

            if (!isChainValid) {
              // 链ID不匹配，标记为不健康但不增加失败计数（这不是节点故障）
              node.isHealthy = false;
              return;
            }
          } else {
            // 没有配置期望链ID，跳过验证
            node.isChainValid = true;
          }
        }

        // 健康检查通过
        node.isHealthy = true;
        node.consecutiveFailures = 0;
        this.updateNodeStats(url, true, responseTime);
      } catch (error: any) {
        // 检查是否是 429 错误
        if (error.message?.includes("429") || error.message?.includes("Too Many Requests")) {
          console.warn(`⏰ Health check: RPC node ${url} rate limited (429), marking as unhealthy`);
          node.isHealthy = false;
          node.consecutiveFailures = this.config.maxConsecutiveFailures; // 直接设置为最大失败次数
        } else {
          console.warn(`❌ Health check failed for ${url}:`, error);
          await this.markNodeFailure(url, error);
        }
      }
    });

    await Promise.allSettled(checkPromises);

    // 输出健康检查摘要
    this.logHealthCheckSummary();
  }

  /**
   * 触发快速健康检查（带去重机制）
   */
  private triggerFastHealthCheck(nodeUrl: string) {
    // 如果已经有正在进行的快速检查，直接返回
    if (this.fastHealthCheckPromises.has(nodeUrl)) {
      console.log(`⏭️ Fast health check already in progress for ${nodeUrl}`);
      return;
    }

    // 创建快速检查 Promise
    const checkPromise = this.performFastHealthCheck(nodeUrl)
      .catch((err: any) => console.warn("Fast health check failed:", err))
      .finally(() => {
        // 检查完成后移除 Promise
        this.fastHealthCheckPromises.delete(nodeUrl);
      });

    this.fastHealthCheckPromises.set(nodeUrl, checkPromise);
  }

  /**
   * 快速健康检查（针对特定节点）
   * 用于故障转移时立即检测节点状态
   */
  private async performFastHealthCheck(nodeUrl: string) {
    const node = this.nodes.get(nodeUrl);
    if (!node) return;

    console.log(`⚡ Fast health check for ${nodeUrl}`);
    const startTime = Date.now();

    try {
      // 使用更短的超时时间进行快速检测
      const controller = new AbortController();
      const timeout = setTimeout(() => controller.abort(), this.config.fastFailoverTimeout);

      const response = await fetch(nodeUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          jsonrpc: "2.0",
          method: "eth_chainId",
          params: [],
          id: Date.now(),
        }),
        signal: controller.signal,
      });

      clearTimeout(timeout);

      if (response.ok) {
        const data = await response.json();
        if (data.result && !data.error) {
          const responseTime = Date.now() - startTime;

          // 快速恢复节点状态
          node.isHealthy = true;
          node.consecutiveFailures = 0;
          this.updateNodeStats(nodeUrl, true, responseTime);

          console.log(`✅ Fast recovery: ${nodeUrl} is back online (${responseTime}ms)`);
          return;
        }
      }

      // 快速检查失败，确认节点不健康
      throw new Error("Fast health check failed");
    } catch (error) {
      console.warn(`❌ Fast health check failed for ${nodeUrl}:`, error);
      // 不需要再次调用 markNodeFailure，因为在 executeRequest 中已经调用过了
    }
  }

  /**
   * 启动定期健康检查
   */
  private startHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);
  }

  /**
   * 获取节点状态
   */
  getNodesStatus() {
    return Array.from(this.nodes.values()).map(node => ({
      url: node.url,
      isHealthy: node.isHealthy,
      consecutiveFailures: node.consecutiveFailures,
      responseTime: node.responseTime,
      lastCheck: new Date(node.lastCheck).toISOString(),
    }));
  }

  /**
   * 销毁管理器
   */
  destroy() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }

    // 清理所有锁和 Promise
    this.nodeUpdateLocks.clear();
    this.fastHealthCheckPromises.clear();

    // 清理节点数据
    this.nodes.clear();
    this.isInitialized = false;

    console.log("🧹 RPC Manager destroyed and cleaned up");
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 根据 URL 推断网络类型
   */
  private inferNetworkType(url: string): string {
    const urlLower = url.toLowerCase();

    if (urlLower.includes("sepolia")) return "sepolia";
    if (urlLower.includes("base")) return "base";
    if (urlLower.includes("mainnet") || urlLower.includes("ethereum")) return "mainnet";
    if (urlLower.includes("polygon")) return "polygon";
    if (urlLower.includes("arbitrum")) return "arbitrum";
    if (urlLower.includes("optimism")) return "optimism";

    return "unknown";
  }

  /**
   * 计算初始优先级
   */
  private calculateInitialPriority(url: string, index: number): number {
    let priority = 5; // 默认优先级

    // 根据 URL 特征调整优先级
    const urlLower = url.toLowerCase();

    // 知名服务商优先级更高
    if (urlLower.includes("alchemy")) priority += 2;
    if (urlLower.includes("infura")) priority += 2;
    if (urlLower.includes("quicknode")) priority += 1;
    if (urlLower.includes("publicnode")) priority += 1;

    // 配置顺序也影响优先级（越靠前优先级越高）
    priority += Math.max(0, 3 - index);

    return Math.min(10, Math.max(1, priority));
  }

  /**
   * 更新节点统计信息
   */
  private updateNodeStats(nodeUrl: string, success: boolean, responseTime: number) {
    const node = this.nodes.get(nodeUrl);
    if (!node) return;

    node.totalRequests++;
    if (success) {
      node.successfulRequests++;
      node.lastSuccessTime = Date.now();
    }

    // 更新成功率
    node.successRate = node.successfulRequests / node.totalRequests;

    // 更新平均响应时间（指数移动平均）
    if (node.avgResponseTime === 0) {
      node.avgResponseTime = responseTime;
    } else {
      node.avgResponseTime = node.avgResponseTime * 0.8 + responseTime * 0.2;
    }
  }

  /**
   * 输出健康检查摘要
   */
  private logHealthCheckSummary() {
    // const nodes = Array.from(this.nodes.values());
    // const healthyNodes = nodes.filter(n => n.isHealthy);
    // const validChainNodes = nodes.filter(n => n.isChainValid === true);
    // const invalidChainNodes = nodes.filter(n => n.isChainValid === false);
    // 详细的节点状态
    // if (validChainNodes.length > 0) {
    //   console.log(
    //     `✅ Valid nodes (${validChainNodes.length}):`,
    //     validChainNodes.map(n => ({
    //       url: n.url.split("/").pop(),
    //       network: n.networkType,
    //       chainId: n.chainId,
    //       responseTime: `${n.avgResponseTime.toFixed(0)}ms`,
    //       successRate: `${(n.successRate * 100).toFixed(1)}%`,
    //     })),
    //   );
    // }
  }
}

// 单例实例
export const rpcManager = new RpcManager();
