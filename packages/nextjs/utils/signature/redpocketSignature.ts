import { getCurrentTimestamp } from "@/utils";
import { ethers } from "ethers";
import createRedPocket from "~~/api/signature/createRedPocket";

export const handleCreateRedPocketSignatureWithMagic = async (magicInstance: any, password: string) => {
  if (!magicInstance) {
    console.error("Magic instance is required");
    return;
  }

  try {
    // 使用 Magic 连接
    const provider = new ethers.BrowserProvider(magicInstance.rpcProvider);
    const signer = await provider.getSigner();
    const userAddress = await signer.getAddress();

    // 验证 userAddress 是否为有效的以太坊地址
    if (!/^0x[0-9a-fA-F]{40}$/.test(userAddress)) {
      throw new Error("Invalid Ethereum address");
    }

    const timestamp = getCurrentTimestamp().toString();

    const domain = {
      name: "ClobAuthDomain",
      version: "1",
      chainId: Number(process.env.NEXT_PUBLIC_CHAIN_ID),
      verifyingContract: "******************************************",
    };

    const types = {
      EIP712Domain: [
        { name: "name", type: "string" },
        { name: "version", type: "string" },
        { name: "chainId", type: "uint256" },
        { name: "verifyingContract", type: "address" },
      ],
      ClobAuth: [
        { name: "address", type: "address" },
        { name: "timestamp", type: "string" },
        { name: "nonce", type: "uint256" },
        { name: "message", type: "string" },
      ],
    };

    const value = {
      address: userAddress.toLowerCase(),
      timestamp: timestamp,
      nonce: 0,
      message: "This message attests that I control the given wallet",
    };

    const signature = await signer.signTypedData(domain, { ClobAuth: types.ClobAuth }, value);

    // 调用后端 API 验证签名
    const response = await createRedPocket({
      address: userAddress,
      signature: signature,
      timestamp: timestamp,
      password: password,
    });

    // 检查响应是否成功
    if (response.success) {
      return response;
    } else {
      // API返回失败，但不是网络错误
      return {
        success: false,
        message: response.message || "Claim failed",
        error: response.error || response.message,
        details: response.details || response,
      };
    }
  } catch (error: any) {
    console.error("Magic CLOB auth error:", error);

    // 处理网络错误或API错误响应
    if (error.response?.data) {
      // API返回了错误响应
      return {
        success: false,
        message: error.response.data.error || error.response.data.message || "API Error",
        error: error.response.data.error || error.response.data.message,
        details: error.response.data.details || error.response.data,
      };
    } else {
      // 网络错误或其他异常
      return {
        success: false,
        message: error.message || "Network Error",
        error: error.message || "Network Error",
        details: null,
      };
    }
  }
};
