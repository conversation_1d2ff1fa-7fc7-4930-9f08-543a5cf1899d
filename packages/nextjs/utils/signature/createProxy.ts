import { getItem } from "@/utils";
import { mapSocialMediaData } from "@/utils/signature";
import { ethers } from "ethers";
import createProxyWallet from "~~/api/signature/createProxyWallet";

// MetaMask
export const handleCreateProxyWallet = async (
  session: any,
  proxyWallet: string,
  setIsCreatedProxy: any,
  setIsBtnLoading: any,
  setIsActiveWallet: any, // 保留参数以保持向后兼容性，但现在由 checkProxyWalletStatus 处理
  setShowTooltip: any,
) => {
  if (typeof window.ethereum !== "undefined") {
    try {
      if (!session || !session.cookie) {
        setShowTooltip(true);
        setTimeout(() => setShowTooltip(false), 3000);
        return;
      }
      setIsBtnLoading(true);
      // 请求用户连接 MetaMask
      await window.ethereum.request({ method: "eth_requestAccounts" });
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      const types = {
        EIP712Domain: [
          { name: "name", type: "string" },
          { name: "chainId", type: "uint256" },
          { name: "verifyingContract", type: "address" },
        ],
        CreateProxy: [
          { name: "paymentToken", type: "address" },
          { name: "payment", type: "uint256" },
          { name: "paymentReceiver", type: "address" },
        ],
      };

      const value = {
        paymentToken: "******************************************",
        payment: "0",
        paymentReceiver: "******************************************",
      };

      const msgParams = JSON.stringify({
        types,
        primaryType: "CreateProxy",
        domain: {
          name: "PredictOne Contract Proxy Factory",
          chainId: process.env.NEXT_PUBLIC_CHAIN_ID,
          verifyingContract: process.env.NEXT_PUBLIC_FACTORY_ADDRESS,
        },
        message: value,
      });

      const signature = await provider.send("eth_signTypedData_v4", [await signer.getAddress(), msgParams]);

      const inviteCode = getItem("invitationCode");

      const response = await createProxyWallet({
        from: await signer.getAddress(),
        proxyWallet: proxyWallet,
        signature: signature,
        cookie: session.cookie,
        inviteCode: inviteCode || undefined,
      });

      if (response.state === "STATE_NEW") {
        setIsBtnLoading(false);
        setIsCreatedProxy(true);
      } else {
        setIsBtnLoading(false);
        console.error("signature verification failed");
      }
    } catch (error) {
      setIsBtnLoading(false);
      console.error("Failed to connect wallet or sign:", error);
    }
  } else {
    setIsBtnLoading(false);
    console.error("MetaMask is not installed");
  }
};

// Magic
export const handleCreateProxyWalletWithMagic = async (
  session: any,
  proxyWallet: string,
  setIsCreatedProxy: any,
  setIsBtnLoading: any,
  setIsActiveWallet: any, // 保留参数以保持向后兼容性，但现在由 checkProxyWalletStatus 处理
  setShowTooltip: any,
  magicInstance: any,
  oauthResult?: any,
) => {
  if (!magicInstance) {
    console.error("Magic instance is required");
    setIsBtnLoading(false);
    return;
  }

  try {
    if (!session || !session.cookie) {
      setShowTooltip(true);
      setTimeout(() => setShowTooltip(false), 3000);
      return;
    }

    setIsBtnLoading(true);
    console.log("开始使用 Magic 创建代理钱包...");

    // 使用 Magic 连接
    const provider = new ethers.BrowserProvider(magicInstance.rpcProvider);
    const signer = await provider.getSigner();
    const userAddress = await signer.getAddress();

    // 验证 userAddress 是否为有效的以太坊地址
    if (!/^0x[0-9a-fA-F]{40}$/.test(userAddress)) {
      throw new Error("Invalid Ethereum address");
    }

    const domain = {
      name: "PredictOne Contract Proxy Factory",
      chainId: Number(process.env.NEXT_PUBLIC_CHAIN_ID),
      verifyingContract: process.env.NEXT_PUBLIC_FACTORY_ADDRESS,
    };

    const types = {
      EIP712Domain: [
        { name: "name", type: "string" },
        { name: "chainId", type: "uint256" },
        { name: "verifyingContract", type: "address" },
      ],
      CreateProxy: [
        { name: "paymentToken", type: "address" },
        { name: "payment", type: "uint256" },
        { name: "paymentReceiver", type: "address" },
      ],
    };

    const value = {
      paymentToken: "******************************************",
      payment: "0",
      paymentReceiver: "******************************************",
    };

    console.log("开始签名...");
    const signature = await signer.signTypedData(domain, { CreateProxy: types.CreateProxy }, value);
    console.log("签名完成:", signature);

    const inviteCode = getItem("invitationCode");

    let socialMediaData;
    if (oauthResult?.oauth?.userInfo) {
      const { userInfo, provider } = oauthResult.oauth;
      socialMediaData = mapSocialMediaData(userInfo, provider);
      console.log(`添加 ${provider} 社交媒体数据:`, socialMediaData);
    }

    const requestData = {
      from: userAddress,
      proxyWallet: proxyWallet,
      signature: signature,
      cookie: session.cookie,
      inviteCode: inviteCode || undefined,
      socialMedia: socialMediaData,
    };

    console.log("调用创建代理钱包 API...", requestData);

    // 添加超时控制和更详细的错误处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 30秒超时

    try {
      const response = await Promise.race([
        createProxyWallet(requestData),
        new Promise((_, reject) => setTimeout(() => reject(new Error("请求超时")), 30000)),
      ]);

      clearTimeout(timeoutId);
      console.log("API 响应:", response);

      if (response && response.state === "STATE_NEW") {
        setIsBtnLoading(false);
        setIsCreatedProxy(true);
        // 不立即设置 setIsActiveWallet(true)，让 checkProxyWalletStatus 来处理
        // setIsActiveWallet(true);
      } else {
        const errorMsg = response?.message || "创建代理钱包失败，未知原因";
        console.error("API 返回错误:", response);
        setIsBtnLoading(false);
        throw new Error(errorMsg);
      }
    } catch (apiError) {
      clearTimeout(timeoutId);
      console.error("API 调用失败:", apiError);
    }
  } catch (error) {
    setIsBtnLoading(false);
    console.error("Magic 创建代理钱包失败:", error);
    throw error; // 重新抛出错误以便上层处理
  }
};
