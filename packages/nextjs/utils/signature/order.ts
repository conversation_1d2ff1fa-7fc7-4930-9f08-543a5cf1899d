import createOrder from "@/api/signature/createOrder";
import computeProxyAddress from "@/contracts/computeProxyAddress";
import { generateOrderSalt, getCurrentTimestamp, getItem } from "@/utils";
import { getSignature } from "@/utils/other/signature";
import { OAuthExtension } from "@magic-ext/oauth";
import { Side as UtilsSide } from "@polymarket/order-utils";
import { ethers } from "ethers";
import { Magic } from "magic-sdk";
import { useGlobalState } from "~~/services/store/store";

export const handleMakeOrder = async (
  postParams: any,
  setEnableState: any,
  setShowNotification: any,
  setMakeOrderStatus: any,
  setOrderError: any,
  onSuccess?: () => void,
) => {
  try {
    setEnableState("loading");
    const { walletType } = useGlobalState.getState();

    let provider: ethers.BrowserProvider;
    let signer: ethers.Signer;
    let userAddress: string;

    // 根据钱包类型初始化不同的 provider 和 signer
    if (walletType === "metamask") {
      if (typeof window.ethereum === "undefined") {
        throw new Error("MetaMask is not installed");
      }

      await window.ethereum.request({ method: "eth_requestAccounts" });
      provider = new ethers.BrowserProvider(window.ethereum);
      signer = await provider.getSigner();
      userAddress = await signer.getAddress();
    } else if (walletType === "magic") {
      const magicApiKey = process.env.NEXT_PUBLIC_MAGIC_API_KEY;
      if (!magicApiKey) {
        throw new Error("Magic API key is not defined");
      }

      const magic = new Magic(magicApiKey, {
        extensions: [new OAuthExtension()],
      });

      provider = new ethers.BrowserProvider(magic.rpcProvider as any);
      signer = await provider.getSigner();

      const userInfo = await magic.user.getInfo();
      if (!userInfo.publicAddress) {
        throw new Error("User not logged in to Magic");
      }
      userAddress = userInfo.publicAddress;
    } else {
      throw new Error("Unsupported wallet type");
    }

    const timestamp = getCurrentTimestamp();

    const types = {
      EIP712Domain: [
        { name: "name", type: "string" },
        { name: "version", type: "string" },
        { name: "chainId", type: "uint256" },
        { name: "verifyingContract", type: "address" },
      ],
      Order: [
        { name: "salt", type: "uint256" },
        { name: "maker", type: "address" },
        { name: "signer", type: "address" },
        { name: "taker", type: "address" },
        { name: "tokenId", type: "uint256" },
        { name: "makerAmount", type: "uint256" },
        { name: "takerAmount", type: "uint256" },
        { name: "expiration", type: "uint256" },
        { name: "nonce", type: "uint256" },
        { name: "feeRateBps", type: "uint256" },
        { name: "side", type: "uint8" },
        { name: "signatureType", type: "uint8" },
      ],
    };

    const newSide = postParams.side === "Buy" ? UtilsSide.BUY : UtilsSide.SELL;
    const currentMakerAmount =
      newSide === UtilsSide.BUY ? Math.floor(postParams.payment) : Math.floor(postParams.shares * 10 ** 6);

    const currentTakerAmount =
      postParams.type === "GTC"
        ? newSide === UtilsSide.BUY
          ? Math.floor(postParams.shares * 10 ** 6)
          : Math.floor(postParams.payment)
        : postParams.type === "MARKET"
        ? newSide === UtilsSide.BUY
          ? Math.floor((Math.floor((postParams.shares / 2) * 100) / 100) * 10 ** 6)
          : Math.floor(postParams.payment / 2)
        : newSide === UtilsSide.BUY
        ? Math.floor(postParams.shares * 10 ** 6)
        : Math.floor(postParams.payment);

    const clobApis = getItem("poly_clob_api_key_map")[userAddress] || {};
    const value = {
      salt: generateOrderSalt(),
      maker: await computeProxyAddress(userAddress),
      signer: userAddress,
      taker: "0x0000000000000000000000000000000000000000",
      tokenId: postParams.tokenId,
      makerAmount: currentMakerAmount,
      takerAmount: currentTakerAmount,
      side: newSide,
      expiration: 0,
      nonce: 0,
      feeRateBps: 0,
      signatureType: 2, // TODO:不知道值的映射是什么，先写死
    };

    const msgParams = JSON.stringify({
      types,
      primaryType: "Order",
      domain: {
        name: "Prediction One CTF Exchange",
        version: "1",
        chainId: Number(process.env.NEXT_PUBLIC_CHAIN_ID),
        verifyingContract: process.env.NEXT_PUBLIC_ORDER_VERIFY_CONTRACT,
      },
      message: value,
    });

    function convertValuesToString(obj: any): any {
      const result: any = {};
      for (const key in obj) {
        if (key === "signatureType") {
          result[key] = obj[key];
        } else {
          result[key] = obj[key].toString();
        }
      }
      return result;
    }

    const signature = await getSignature(msgParams, walletType, "eth_signTypedData_v4");

    const signedOrder = convertValuesToString({
      ...value,
      signature: signature,
    });

    const response = await createOrder({
      value: signedOrder,
      clobApis: clobApis,
      timestamp: timestamp,
      signer: signer as any,
      type: postParams.type,
    });

    if (response.success === true) {
      setEnableState("success");
      setShowNotification(true);
      setMakeOrderStatus("success");
      if (typeof onSuccess === "function") {
        onSuccess();
      }
    } else {
      setEnableState("none");
      setShowNotification(true);
      setMakeOrderStatus("failure");
    }
  } catch (error: any) {
    setEnableState("none");
    setShowNotification(true);

    if (error.response) {
      setMakeOrderStatus("failure");
      setOrderError(error.response.data);
    } else {
      setMakeOrderStatus("canceled");
      setOrderError("Order canceled");
    }
  }
};
