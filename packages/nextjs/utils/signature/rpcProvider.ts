/**
 * @deprecated 此文件已弃用
 * 请使用新的 RPC 管理器：@/utils/rpc/rpcClient
 * 新的实现提供了更好的性能、可靠性和负载均衡
 */
import { useGlobalState } from "@/services/store/store";
import { getItem, setItem } from "@/utils";
import { addToast } from "@heroui/react";
import backoff from "backoff";

const providerUrls = JSON.parse(process.env.NEXT_PUBLIC_PROVIDER_URLS || "[]");

let isToastDisplayed = false;
const LAST_WORKING_RPC_KEY = "lastWorkingRpc";
const LAST_WORKING_RPC_TIME_KEY = "lastWorkingRpcTime";
const CACHE_DURATION = 3 * 60 * 1000;

let fetchingPromise: Promise<string> | null = null;

function setRpcCache(url: string) {
  setItem(LAST_WORKING_RPC_KEY, url);
  setItem(LAST_WORKING_RPC_TIME_KEY, Date.now().toString());
}

function getRpcCache() {
  const url = getItem(LAST_WORKING_RPC_KEY);
  const time = Number(getItem(LAST_WORKING_RPC_TIME_KEY));
  if (url && time && Date.now() - time < CACHE_DURATION) {
    return url;
  }
  return null;
}

// 已弃用：使用新的 RPC 管理器替代

export async function fetchWithFailover() {
  const setRpcError = useGlobalState.getState().setRpcError;

  // providerUrls 为空直接 reject
  if (!Array.isArray(providerUrls) || providerUrls.length === 0) {
    setRpcError(true);
    throw new Error("No RPC provider URLs configured.");
  }

  // 检查是否已有检测中的Promise
  if (fetchingPromise) {
    return fetchingPromise;
  }

  // 优先用缓存且未过期
  const cachedRpc = getRpcCache();
  if (cachedRpc) {
    setRpcError(false);
    isToastDisplayed = false;
    return cachedRpc;
  }

  // 检测逻辑
  fetchingPromise = (async () => {
    const lastRpc = getItem(LAST_WORKING_RPC_KEY);

    if (lastRpc) {
      try {
        const response = await testRpcConnection(lastRpc);
        if (response) {
          setRpcError(false);
          isToastDisplayed = false;
          setRpcCache(lastRpc);
          fetchingPromise = null;
          return lastRpc;
        } else {
          setItem(LAST_WORKING_RPC_KEY, "");
          setItem(LAST_WORKING_RPC_TIME_KEY, "");
          for (let i = 0; i < providerUrls.length; i++) {
            const providerUrl = providerUrls[i];
            if (providerUrl === lastRpc) continue;
            try {
              const ok = await testRpcConnection(providerUrl);
              if (ok) {
                setRpcError(false);
                isToastDisplayed = false;
                setRpcCache(providerUrl);
                fetchingPromise = null;
                return providerUrl;
              }
            } catch (err) {}
          }
        }
      } catch (error) {
        setRpcError(true);
        if (!isToastDisplayed) {
          addToast({
            title: "RPC Error",
            description: "All RPC services are unavailable. Please try again later.",
            timeout: 5000,
          });
          isToastDisplayed = true;
        }
        fetchingPromise = null;
      }
    } else {
      for (let i = 0; i < providerUrls.length; i++) {
        const providerUrl = providerUrls[i];
        try {
          const ok = await testRpcConnection(providerUrl);
          if (ok) {
            setRpcError(false);
            isToastDisplayed = false;
            setRpcCache(providerUrl);
            fetchingPromise = null;
            return providerUrl;
          }
        } catch (err) {}
      }
    }

    // backoff流程
    return await new Promise<string>((resolve, reject) => {
      let index = 0;
      let lastError: any = null;

      const call = backoff.call(
        async (cb: (err: Error | null, result?: string) => void) => {
          if (index >= providerUrls.length) {
            cb(lastError || new Error("All RPC providers failed"));
            return;
          }
          const providerUrl = providerUrls[index];
          try {
            const ok = await testRpcConnection(providerUrl);
            if (ok) {
              cb(null, providerUrl);
            } else {
              lastError = new Error(`Provider ${providerUrl} not available`);
              index++;
              cb(lastError);
            }
          } catch (err: any) {
            lastError = err;
            index++;
            cb(err);
          }
        },
        (err, result) => {
          fetchingPromise = null; // 检测结束，释放锁
          if (result) {
            setRpcError(false);
            isToastDisplayed = false;
            setRpcCache(result as string);
            resolve(result as string);
          } else {
            setRpcError(true);
            if (!isToastDisplayed) {
              addToast({
                title: "RPC Error",
                description: "All RPC services are unavailable. Please try again later.",
                timeout: 5000,
              });
              isToastDisplayed = true;
            }
            reject(err || new Error("All RPC providers failed"));
          }
        },
      );

      call.setStrategy(new backoff.ExponentialStrategy({ initialDelay: 1000, maxDelay: 4000 }));
      call.failAfter(providerUrls.length * 3);
      call.start();
    });
  })();

  return fetchingPromise;
}

// Helper function to test RPC connection
const ENV_CHAIN_ID = process.env.NEXT_PUBLIC_CHAIN_ID ? Number(process.env.NEXT_PUBLIC_CHAIN_ID) : undefined;

async function testRpcConnection(providerUrl: string): Promise<boolean> {
  const controller = new AbortController();
  let timeout: NodeJS.Timeout | null = null;
  try {
    timeout = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(providerUrl, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        jsonrpc: "2.0",
        method: "eth_chainId",
        params: [],
        id: 1,
      }),
      signal: controller.signal,
    });

    if (timeout) clearTimeout(timeout);

    if (!response.ok || response.status === 408) {
      return false;
    }

    let data: any;
    try {
      data = await response.json();
    } catch {
      return false;
    }
    if (typeof data === "object" && data !== null && data.jsonrpc === "2.0" && typeof data.result === "string") {
      // Verify that chainId matches
      if (ENV_CHAIN_ID !== undefined) {
        const rpcChainId = parseInt(data.result, 16);
        if (rpcChainId !== ENV_CHAIN_ID) {
          return false;
        }
      }
      return true;
    }
    return false;
  } catch (error: any) {
    return false;
  } finally {
    if (timeout) clearTimeout(timeout);
  }
}
