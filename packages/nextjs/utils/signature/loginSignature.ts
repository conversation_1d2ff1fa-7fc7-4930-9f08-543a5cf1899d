import { generateRandomNonce, getItem, setItem } from "@/utils";
import { Web3Provider } from "@ethersproject/providers";
import { OAuthExtension } from "@magic-ext/oauth";
import { Magic } from "magic-sdk";
import { signIn } from "next-auth/react";
import { SiweMessage } from "siwe";

type WalletType = "metamask" | "magic";

export const signInWithSiwe = async (
  address: string,
  walletType: WalletType,
  signMessageAsync?: (params: { message: string }) => Promise<string>,
  didToken?: string,
  setIsShowSignatureModal?: (value: boolean) => void,
  onClose?: () => void,
  setLoadStatus?: (value: string) => void,
) => {
  setLoadStatus?.("loading");
  const nonce = generateRandomNonce(16);

  try {
    const siweMessage = new SiweMessage({
      address: address,
      chainId: Number(process.env.NEXT_PUBLIC_CHAIN_ID),
      nonce: nonce,
      domain: process.env.NEXT_PUBLIC_LOGIN_DOMAIN,
      expirationTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      uri: process.env.NEXT_PUBLIC_LOGIN_URL,
      statement: "Welcome to Prediction One! Sign to connect.",
      version: "1",
    });

    const message = siweMessage.prepareMessage();
    let signature: string;

    // 根据钱包类型选择不同的签名方法
    if (walletType === "magic") {
      if (!didToken) {
        throw new Error("Magic 登录需要 didToken");
      }

      const magicApiKey = process.env.NEXT_PUBLIC_MAGIC_API_KEY;
      if (!magicApiKey) {
        throw new Error("Magic API key is not defined in environment variables");
      }
      const magic = new Magic(magicApiKey, {
        extensions: [new OAuthExtension()],
      });

      // 使用 ethers 包装 Magic Provider
      const provider = new Web3Provider(magic.rpcProvider as any);
      const signer = provider.getSigner();
      signature = await signer.signMessage(message);
    } else {
      // MetaMask 等钱包签名
      if (!signMessageAsync) {
        throw new Error("MetaMask 登录需要 signMessageAsync 函数");
      }
      signature = await signMessageAsync({ message });
    }

    // 发送到后端验证
    const credentials: any = {
      message,
      signature,
      redirect: false,
    };

    await signIn("credentials", credentials);

    const signatureStatus = await getItem(`login_signed_${address}`);
    if (!signatureStatus) {
      setItem(`login_signed_${address}`, true);
      setIsShowSignatureModal?.(false);
    }
    setLoadStatus?.("none");
    onClose?.();
  } catch (error) {
    if ((error as any).code === 4001) {
      console.error("用户拒绝了请求");
      setLoadStatus?.("failed");
    } else {
      console.error("发生错误:", error);
      setLoadStatus?.("failed");
    }
  }
};

// 为了向后兼容，保留原函数（仅支持 MetaMask）
export const signInWithSiweMetaMask = async (
  address: string,
  signMessageAsync: (params: { message: string }) => Promise<string>,
  setIsShowSignatureModal: (value: boolean) => void,
  onClose: () => void,
  setLoadStatus: (value: string) => void,
) => {
  return signInWithSiwe(
    address,
    "metamask",
    signMessageAsync,
    undefined,
    setIsShowSignatureModal,
    onClose,
    setLoadStatus,
  );
};
