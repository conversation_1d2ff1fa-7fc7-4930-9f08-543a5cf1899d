import { ethers } from "ethers";

export interface SignatureMessage {
  address: string;
  timestamp: string;
  nonce: number;
  action: string;
  message: string;
}

const MYSTERY_BOX_SIGNATURE_CONFIG = {
  domain: {
    name: "MysteryBoxDomain",
    version: "1",
    chainId: "11155111",
    verifyingContract: "******************************************",
  },
  types: {
    EIP712Domain: [
      { name: "name", type: "string" },
      { name: "version", type: "string" },
      { name: "chainId", type: "uint256" },
      { name: "verifyingContract", type: "address" },
    ],
    MysteryBoxAuth: [
      { name: "address", type: "address" },
      { name: "timestamp", type: "string" },
      { name: "nonce", type: "uint256" },
      { name: "action", type: "string" },
      { name: "message", type: "string" },
    ],
  },
  primaryType: "MysteryBoxAuth",
  signatureValidityPeriod: 5 * 60 * 1000,
};

const generateTimestamp = (): string => {
  return Date.now().toString();
};

/**
 * Magic 钱包签名实现
 */
export const signWithMagic = async (magicInstance: any, userAddress: string) => {
  try {
    if (!magicInstance) {
      throw new Error("Magic instance is not available");
    }

    const provider = new ethers.BrowserProvider(magicInstance.rpcProvider);
    const signer = await provider.getSigner();
    const signerAddress = await signer.getAddress();

    if (!/^0x[0-9a-fA-F]{40}$/.test(signerAddress)) {
      throw new Error("Invalid Ethereum address");
    }

    if (signerAddress.toLowerCase() !== userAddress.toLowerCase()) {
      throw new Error("Magic 钱包地址不匹配");
    }

    const timestamp = generateTimestamp();
    const { domain, types } = MYSTERY_BOX_SIGNATURE_CONFIG;

    const value = {
      address: signerAddress.toLowerCase(),
      timestamp: timestamp,
      nonce: 0,
      action: "openMysteryBox",
      message: "I authorize opening my mystery box",
    };

    const signature = await signer.signTypedData(domain, { MysteryBoxAuth: types.MysteryBoxAuth }, value);

    return {
      userAddress: signerAddress,
      signature,
      timestamp,
      nonce: 0,
    };
  } catch (error) {
    throw error;
  }
};

/**
 * 盲盒签名函数 - 仅支持Magic用户
 */
export const signMysteryBoxAuth = async (userAddress: string, magicInstance: any) => {
  if (!magicInstance) {
    throw new Error("Magic instance is required for mystery box signing");
  }
  return await signWithMagic(magicInstance, userAddress);
};

/**
 * 奖励签名函数 - 仅支持Magic用户
 */
export const signRewardAuth = async (userAddress: string, magicInstance: any) => {
  if (!magicInstance) {
    throw new Error("Magic instance is required for reward signing");
  }

  const timestamp = generateTimestamp();
  const { domain, types } = MYSTERY_BOX_SIGNATURE_CONFIG;

  const value = {
    address: userAddress,
    timestamp: timestamp,
    nonce: 0,
    action: "claimRewards",
    message: "I authorize claiming my rewards",
  };

  const provider = new ethers.BrowserProvider(magicInstance.rpcProvider);
  const signer = await provider.getSigner();
  const signerAddress = await signer.getAddress();

  if (signerAddress.toLowerCase() !== userAddress.toLowerCase()) {
    throw new Error("Magic 钱包地址不匹配");
  }

  const signature = await signer.signTypedData(domain, { MysteryBoxAuth: types.MysteryBoxAuth }, value);

  return {
    userAddress: signerAddress,
    signature,
    timestamp,
    nonce: 0,
  };
};

/**
 * 格式化错误消息
 */
export const formatSignatureError = (error: any): string => {
  if (error.code === 4001) {
    return "用户取消了签名请求";
  }
  if (error.code === -32602) {
    return "签名参数无效";
  }
  if (error.message?.includes("User rejected")) {
    return "用户拒绝了签名请求";
  }
  if (error.message?.includes("不匹配")) {
    return error.message;
  }
  if (error.message?.includes("签名已过期")) {
    return "签名已过期，请重新签名";
  }
  if (error.code === "INVALID_SIGNATURE") {
    return "签名验证失败，请检查钱包连接状态";
  }
  return error.message || "签名失败，请重试";
};

/**
 * 签名前预检查
 */
export const preCheckSignature = (userAddress: string): void => {
  if (!userAddress || !/^0x[0-9a-fA-F]{40}$/.test(userAddress)) {
    throw new Error("无效的用户地址");
  }
};
