/**
 * 社交媒体数据映射工具
 * 处理不同社交媒体平台的用户信息字段映射
 */

export interface SocialMediaData {
  social_media_id: string;
  social_media: string;
  email: string;
  social_media_user_name: string;
}

/**
 * 将不同社交媒体平台的用户信息映射为统一格式
 * @param userInfo OAuth 返回的用户信息
 * @param provider 社交媒体平台名称 (google, twitter, discord, etc.)
 * @returns 映射后的社交媒体数据
 */
export const mapSocialMediaData = (userInfo: any, provider: string): SocialMediaData => {
  const baseData = {
    social_media_id: userInfo.sub,
    social_media: provider,
  };

  switch (provider.toLowerCase()) {
    case "google":
      return {
        ...baseData,
        email: userInfo.email,
        social_media_user_name: userInfo.name,
      };

    case "twitter":
      return {
        ...baseData,
        email: userInfo.preferredUsername, // Twitter 特殊处理
        social_media_user_name: userInfo.name,
      };

    case "discord":
      return {
        ...baseData,
        email: userInfo.email,
        social_media_user_name: userInfo.preferredUsername, // Discord 特殊处理
      };

    default:
      // 默认映射（适用于其他平台）
      return {
        ...baseData,
        email: userInfo.email || "",
        social_media_user_name: userInfo.name || userInfo.preferredUsername || "",
      };
  }
};
