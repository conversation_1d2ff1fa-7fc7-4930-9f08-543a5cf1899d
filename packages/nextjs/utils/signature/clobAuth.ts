import { getCurrentTimestamp } from "@/utils";
import { ethers } from "ethers";
import createClobAuth from "~~/api/signature/creatClobAuth";

// MetaMask
export const handleCreateClobAuth = async (setEnableState: any) => {
  if (typeof window.ethereum !== "undefined") {
    try {
      setEnableState("loading");

      await window.ethereum.request({ method: "eth_requestAccounts" });
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const signerAddress = await signer.getAddress();

      if (!/^0x[0-9a-fA-F]{40}$/.test(signerAddress)) {
        throw new Error("Invalid Ethereum address");
      }

      const timestamp = getCurrentTimestamp().toString();

      const domain = {
        name: "ClobAuthDomain",
        version: "1",
        chainId: Number(process.env.NEXT_PUBLIC_CHAIN_ID),
        verifyingContract: "******************************************",
      };

      // 定义 EIP-712 类型
      const types = {
        EIP712Domain: [
          { name: "name", type: "string" },
          { name: "version", type: "string" },
          { name: "chainId", type: "uint256" },
          { name: "verifyingContract", type: "address" },
        ],
        ClobAuth: [
          { name: "address", type: "address" },
          { name: "timestamp", type: "string" },
          { name: "nonce", type: "uint256" },
          { name: "message", type: "string" },
        ],
      };
      const value = {
        address: signerAddress.toLowerCase(),
        timestamp: timestamp,
        nonce: 0,
        message: "This message attests that I control the given wallet",
      };

      // 调用 eth_signTypedData_v4
      const payload = {
        domain,
        types,
        primaryType: "ClobAuth",
        message: value,
      };

      const params = [signerAddress.toLowerCase(), JSON.stringify(payload)];

      try {
        const signature = await window.ethereum.request({
          method: "eth_signTypedData_v4",
          params: params,
        });

        // 调用后端 API 验证签名
        const response = await createClobAuth({
          address: signerAddress,
          signature: signature,
          timestamp: timestamp,
        });

        if (response.apiKey) {
          setEnableState("success");
        } else {
          console.error("signature verification failed");
          setEnableState("none");
        }
      } catch (error) {
        console.error("error:", error);
        setEnableState("none");
      }
    } catch (error) {
      // console.error("连接钱包或签名失败:", error);
      setEnableState("none");
    }
  } else {
    console.error("MetaMask is not installed");
    setEnableState("none");
  }
};

// Magic
export const handleCreateClobAuthWithMagic = async (magicInstance: any) => {
  if (!magicInstance) {
    console.error("Magic instance is required");
    return;
  }

  try {
    // 使用 Magic 连接
    const provider = new ethers.BrowserProvider(magicInstance.rpcProvider);
    const signer = await provider.getSigner();
    const userAddress = await signer.getAddress();

    // 验证 userAddress 是否为有效的以太坊地址
    if (!/^0x[0-9a-fA-F]{40}$/.test(userAddress)) {
      throw new Error("Invalid Ethereum address");
    }

    const timestamp = getCurrentTimestamp().toString();

    const domain = {
      name: "ClobAuthDomain",
      version: "1",
      chainId: Number(process.env.NEXT_PUBLIC_CHAIN_ID),
      verifyingContract: "******************************************",
    };

    const types = {
      EIP712Domain: [
        { name: "name", type: "string" },
        { name: "version", type: "string" },
        { name: "chainId", type: "uint256" },
        { name: "verifyingContract", type: "address" },
      ],
      ClobAuth: [
        { name: "address", type: "address" },
        { name: "timestamp", type: "string" },
        { name: "nonce", type: "uint256" },
        { name: "message", type: "string" },
      ],
    };

    const value = {
      address: userAddress.toLowerCase(),
      timestamp: timestamp,
      nonce: 0,
      message: "This message attests that I control the given wallet",
    };

    const signature = await signer.signTypedData(domain, { ClobAuth: types.ClobAuth }, value);

    // 调用后端 API 验证签名
    const response = await createClobAuth({
      address: userAddress,
      signature: signature,
      timestamp: timestamp,
    });

    if (response.apiKey) {
      console.log("Magic CLOB auth successful");
    } else {
      console.error("signature verification failed");
    }
  } catch (error) {
    console.error("Magic CLOB auth error:", error);
  }
};
