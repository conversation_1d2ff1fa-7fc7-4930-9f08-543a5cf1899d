// 简单的token过期检测函数
export const isTokenExpiredError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || "";
  const responseData = error?.response?.data || {};

  return (
    errorMessage.includes("token has invalid claims: token is expired") ||
    errorMessage.includes("token is expired") ||
    errorMessage.includes("fail to verify the token") ||
    responseData?.isTokenExpired === true
  );
};
