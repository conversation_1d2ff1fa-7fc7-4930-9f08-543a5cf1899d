// 定义接口，但不包含多语言文本
interface SportItem {
  id: string | number;
  nameKey: string; // 使用 i18n key 而不是多语言对象
  key: string;
  href: string;
  icon: string;
  isSpecial: boolean;
  tag_id?: number;
  tag_game_id?: number;
}

const MOCKLEFTNAVLIST: SportItem[] = [
  {
    id: 1,
    nameKey: "sports.basketball",
    key: "basketball",
    href: "/sports/basketball",
    icon: "/basketball_icon.png",
    isSpecial: false,
    tag_game_id: 59,
    tag_id: 62,
  },
  {
    id: 2,
    nameKey: "sports.soccer",
    key: "soccer",
    href: "/sports/soccer",
    icon: "/soccer_icon.png",
    isSpecial: false,
    // tag_game_id: 58,
    tag_id: 63,
  },
  // 可以添加更多体育类型
  // {
  //   id: 4,
  //   nameKey: "sports.nfl",
  //   key: "nfl",
  //   href: "/sports/nfl",
  //   icon: "/sport_nfl_logo.png",
  //   isSpecial: false,
  //   tag_game_id: 49,
  //   tag_id: 60,
  // },
];

// 添加 All Sports 到特殊项目中
const SPECIALMOCKLEFTNAVLIST: SportItem[] = [
  {
    id: "all",
    nameKey: "sports.all_sports",
    key: "",
    href: "/sports",
    icon: "/all_icon.png",
    isSpecial: true,
  },
  // 其他特殊项目可以在这里添加
];

const COMBINEDMOCKLEFTNAVLIST = [...SPECIALMOCKLEFTNAVLIST, ...MOCKLEFTNAVLIST];

export { MOCKLEFTNAVLIST, SPECIALMOCKLEFTNAVLIST, COMBINEDMOCKLEFTNAVLIST, type SportItem };
