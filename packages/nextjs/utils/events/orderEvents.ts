/**
 * 订单事件系统
 * 用于在订单成功后通知其他组件刷新数据
 */

type OrderEventType = "ORDER_SUCCESS" | "ORDER_FAILED";

interface OrderEventData {
  type: "buy" | "sell";
  tokenId: string;
  shares: number;
  price?: number;
  timestamp: number;
}

type OrderEventListener = (data: OrderEventData) => void;

class OrderEventManager {
  private listeners: Map<OrderEventType, Set<OrderEventListener>> = new Map();

  constructor() {
    this.listeners.set("ORDER_SUCCESS", new Set());
    this.listeners.set("ORDER_FAILED", new Set());
  }

  /**
   * 添加事件监听器
   */
  addEventListener(event: OrderEventType, listener: OrderEventListener) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.add(listener);
    }

    // 返回移除监听器的函数
    return () => {
      const eventListeners = this.listeners.get(event);
      if (eventListeners) {
        eventListeners.delete(listener);
      }
    };
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(event: OrderEventType, listener: OrderEventListener) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(listener);
    }
  }

  /**
   * 触发事件
   */
  emitEvent(event: OrderEventType, data: OrderEventData) {
    console.log(`📢 Order event emitted: ${event}`, data);

    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`❌ Error in order event listener:`, error);
        }
      });
    }
  }

  /**
   * 清除所有监听器
   */
  clearAllListeners() {
    this.listeners.forEach(eventListeners => {
      eventListeners.clear();
    });
  }
}

// 创建全局实例
export const orderEventManager = new OrderEventManager();

// 导出类型
export type { OrderEventType, OrderEventData, OrderEventListener };

// 便捷函数
export const emitOrderSuccess = (data: Omit<OrderEventData, "timestamp">) => {
  const eventData = {
    ...data,
    timestamp: Date.now(),
  };

  console.log(`🚀 Emitting ORDER_SUCCESS event:`, eventData);
  orderEventManager.emitEvent("ORDER_SUCCESS", eventData);
  console.log(`📡 ORDER_SUCCESS event emitted successfully`);
};

export const emitOrderFailed = (data: Omit<OrderEventData, "timestamp">) => {
  orderEventManager.emitEvent("ORDER_FAILED", {
    ...data,
    timestamp: Date.now(),
  });
};
