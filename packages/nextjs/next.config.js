/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  typescript: {
    ignoreBuildErrors: process.env.NEXT_PUBLIC_IGNORE_BUILD_ERROR === "true",
  },
  eslint: {
    ignoreDuringBuilds: process.env.NEXT_PUBLIC_IGNORE_BUILD_ERROR === "true",
  },
  webpack: config => {
    config.resolve.fallback = { fs: false, net: false, tls: false };
    config.externals.push("pino-pretty", "lokijs", "encoding");
    return config;
  },
  images: {
    remotePatterns: [
      { protocol: "https", hostname: "picsum.photos" }, // TODO:del - Temporary image source，
      { protocol: "https", hostname: "i.postimg.cc" }, // TODO:del - Temporary image source，
      { protocol: "https", hostname: "postimg.cc" }, // TODO:del - Temporary image source
      { protocol: "https", hostname: "gravatar.com" },
      { protocol: "https", hostname: "sgp1.vultrobjects.com" },
      { protocol: "https", hostname: "polymarket-upload.s3.us-east-2.amazonaws.com" },
      { protocol: "https", hostname: "polymarket.com" },
    ],
  },
};

module.exports = nextConfig;
