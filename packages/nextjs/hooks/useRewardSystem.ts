"use client";

import { useCallback, useEffect, useState } from "react";
import { type CheckAndCreateRewardResponse, checkAndCreateReward } from "@/api/mystery/reward";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useMagicStore } from "@/services/store/magicStore";
import { signRewardAuth } from "@/utils/signature/signMysteryBoxAuth";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

interface RewardSystemState {
  isChecking: boolean;
  lastRewardCheck: CheckAndCreateRewardResponse | null;
  error: string | null;
}

export const useRewardSystem = (onMysteryBoxRefresh?: () => Promise<void>) => {
  const { t } = useTranslation();
  const { address } = useUserAddress();
  const { magic: magicInstance } = useMagicStore();
  const [lastCheckedAddress, setLastCheckedAddress] = useState<string>("");
  const [inactiveAccounts, setInactiveAccounts] = useState<Set<string>>(new Set());
  const [state, setState] = useState<RewardSystemState>({
    isChecking: false,
    lastRewardCheck: null,
    error: null,
  });

  // 检查并创建奖励盲盒
  const checkAndCreateRewards = useCallback(async (): Promise<CheckAndCreateRewardResponse | null> => {
    if (!address) {
      return null;
    }

    setState(prev => ({ ...prev, isChecking: true, error: null }));

    try {
      // 生成签名（仅Magic用户）
      const signatureData = await signRewardAuth(address, magicInstance);

      const result = await checkAndCreateReward(signatureData);

      setState(prev => ({
        ...prev,
        lastRewardCheck: result,
        isChecking: false,
      }));

      // 显示奖励通知
      if (result.success && result.data.createdBoxes > 0) {
        toast.success(
          t("mysteryBox.rewards.congratulations", {
            count: result.data.createdBoxes,
          }),
          {
            duration: 5000,
            style: {
              background: "#10B981",
              color: "white",
              fontSize: "14px",
              padding: "16px",
            },
          },
        );
      } else if (result.success && result.data.createdBoxes === 0) {
        console.log("No new reward boxes created");
      }

      return result;
    } catch (error: any) {
      console.error("Error checking rewards:", error);
      setState(prev => ({
        ...prev,
        error: error.message || t("mysteryBox.rewards.checkFailed"),
        isChecking: false,
      }));
      return null;
    }
  }, [address, magicInstance, t]);

  // 用户激活后的奖励检查
  const onUserActivated = useCallback(async () => {
    console.log("🎁 用户激活，从未激活列表移除并触发奖励检查，用户地址:", address);

    if (!address) {
      console.log("🎁 没有用户地址，跳过奖励检查");
      return null;
    }

    // 从未激活账户列表中移除
    console.log("🔄 从未激活列表移除，允许重新检查");
    setInactiveAccounts(prev => {
      const newSet = new Set(prev);
      newSet.delete(address);
      return newSet;
    });
    setLastCheckedAddress(""); // 重置检查状态，允许重新检查

    // 防止并发调用
    if (state.isChecking) {
      console.log("🎁 正在检查中，跳过重复调用");
      return null;
    }

    console.log("🎁 调用 checkAndCreateRewards...", { address });
    const result = await checkAndCreateRewards();
    console.log("🎁 checkAndCreateRewards 结果:", result);

    if (result?.success) {
      console.log("🎁 奖励检查成功，创建的盒子数量:", result.data.createdBoxes);

      // 标记该地址已检查
      setLastCheckedAddress(address);

      // 如果有新的盲盒创建，刷新盲盒数据
      if (result.data.createdBoxes > 0 && onMysteryBoxRefresh) {
        console.log("🎁 检测到新盲盒，刷新盲盒数据");
        await onMysteryBoxRefresh();
      } else if (result.data.createdBoxes === 0) {
        console.log("🎁 没有新的盲盒创建");
      } else if (!onMysteryBoxRefresh) {
        console.log("🎁 没有盲盒刷新回调函数");
      }

      return result;
    } else {
      console.log("🎁 奖励检查失败或没有成功标志");
    }
    return null;
  }, [checkAndCreateRewards, onMysteryBoxRefresh, address, state.isChecking]);

  // 自动检查奖励（跳过未激活账户）
  const autoCheckRewards = useCallback(async () => {
    if (!address || state.isChecking || lastCheckedAddress === address) {
      console.log("🔄 跳过重复的奖励检查");
      return null;
    }

    // 如果账户已知未激活，直接跳过
    if (inactiveAccounts.has(address)) {
      console.log("⚠️ 账户未激活，跳过奖励检查");
      return null;
    }

    try {
      const result = await checkAndCreateRewards();

      if (result?.success) {
        // 成功时从未激活列表中移除（如果存在）
        setInactiveAccounts(prev => {
          const newSet = new Set(prev);
          newSet.delete(address);
          return newSet;
        });
        setLastCheckedAddress(address);

        // 重新查询盲盒列表
        if (onMysteryBoxRefresh) {
          console.log("🎁 奖励检查完成，刷新盲盒列表");
          await onMysteryBoxRefresh();
        }
      } else if (result && !result.success && result.message?.includes("Account Not Activated")) {
        // 账户未激活，标记为未激活状态，不再重试
        console.log("⚠️ 账户未激活，标记并跳过后续检查");
        setInactiveAccounts(prev => new Set(prev).add(address));
        setLastCheckedAddress(address); // 标记已检查，避免重复
      } else {
        // 其他错误，标记已检查但不做特殊处理
        console.log(`❌ 奖励检查失败: ${result?.message || "未知错误"}`);
        setLastCheckedAddress(address);
      }

      return result;
    } catch (error: any) {
      // 网络错误或其他异常
      console.error(`❌ 奖励检查异常: ${error.message}`);
      setLastCheckedAddress(address); // 标记已检查，避免重复
      return null;
    }
  }, [checkAndCreateRewards, onMysteryBoxRefresh, address, lastCheckedAddress, state.isChecking, inactiveAccounts]);

  // 手动检查奖励
  const handleManualRewardCheck = useCallback(async () => {
    const result = await checkAndCreateRewards();

    if (result?.success) {
      if (result.data.createdBoxes > 0) {
        toast.success(t("mysteryBox.rewards.newBoxes", { count: result.data.createdBoxes }));

        // 如果有新的盲盒创建，刷新盲盒数据
        if (onMysteryBoxRefresh) {
          console.log("🎁 手动检查获得新盲盒，刷新盲盒数据");
          await onMysteryBoxRefresh();
        }
      } else {
        toast(t("mysteryBox.rewards.noNewBoxes"), {
          icon: "ℹ️",
          style: {
            background: "#F3F4F6",
            color: "#374151",
          },
        });
      }
    }

    return result;
  }, [checkAndCreateRewards, onMysteryBoxRefresh, t]);

  // 重置账户状态（用户激活账户后调用）
  const resetAccountStatus = useCallback(
    (userAddress?: string) => {
      const targetAddress = userAddress || address;
      if (targetAddress) {
        console.log(`🔄 重置地址 ${targetAddress} 的账户状态`);
        setInactiveAccounts(prev => {
          const newSet = new Set(prev);
          newSet.delete(targetAddress);
          return newSet;
        });
        setLastCheckedAddress(""); // 重置检查状态，允许重新检查
      }
    },
    [address],
  );

  // 地址切换时重置状态
  useEffect(() => {
    if (address && address !== lastCheckedAddress) {
      console.log("🔄 检测到地址切换，重置奖励检查状态", {
        oldAddress: lastCheckedAddress,
        newAddress: address,
      });
      setLastCheckedAddress(""); // 重置检查状态，允许新地址进行检查
      setState(prev => ({
        ...prev,
        lastRewardCheck: null,
        stats: null,
        error: null,
      }));
    }
  }, [address, lastCheckedAddress]);

  // 自动检查奖励（Magic用户登录后）
  useEffect(() => {
    if (address) {
      autoCheckRewards();
    }
  }, [address]); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    // 状态
    ...state,

    // 方法
    checkAndCreateRewards,
    autoCheckRewards,
    onUserActivated,
    resetAccountStatus,
    handleManualRewardCheck,

    // 便捷访问
    hasNewRewards: !!state.lastRewardCheck?.data && state.lastRewardCheck.data.createdBoxes > 0,
    totalUnOpenedBoxes: state.lastRewardCheck?.data?.totalUnOpenedBoxes || 0,
  };
};
