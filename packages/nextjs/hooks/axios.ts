import nextConfig from "@/next.config";
import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from "axios";

// 重试配置
interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: AxiosError) => boolean;
}

// 默认重试配置
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  retries: 3,
  retryDelay: 1000,
  retryCondition: (error: AxiosError) => {
    // 重试条件：网络错误、超时、5xx服务器错误、认证相关错误
    const isNetworkError = !error.response;
    const isTimeout = error.code === "ECONNABORTED" || error.message.includes("timeout");
    const isServerError = error.response?.status ? error.response.status >= 500 : false;
    const isSignerError = error.message.includes("Signer is undefined");

    return isNetworkError || isTimeout || isServerError || isSignerError;
  },
};

// 重试函数
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const retryRequest = async (
  requestFn: () => Promise<AxiosResponse>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
): Promise<AxiosResponse> => {
  let lastError: AxiosError | undefined;

  for (let attempt = 0; attempt <= config.retries; attempt++) {
    try {
      const response = await requestFn();
      if (attempt > 0) {
        console.log(`✅ Request succeeded on attempt ${attempt + 1}`);
      }
      return response;
    } catch (error) {
      lastError = error as AxiosError;

      // 如果是最后一次尝试，或者不满足重试条件，直接抛出错误
      if (attempt === config.retries || !config.retryCondition?.(lastError)) {
        throw lastError;
      }

      const delay = config.retryDelay * Math.pow(2, attempt); // 指数退避
      console.log(`⚠️ Request failed (attempt ${attempt + 1}/${config.retries + 1}), retrying in ${delay}ms...`, {
        error: lastError.message,
        code: lastError.code,
        status: lastError.response?.status,
      });

      await sleep(delay);
    }
  }

  throw lastError || new Error("Unknown error occurred during retry");
};

// 创建 Axios 实例
const request = axios.create({
  baseURL: process.env.NEXT_PUBLIC_HASURA_ENDPOINT,
  timeout: 30 * 1000, // 增加到30秒
  responseType: "json",
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const whiteList = nextConfig.whitelist;
    const url = config.url || "";
    const token = localStorage.getItem("token");
    if (token && !whiteList.includes(url)) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      } as InternalAxiosRequestConfig["headers"];
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  },
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const status = response.data?.code || 200;
    const message = response.data?.msg || "success";

    // 修复逻辑错误：401应该在status !== 200之前检查
    if (status === 401) {
      console.error("🔐 Token expired, redirecting to login...");
      localStorage.removeItem("token");
      window.location.href = "/login";
      return Promise.reject(new Error("Token expired"));
    }

    if (status !== 200) {
      console.error(`❌ API Error - Code ${status}: ${message}`, {
        url: response.config?.url,
        method: response.config?.method,
        data: response.data,
      });
      return Promise.reject(new Error(`API Error: ${message}`));
    }

    return response;
  },
  (error: AxiosError) => {
    // 详细的错误日志
    const errorInfo = {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      url: error.config?.url,
      method: error.config?.method,
    };

    console.error("🌐 Network/Request Error:", errorInfo);

    // 为常见错误提供更友好的错误信息
    if (error.code === "ECONNABORTED") {
      return Promise.reject(new Error("Request timeout - please check your network connection"));
    }

    if (!error.response) {
      return Promise.reject(new Error("Network error - please check your internet connection"));
    }

    if (error.response.status >= 500) {
      return Promise.reject(new Error("Server error - please try again later"));
    }

    return Promise.reject(error);
  },
);

// 带重试功能的请求函数
export const requestWithRetry = async <T = any>(
  config: Parameters<typeof request>[0],
  retryConfig?: Partial<RetryConfig>,
): Promise<AxiosResponse<T>> => {
  const finalRetryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };

  return retryRequest(() => request(config), finalRetryConfig);
};

// 便捷方法
export const getWithRetry = <T = any>(
  url: string,
  config?: Parameters<typeof request.get>[1],
  retryConfig?: Partial<RetryConfig>,
): Promise<AxiosResponse<T>> => {
  const finalRetryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };

  return retryRequest(() => request.get<T>(url, config), finalRetryConfig);
};

export const postWithRetry = <T = any>(
  url: string,
  data?: any,
  config?: Parameters<typeof request.post>[2],
  retryConfig?: Partial<RetryConfig>,
): Promise<AxiosResponse<T>> => {
  const finalRetryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };

  return retryRequest(() => request.post<T>(url, data, config), finalRetryConfig);
};

// 导出原始实例（向后兼容）
export default request;

// 导出重试配置类型
export type { RetryConfig };
