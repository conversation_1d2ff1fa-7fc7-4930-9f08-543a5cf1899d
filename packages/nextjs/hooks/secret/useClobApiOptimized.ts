import { useCallback, useEffect, useState } from "react";
import { getItem, setItem } from "@/utils";

interface ClobApiData {
  [address: string]: {
    key: string;
    secret: string;
    passphrase: string;
    baseAddress: string;
  };
}

type ClobApiListener = (apis: ClobApiData | null) => void;

class ClobApiManager {
  private static instance: ClobApiManager;
  private currentApis: ClobApiData | null = null;
  private listeners = new Set<ClobApiListener>();
  private pollingInterval: NodeJS.Timeout | null = null;
  private lastUpdateTime = 0;
  private readonly STORAGE_KEY = "poly_clob_api_key_map";
  private readonly POLLING_INTERVAL = 5000;
  private readonly UPDATE_DEBOUNCE = 100; // 防抖时间

  static getInstance(): ClobApiManager {
    if (!ClobApiManager.instance) {
      ClobApiManager.instance = new ClobApiManager();
    }
    return ClobApiManager.instance;
  }

  private constructor() {
    this.loadFromStorage();
  }

  private loadFromStorage(): void {
    try {
      const stored = getItem(this.STORAGE_KEY);
      this.currentApis = stored && typeof stored === "object" ? stored : null;
    } catch (error) {
      console.error("Failed to load clob APIs from storage:", error);
      this.currentApis = null;
    }
  }

  private saveToStorage(apis: ClobApiData | null): void {
    try {
      if (apis) {
        setItem(this.STORAGE_KEY, apis);
      }
    } catch (error) {
      console.error("Failed to save clob APIs to storage:", error);
    }
  }

  private notifyListeners(): void {
    // 防抖处理，避免频繁更新
    const now = Date.now();
    if (now - this.lastUpdateTime < this.UPDATE_DEBOUNCE) {
      return;
    }
    this.lastUpdateTime = now;

    this.listeners.forEach(listener => {
      try {
        listener(this.currentApis);
      } catch (error) {
        console.error("Error in clob API listener:", error);
      }
    });
  }

  private startPolling(): void {
    if (this.pollingInterval || this.listeners.size === 0) return;

    this.pollingInterval = setInterval(() => {
      try {
        const newApis = getItem(this.STORAGE_KEY);

        // 使用更高效的比较方法
        if (!this.isEqual(newApis, this.currentApis)) {
          this.currentApis = newApis && typeof newApis === "object" ? newApis : null;
          this.notifyListeners();
        }
      } catch (error) {
        console.error("Error during polling:", error);
      }
    }, this.POLLING_INTERVAL);
  }

  private stopPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
  }

  private isEqual(a: any, b: any): boolean {
    if (a === b) return true;
    if (!a || !b) return false;

    try {
      return JSON.stringify(a) === JSON.stringify(b);
    } catch {
      return false;
    }
  }

  subscribe(listener: ClobApiListener): () => void {
    this.listeners.add(listener);
    this.startPolling();

    // 立即通知当前状态
    try {
      listener(this.currentApis);
    } catch (error) {
      console.error("Error in initial clob API listener call:", error);
    }

    // 返回取消订阅函数
    return () => {
      this.listeners.delete(listener);
      if (this.listeners.size === 0) {
        this.stopPolling();
      }
    };
  }

  updateApis(newApis: ClobApiData | null): void {
    this.currentApis = newApis;
    this.saveToStorage(newApis);
    this.notifyListeners();
  }

  getCurrentApis(): ClobApiData | null {
    return this.currentApis;
  }
}

// 获取全局管理器实例
const clobApiManager = ClobApiManager.getInstance();

export const useClobApiOptimized = () => {
  const [clobApis, setClobApis] = useState<ClobApiData | null>(() => {
    return clobApiManager.getCurrentApis();
  });

  useEffect(() => {
    // 订阅状态变化
    const unsubscribe = clobApiManager.subscribe(setClobApis);

    // 返回清理函数
    return unsubscribe;
  }, []);

  const updateClobApis = useCallback((newApis: ClobApiData | null) => {
    clobApiManager.updateApis(newApis);
  }, []);

  return {
    clobApis,
    updateClobApis,
    isLoading: clobApis === null,
    hasApiForAddress: useCallback(
      (address: string) => {
        return !!(clobApis && clobApis[address]);
      },
      [clobApis],
    ),
  };
};

// 向后兼容的导出
export default useClobApiOptimized;

// 导出类型供其他地方使用
export type { ClobApiData, ClobApiListener };

// 导出管理器实例供高级用法
export { clobApiManager };
