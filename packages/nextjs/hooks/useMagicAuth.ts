import { useCallback, useEffect } from "react";
import { getWebUsersByProxyWallet } from "@/api/user";
import { checkIfContract } from "@/contracts/checkIfContract";
import computeProxyAddress from "@/contracts/computeProxyAddress";
import {
  approveSignatureWithMagic,
  createGetTransactionHash,
  getNonce,
  hasApproved,
} from "@/contracts/encodeMultiSendTransaction";
import { useMagicStore } from "@/services/store/magicStore";
import { generateRandomNonce, getItem } from "@/utils";
import { mapSocialMediaData } from "@/utils/signature";
import { handleCreateClobAuthWithMagic } from "@/utils/signature/clobAuth";
import { handleCreateProxyWalletWithMagic } from "@/utils/signature/createProxy";
import { Web3Provider } from "@ethersproject/providers";
import { getSession, signIn } from "next-auth/react";
import { useTranslation } from "react-i18next";
import { SiweMessage } from "siwe";

interface UseMagicAuthProps {
  setStatus: (status: string) => void;
  setError: (error: string) => void;
  setCurrentStep: (step: string) => void;
  router: any;
}

export const useMagicAuth = ({ setStatus, setCurrentStep, router }: UseMagicAuthProps) => {
  const { t } = useTranslation();
  const { magic, login: loginMagic, initializeMagic, isInitialized } = useMagicStore();

  useEffect(() => {
    if (!isInitialized && !magic) {
      initializeMagic();
    }
  }, [isInitialized, magic, initializeMagic]);

  // 处理 OAuth 回调
  const handleOAuthCallback = useCallback(
    async (magic: any) => {
      setCurrentStep(t("login.steps.oauth_callback"));
      try {
        const result = await magic.oauth.getRedirectResult();
        console.log("Magic OAuth result:", result);

        // 清理 URL 参数
        if (typeof window !== "undefined") {
          const url = new URL(window.location.href);
          url.searchParams.delete("magic_credential");
          url.searchParams.delete("state");
          url.searchParams.delete("provider");
          window.history.replaceState({}, "", url.toString());
        }
        return result;
      } catch (oauthError) {
        // 处理 OAuth 错误的逻辑
        if (
          typeof oauthError === "object" &&
          oauthError !== null &&
          "message" in oauthError &&
          typeof (oauthError as any).message === "string" &&
          (oauthError as any).message.includes("parsing oauth query params")
        ) {
          const isLoggedIn = await magic.user.isLoggedIn();
          if (isLoggedIn) {
            const metadata = await magic.user.getInfo();
            return {
              oauth: { userInfo: metadata, provider: "google" },
              magic: { userMetadata: metadata },
            };
          }
        }
        throw oauthError;
      }
    },
    [setCurrentStep, t],
  );

  // 用户认证流程
  const authenticateUser = useCallback(
    async (magic: any, oauthResult?: any) => {
      setCurrentStep(t("login.steps.get_user_info"));
      const metadata = await magic.user.getInfo();
      const didToken = await magic.user.getIdToken();

      if (!metadata.issuer || !metadata.publicAddress || !didToken) {
        throw new Error(t("login.errors.get_user_info"));
      }

      // 更新全局 Magic 状态
      loginMagic({
        publicAddress: metadata.publicAddress,
        email: metadata.email || undefined,
        issuer: metadata.issuer || undefined,
      });

      setCurrentStep(t("login.steps.generate_signature"));
      const nonce = generateRandomNonce(16);

      // 构建标准的 SIWE 消息
      const siweMessageData: any = {
        address: metadata.publicAddress,
        chainId: Number(process.env.NEXT_PUBLIC_CHAIN_ID),
        nonce: nonce,
        domain: process.env.NEXT_PUBLIC_LOGIN_DOMAIN,
        expirationTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        uri: process.env.NEXT_PUBLIC_LOGIN_URL,
        statement: "Welcome to Prediction One! Sign to connect.",
        version: "1",
      };

      const siweMessage = new SiweMessage(siweMessageData);
      const message = siweMessage.prepareMessage();

      setCurrentStep(t("login.steps.verify_signature"));
      const provider = new Web3Provider(magic.rpcProvider as any);
      const signer = provider.getSigner();
      const signature = await signer.signMessage(message);

      let socialMediaDataString = "";
      if (oauthResult?.oauth?.userInfo) {
        const { userInfo, provider } = oauthResult.oauth;
        const socialMediaData = mapSocialMediaData(userInfo, provider);
        socialMediaDataString = JSON.stringify(socialMediaData);
        console.log(`准备传递 ${provider} 社交媒体数据:`, socialMediaData);
      }

      setCurrentStep(t("login.steps.login_verification"));
      const signInResult = await signIn("credentials", {
        message,
        signature,
        didToken,
        socialMediaData: socialMediaDataString,
        redirect: false,
      });

      if (!signInResult?.ok) {
        throw new Error(t("login.errors.login_verification"));
      }

      return {
        sessionData: {
          cookie: didToken,
          user: {
            address: metadata.publicAddress,
            email: metadata.email,
          },
        },
        metadata,
        didToken,
      };
    },
    [setCurrentStep, loginMagic, t],
  );

  // 处理代理钱包
  const handleProxyWallet = useCallback(
    async (sessionData: any, metadata: any, magic: any, oauthResult?: any) => {
      const address = metadata.publicAddress.toLowerCase();
      const proxyWallet = await computeProxyAddress(metadata.publicAddress);

      setCurrentStep(t("login.steps.check_proxy_wallet"));
      let isContract = await checkIfContract(proxyWallet);
      const webUsers = await getWebUsersByProxyWallet(proxyWallet);

      const isWalletActive = isContract && webUsers?.data?.web_users?.length > 0;

      if (!isWalletActive) {
        await createProxyWallet(sessionData, proxyWallet, magic, oauthResult);
        isContract = await checkIfContract(proxyWallet);
      }

      return { address, proxyWallet, isContract };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [setCurrentStep, t],
  );

  // 创建代理钱包
  const createProxyWallet = useCallback(
    async (sessionData: any, proxyWallet: string, magic: any, oauthResult?: any) => {
      setCurrentStep(t("login.steps.create_proxy_wallet"));

      let isCreatedProxy = false;
      const setIsCreatedProxy = (value: boolean) => {
        isCreatedProxy = value;
      };
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      const setIsActiveWallet = () => {};
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      const setShowTooltip = () => {};

      const setIsBtnLoading = (loading: boolean) => {
        if (loading) setCurrentStep(t("login.steps.processing"));
      };

      await handleCreateProxyWalletWithMagic(
        sessionData,
        proxyWallet,
        setIsCreatedProxy,
        setIsBtnLoading,
        setIsActiveWallet,
        setShowTooltip,
        magic,
        oauthResult,
      );

      if (!isCreatedProxy) {
        throw new Error(t("login.errors.create_proxy"));
      }

      await new Promise(resolve => setTimeout(resolve, 3000));
    },
    [setCurrentStep, t],
  );

  // 处理代币授权
  const handleTokenApproval = useCallback(
    async (address: string, proxyWallet: string, currentSession: any, magic: any) => {
      setCurrentStep(t("login.steps.check_token_approval"));
      const isApproved = await hasApproved(proxyWallet);

      if (isApproved === "none" || !isApproved) {
        setCurrentStep(t("login.steps.approve_token"));
        const nonce = await getNonce(proxyWallet);
        const transactionHash = await createGetTransactionHash(proxyWallet, nonce);

        await approveSignatureWithMagic(address, proxyWallet, transactionHash, nonce, currentSession?.cookie, magic);
      }
    },
    [setCurrentStep, t],
  );

  // 启用交易功能
  const enableTrading = useCallback(
    async (metadata: any, magic: any) => {
      setCurrentStep(t("login.steps.enable_trading"));
      try {
        const clobApis = getItem("poly_clob_api_key_map") || {};
        const hasCurrentApi = clobApis && clobApis.hasOwnProperty(metadata.publicAddress);
        if (!hasCurrentApi) {
          await handleCreateClobAuthWithMagic(magic);
        }
      } catch (tradingError) {
        console.error(t("login.errors.trading_failed"), tradingError);
      }
    },
    [setCurrentStep, t],
  );

  // 主要处理函数
  const processMagicAuth = useCallback(async () => {
    setStatus("loading");

    try {
      // 确保 Magic 已初始化
      let magicInstance = magic;
      let waitCount = 0;

      if (!magicInstance) {
        setCurrentStep(t("login.steps.init_magic"));
      }

      while (!magicInstance && waitCount < 50) {
        // 最多等待 5 秒
        await new Promise(resolve => setTimeout(resolve, 100));
        magicInstance = useMagicStore.getState().magic;
        waitCount++;
      }

      if (!magicInstance) {
        throw new Error(t("login.errors.magic_timeout"));
      }

      // 2. 处理 OAuth 回调
      const oauthResult = await handleOAuthCallback(magicInstance);

      // 3. 用户认证（传递 OAuth 结果）
      const { metadata } = await authenticateUser(magicInstance, oauthResult);

      let currentSession = null;
      let retryCount = 0;
      const maxRetries = 10;

      while (!currentSession?.cookie && retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        currentSession = await getSession();
        retryCount++;
      }

      // 4. 创建或获取代理钱包（传递 OAuth 结果）
      const { address, proxyWallet, isContract } = await handleProxyWallet(
        currentSession,
        metadata,
        magicInstance,
        oauthResult,
      );

      // 5. 处理代币授权（仅当钱包存在时）
      if (isContract) {
        await handleTokenApproval(address, proxyWallet, currentSession, magicInstance);
        await enableTrading(metadata, magicInstance);
      }

      // 6. 完成设置
      setCurrentStep(t("login.steps.complete_setup"));
      setStatus("success");

      // 7. 重定向
      setTimeout(() => {
        if (window.opener) {
          window.opener.postMessage({ type: "MAGIC_LOGIN_SUCCESS" }, window.location.origin);
          window.close();
        } else {
          router.replace("/");
        }
      }, 1500);
    } catch (error) {
      throw error;
    }
  }, [
    setStatus,
    magic,
    t,
    handleOAuthCallback,
    authenticateUser,
    handleProxyWallet,
    handleTokenApproval,
    enableTrading,
    setCurrentStep,
    router,
  ]);

  return { processMagicAuth };
};
