// hooks/orders/useOrderState.ts
import { useCallback, useEffect, useRef, useState } from "react";
import { getOpenOrderData, getPositionsListDataInPofolio } from "@/api/order";
import { WalletType } from "@/services/store/store";

interface OrderState {
  // 数据状态
  positionsData: any[];
  openOrdersData: any[];
  currentSellValue: Array<{ side: string; value: number }>;

  // 加载状态
  isInitializing: boolean;
  isRefreshing: boolean;
  lastUpdateTime: number;

  // 错误状态
  error: string | null;
}

interface UseOrderStateProps {
  clobApis: any;
  walletType: WalletType;
  address: string;
  availableBalance: number;
  tokenIds?: string[];
}

export const useOrderState = ({
  clobApis,
  walletType,
  address,
  availableBalance,
  tokenIds = [],
}: UseOrderStateProps) => {
  // 状态管理
  const [state, setState] = useState<OrderState>({
    positionsData: [],
    openOrdersData: [],
    currentSellValue: [],
    isInitializing: true,
    isRefreshing: false,
    lastUpdateTime: 0,
    error: null,
  });

  // 防止重复请求
  const requestInProgress = useRef(false);
  const initializationAttempted = useRef(false);

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<OrderState>) => {
    setState(prev => ({
      ...prev,
      ...updates,
      lastUpdateTime: Date.now(),
    }));
  }, []);

  // 获取持仓数据
  const fetchPositions = useCallback(async (): Promise<any[]> => {
    if (!clobApis || !walletType) {
      throw new Error("Missing clobApis or walletType");
    }

    const response = await getPositionsListDataInPofolio(clobApis, walletType, {
      offset: 0,
      limit: 99999,
    });

    return response?.data?.data || [];
  }, [clobApis, walletType]);

  // 获取订单数据
  const fetchOrders = useCallback(async (): Promise<any[]> => {
    if (!clobApis || !walletType) {
      throw new Error("Missing clobApis or walletType");
    }

    const response = await getOpenOrderData(clobApis, walletType, {
      offset: 0,
      limit: 99999,
    });

    return response?.data?.data || [];
  }, [clobApis, walletType]);

  // 计算当前卖出值
  const calculateSellValue = useCallback((positions: any[], tokens: string[]) => {
    if (!tokens || tokens.length === 0) return [];

    // tokens[0] = Yes token, tokens[1] = No token
    const yesTokenId = tokens[0];
    const noTokenId = tokens[1];

    const yesPosition = positions.find((position: any) => {
      const assetValue = position.asset?.String || position.asset;
      return assetValue === yesTokenId;
    });

    const noPosition = positions.find((position: any) => {
      const assetValue = position.asset?.String || position.asset;
      return assetValue === noTokenId;
    });

    const result = [
      {
        side: "yes",
        value: yesPosition ? yesPosition.size : 0,
      },
      {
        side: "no",
        value: noPosition ? noPosition.size : 0,
      },
    ];

    return result;
  }, []);

  // 刷新所有数据
  const refreshData = useCallback(
    async (force = false): Promise<void> => {
      // 防止重复请求
      if (requestInProgress.current && !force) {
        console.log("🔄 Request already in progress, skipping...");
        return;
      }

      if (!clobApis || !walletType || !address) {
        updateState({
          error: "Missing authentication data",
          isInitializing: false,
          isRefreshing: false,
        });
        return;
      }

      requestInProgress.current = true;
      updateState({
        isRefreshing: true,
        error: null,
      });

      try {
        // 并行获取数据
        const [positions, orders] = await Promise.allSettled([fetchPositions(), fetchOrders()]);

        const positionsData = positions.status === "fulfilled" ? positions.value : [];
        const openOrdersData = orders.status === "fulfilled" ? orders.value : [];

        // 计算卖出值
        const currentSellValue = calculateSellValue(positionsData, tokenIds);

        // 更新状态
        updateState({
          positionsData,
          openOrdersData,
          currentSellValue,
          isInitializing: false,
          isRefreshing: false,
          error: null,
        });
      } catch (error) {
        updateState({
          error: error instanceof Error ? error.message : "Unknown error",
          isInitializing: false,
          isRefreshing: false,
        });
      } finally {
        requestInProgress.current = false;
      }
    },
    [clobApis, walletType, address, fetchPositions, fetchOrders, calculateSellValue, tokenIds, updateState],
  );

  // 计算冻结份额
  const calculateFrozenShares = useCallback(
    (tokenId: string): number => {
      const tokenSellOrders = state.openOrdersData.filter((order: any) => {
        const isMatchingAsset = order.asset_id === tokenId || order.asset === tokenId;
        // 支持多种可能的卖单标识
        const isSellOrder =
          order.side === "SELL" ||
          order.side === "sell" ||
          order.side === "Sell" ||
          order.side?.toUpperCase() === "SELL";
        const isActive =
          order.status === "Open" ||
          order.status === "ACTIVE" ||
          order.status === "open" ||
          order.status === "active" ||
          !order.status;

        return isMatchingAsset && isSellOrder && isActive;
      });

      const frozenAmount = tokenSellOrders.reduce((total, order) => {
        const size = order.size_current || order.original_size || order.size || "0";
        const sizeNum = parseInt(size, 10);
        return total + sizeNum;
      }, 0);

      const result = Math.floor(frozenAmount / 10 ** 6);

      return result;
    },
    [state.openOrdersData],
  );

  // 计算最大份额
  const calculateMaxShares = useCallback(
    (selectedButtonType: string, currentSelectedItem: string, orderTradeTokenId: string, price: number): number => {
      if (currentSelectedItem === "Buy") {
        if (price <= 0) return 0;
        const feeRate = 0.02;
        const effectiveBalance = availableBalance / (1 + feeRate);
        const result = Math.floor((effectiveBalance * 100) / price);
        return result;
      } else if (currentSelectedItem === "Sell") {
        const selectedValue =
          state.currentSellValue.find((item: { side: string }) => item.side === selectedButtonType)?.value || 0;

        const totalShares = selectedValue / 10 ** 6;
        const frozenShares = calculateFrozenShares(orderTradeTokenId);
        const availableShares = Math.max(0, totalShares - frozenShares);

        const result = Math.round(availableShares * 100) / 100;

        return result;
      }
      return 0;
    },
    [availableBalance, state.currentSellValue, calculateFrozenShares],
  );

  // 初始化数据
  useEffect(() => {
    if (!initializationAttempted.current && clobApis && walletType && address) {
      initializationAttempted.current = true;
      refreshData(true);
    }
  }, [clobApis, walletType, address, refreshData]);

  // 当 tokenIds 变化时重新计算卖出值
  useEffect(() => {
    if (tokenIds.length > 0 && state.positionsData.length > 0) {
      const newSellValue = calculateSellValue(state.positionsData, tokenIds);
      updateState({ currentSellValue: newSellValue });
    }
  }, [tokenIds, state.positionsData, calculateSellValue, updateState]);

  // 重置初始化状态当关键依赖变化时
  useEffect(() => {
    initializationAttempted.current = false;
    requestInProgress.current = false;
  }, [clobApis, walletType, address]);

  // 订单状态监控和自动刷新 - 使用 ref 避免依赖问题
  const refreshDataRef = useRef(refreshData);
  refreshDataRef.current = refreshData;

  useEffect(() => {
    if (!clobApis || !walletType || !address) {
      return;
    }

    // 检查是否有活跃订单的函数
    const hasActiveOrders = () => {
      return state.openOrdersData && state.openOrdersData.length > 0;
    };

    let intervalId: NodeJS.Timeout | null = null;

    const startMonitoring = () => {
      if (intervalId) {
        clearInterval(intervalId);
      }

      if (hasActiveOrders()) {
        intervalId = setInterval(async () => {
          try {
            await refreshDataRef.current(true);
          } catch (error) {
            console.error("❌ Auto-refresh failed:", error);
          }
        }, 30000); // 30秒间隔，减少频率
      }
    };

    // 立即检查并启动监控
    startMonitoring();

    // 清理函数
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clobApis, walletType, address, state.openOrdersData.length]);

  // 页面可见性检测 - 当用户回到页面时自动刷新
  useEffect(() => {
    if (!clobApis || !walletType || !address) {
      return;
    }

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refreshData(true);
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [clobApis, walletType, address, refreshData]);

  // 订单成功后的专门刷新方法
  const refreshAfterOrder = useCallback(async (): Promise<void> => {
    try {
      // 等待服务器数据同步
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 第二次刷新 - 确保获取同步后的数据
      await refreshData(true);

      // 启动短期密集监控（用于卖单后的实时更新）
      startIntensiveMonitoring();
    } catch (error) {
      console.error("❌ Error during post-order refresh:", error);
      // 即使出错也要尝试启动监控
      startIntensiveMonitoring();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshData]);

  // 智能订单执行监控 - 检测订单状态变化
  // const startOrderExecutionMonitoring = useCallback(() => {
  //   console.log("🚀 Starting order execution monitoring...");

  //   let count = 0;
  //   const maxCount = 6; // 最多监控60秒
  //   let lastOrderCount = state.openOrdersData.length;

  //   const monitoringInterval = setInterval(async () => {
  //     count++;
  //     try {
  //       // 获取最新数据
  //       await refreshData(true);

  //       // 检查订单状态变化
  //       const currentOrderCount = state.openOrdersData.length;

  //       // 如果订单数量减少，说明有订单被执行了
  //       if (currentOrderCount < lastOrderCount) {
  //         console.log(`🎉 Order executed! Order count: ${lastOrderCount} → ${currentOrderCount}`);

  //         // 订单执行后，需要刷新余额和frozen值
  //         console.log("🔄 Refreshing balance and frozen value after order execution...");

  //         // 这里可以触发余额刷新
  //         // 注意：这里应该调用父组件的余额刷新方法

  //         console.log("✅ Order execution detected, stopping monitoring");
  //         clearInterval(monitoringInterval);
  //         return;
  //       }

  //       lastOrderCount = currentOrderCount;

  //       if (count >= maxCount) {
  //         console.log("⏰ Order execution monitoring timeout, stopping");
  //         clearInterval(monitoringInterval);
  //       }
  //     } catch (error) {
  //       console.error("❌ Order execution monitoring failed:", error);
  //     }
  //   }, 10000); // 每10秒检查一次

  //   // 60秒后自动清理
  //   setTimeout(() => {
  //     clearInterval(monitoringInterval);
  //     console.log("⏰ Order execution monitoring timeout, stopped");
  //   }, 65000);
  // }, [refreshData, state.openOrdersData.length]);

  // 短期密集监控 - 用于订单提交后的数据刷新
  const startIntensiveMonitoring = useCallback(() => {
    console.log("🚀 Starting intensive monitoring for order submission...");

    let count = 0;
    const maxCount = 2; // 20秒 / 10秒 = 2次

    const intensiveInterval = setInterval(async () => {
      count++;
      console.log(`🔄 Intensive refresh ${count}/${maxCount}...`);

      try {
        await refreshData(true);
      } catch (error) {
        console.error("❌ Intensive refresh failed:", error);
      }

      if (count >= maxCount) {
        console.log("✅ Intensive monitoring completed");
        clearInterval(intensiveInterval);
      }
    }, 10000); // 每10秒刷新一次，持续20秒

    // 20秒后自动清理
    setTimeout(() => {
      clearInterval(intensiveInterval);
      console.log("⏰ Intensive monitoring timeout, stopped");
    }, 25000);
  }, [refreshData]);

  return {
    // 状态
    ...state,

    // 方法
    refreshData,
    refreshAfterOrder,
    calculateMaxShares,
    calculateFrozenShares,
    // startOrderExecutionMonitoring,

    // 辅助状态
    isReady: !state.isInitializing && !state.error,
    hasData: state.positionsData.length > 0 || state.openOrdersData.length > 0,
  };
};

export default useOrderState;
