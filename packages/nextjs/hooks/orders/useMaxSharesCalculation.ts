import { useCallback, useEffect, useRef, useState } from "react";
import { getOpenOrderData } from "@/api/order";
import { getPositionsListDataInPofolio } from "@/api/order";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useGlobalState } from "~~/services/store/store";

interface UseMaxSharesCalculationOptions {
  clobApis: string;
  availableBalance: number;
  onPositionsUpdate?: (positions: any[]) => void;
}

interface UseMaxSharesCalculationReturn {
  calculateMaxShares: (
    selectedButtonType: string,
    currentSelectedItem: string,
    orderTradeTokenId: string,
    price: number,
    orderType?: "Market" | "Limit",
  ) => number;
  openOrdersData: any[];
  positionsData: any[];
  currentSellValue: Array<{ side: string; value: number }>;
  isLoading: boolean;
  refreshOpenOrders: () => Promise<void>;
  refreshPositions: () => Promise<void>;
  refreshAll: () => Promise<void>;
  updateCurrentSellValue: (tokenIds: string[]) => void;
}

export function useMaxSharesCalculation({
  clobApis,
  availableBalance,
  onPositionsUpdate,
}: UseMaxSharesCalculationOptions): UseMaxSharesCalculationReturn {
  const { walletType } = useGlobalState();
  const { address } = useUserAddress();
  const [openOrdersData, setOpenOrdersData] = useState<any[]>([]);
  const [positionsData, setPositionsData] = useState<any[]>([]);
  const [currentSellValue, setCurrentSellValue] = useState<Array<{ side: string; value: number }>>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 为两个请求分别设置独立的状态追踪
  const lastOpenOrdersFetchTime = useRef(0);
  const lastPositionsFetchTime = useRef(0);
  const isOpenOrdersFetching = useRef(false);
  const isPositionsFetching = useRef(false);
  const FETCH_COOLDOWN = 1000; // 1秒内不重复请求

  // Fetch open orders data
  const fetchOpenOrdersData = useCallback(async () => {
    if (!clobApis || walletType === "none" || isOpenOrdersFetching.current) {
      return;
    }

    const now = Date.now();
    if (now - lastOpenOrdersFetchTime.current < FETCH_COOLDOWN) {
      return;
    }

    isOpenOrdersFetching.current = true;
    setIsLoading(true);
    lastOpenOrdersFetchTime.current = now;

    try {
      const res = await getOpenOrderData(clobApis, walletType, {
        offset: 0,
        limit: parseInt(process.env.NEXT_PUBLIC_BULK_DATA_LIMIT || "99999"),
      });

      if (res?.data?.data && Array.isArray(res.data.data)) {
        setOpenOrdersData(res.data.data);
      } else {
        setOpenOrdersData([]);
      }
    } catch (error) {
      setOpenOrdersData([]);
    } finally {
      setIsLoading(false);
      isOpenOrdersFetching.current = false;
    }
  }, [clobApis, walletType]);

  // Fetch positions data
  const fetchPositionsData = useCallback(async () => {
    if (!clobApis || !walletType || !address || isPositionsFetching.current) {
      return;
    }

    const now = Date.now();
    if (now - lastPositionsFetchTime.current < FETCH_COOLDOWN) {
      return;
    }

    isPositionsFetching.current = true;
    lastPositionsFetchTime.current = now;

    try {
      const res = await getPositionsListDataInPofolio(clobApis, walletType, {
        offset: 0,
        limit: parseInt(process.env.NEXT_PUBLIC_BULK_DATA_LIMIT || "99999"),
      });

      if (res?.data?.data && Array.isArray(res.data.data)) {
        setPositionsData(res.data.data);
        onPositionsUpdate?.(res.data.data);
      } else {
        setPositionsData([]);
        onPositionsUpdate?.([]);
      }
    } catch (error) {
      setPositionsData([]);
      onPositionsUpdate?.([]);
    } finally {
      isPositionsFetching.current = false;
    }
  }, [clobApis, walletType, address, onPositionsUpdate]);

  // Calculate current sell value based on positions and token IDs
  const updateCurrentSellValue = useCallback(
    (tokenIds: string[]) => {
      if (!tokenIds || tokenIds.length === 0) {
        return;
      }

      const result = tokenIds.map((tokenId: string, index: number) => {
        const matchingPosition = positionsData.find((position: any) => {
          const assetValue = position.asset?.String || position.asset;
          const matches = assetValue === tokenId;
          return matches;
        });

        return {
          side: index === 0 ? "yes" : "no",
          value: matchingPosition ? matchingPosition.size : 0,
        };
      });

      setCurrentSellValue(result);
    },
    [positionsData],
  );

  // Calculate frozen shares for specific token
  const calculateFrozenSharesForToken = useCallback(
    (tokenId: string): number => {
      if (!openOrdersData || !Array.isArray(openOrdersData)) {
        return 0;
      }

      const tokenSellOrders = openOrdersData.filter((order: any) => {
        const isMatchingAsset = order.asset_id === tokenId || order.asset === tokenId;
        const isSellOrder = order.side === "SELL";
        const isActive =
          order.status === "Open" || order.status === "ACTIVE" || order.status === "open" || !order.status;

        return isMatchingAsset && isSellOrder && isActive;
      });

      const frozenAmount = tokenSellOrders.reduce((total, order) => {
        const size = order.size_current || order.original_size || order.size || "0";
        const orderSize = parseInt(size, 10);
        return total + orderSize;
      }, 0);

      const frozenShares = Math.floor(frozenAmount / 10 ** 6);

      return frozenShares;
    },
    [openOrdersData],
  );

  // Calculate buy order max shares
  const calculateBuyMaxShares = useCallback(
    (price: number): number => {
      if (price <= 0) return 0;

      const feeRate = 0.02;
      const effectiveBalance = availableBalance / (1 + feeRate);
      const maxShares = Math.floor((effectiveBalance * 100) / price);

      return maxShares;
    },
    [availableBalance],
  );

  // Calculate sell order max shares
  const calculateSellMaxShares = useCallback(
    (selectedButtonType: string, orderTradeTokenId: string): number => {
      const selectedValue =
        currentSellValue.find((item: { side: string }) => item.side === selectedButtonType)?.value || 0;

      // 所有订单类型都保留小数
      const totalShares = selectedValue / 10 ** 6;

      const frozenShares = calculateFrozenSharesForToken(orderTradeTokenId);
      const availableShares = Math.max(0, totalShares - frozenShares);

      // 所有订单类型都保留2位小数
      return Math.round(availableShares * 100) / 100;
    },
    [currentSellValue, calculateFrozenSharesForToken],
  );

  // Main calculation function
  const calculateMaxShares = useCallback(
    (selectedButtonType: string, currentSelectedItem: string, orderTradeTokenId: string, price: number): number => {
      if (currentSelectedItem === "Buy") {
        return calculateBuyMaxShares(price);
      } else if (currentSelectedItem === "Sell") {
        return calculateSellMaxShares(selectedButtonType, orderTradeTokenId);
      }
      return 0;
    },
    [calculateBuyMaxShares, calculateSellMaxShares],
  );

  // Refresh functions
  const refreshAll = useCallback(async () => {
    console.log("🔄 开始刷新所有数据...");

    // 重置冷却时间，允许立即刷新
    lastOpenOrdersFetchTime.current = 0;
    lastPositionsFetchTime.current = 0;

    // 使用 Promise.allSettled 确保即使一个请求失败，另一个也能继续
    const results = await Promise.allSettled([fetchOpenOrdersData(), fetchPositionsData()]);

    let hasSuccess = false;
    results.forEach((result, index) => {
      const type = index === 0 ? "订单数据" : "持仓数据";
      if (result.status === "rejected") {
        console.error(`❌ ${type}刷新失败:`, result.reason);
      } else {
        console.log(`✅ ${type}刷新成功`);
        hasSuccess = true;
      }
    });

    if (hasSuccess) {
      console.log("🔄 数据刷新完成，等待状态更新...");
    }
  }, [fetchOpenOrdersData, fetchPositionsData]);

  // 使用 ref 来追踪初始化状态
  const isInitialized = useRef(false);

  // Initial data fetch - 只在首次加载时执行
  useEffect(() => {
    if (!isInitialized.current && clobApis && walletType && address) {
      isInitialized.current = true;

      // 使用 Promise.allSettled 确保两个请求都能执行
      Promise.allSettled([fetchOpenOrdersData(), fetchPositionsData()])
        .then(results => {
          results.forEach((result, index) => {
            const type = index === 0 ? "订单数据" : "持仓数据";
            if (result.status === "rejected") {
              console.error(`❌ 初始化${type}失败:`, result.reason);
            }
          });
        })
        .catch(error => {
          console.error("❌ 初始化数据获取失败:", error);
        });
    }
  }, [clobApis, walletType, address, fetchOpenOrdersData, fetchPositionsData]);

  // 当关键依赖项变化时重置初始化状态
  useEffect(() => {
    isInitialized.current = false;
  }, [clobApis, walletType, address]);

  return {
    calculateMaxShares,
    openOrdersData,
    positionsData,
    currentSellValue,
    isLoading,
    refreshOpenOrders: fetchOpenOrdersData,
    refreshPositions: fetchPositionsData,
    refreshAll,
    updateCurrentSellValue,
  };
}

export default useMaxSharesCalculation;
