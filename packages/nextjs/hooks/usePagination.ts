import { useCallback, useEffect, useRef, useState } from "react";

// 定义参数接口
interface PaginationParams {
  limit: number;
  offset: number;
  [key: string]: any; // 允许额外的动态属性
}

interface UsePaginationOptions {
  fetchFunction: (...args: any[]) => Promise<{ data: any }>;
  limit?: number;
  initialParams?: Partial<PaginationParams>;
  fetchParams?: {
    creds?: any;
    walletType?: string;
    [key: string]: any;
  }; // 用于传递给 fetchFunction 的额外参数
  dependencies?: any[]; // 外部依赖，变化时重置分页
}

interface UsePaginationReturn<T> {
  data: T[];
  loading: boolean;
  hasMore: boolean;
  isInitialLoad: boolean;
  loadMore: () => void;
  reset: () => void;
  refetch: () => void;
  observerRef: React.RefObject<HTMLDivElement>;
  updateParams: (newParams: Partial<PaginationParams>) => void;
}

export function usePagination<T = any>({
  fetchFunction,
  limit = 10,
  initialParams = {},
  fetchParams = {},
  dependencies = [],
}: UsePaginationOptions): UsePaginationReturn<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);
  const [params, setParams] = useState<PaginationParams>({
    ...initialParams,
    limit,
    offset: 0,
  });

  const observerRef = useRef<HTMLDivElement>(null);

  // 重置函数
  const reset = useCallback(() => {
    setData([]);
    setHasMore(true);
    setIsInitialLoad(true);
    setParams((prev: PaginationParams) => ({
      ...prev,
      offset: 0,
    }));
  }, []);

  // 获取数据函数
  const fetchData = useCallback(
    async (isLoadMore = false) => {
      if (loading) return;

      setLoading(true);
      try {
        const currentOffset = isLoadMore ? params.offset : 0;
        const requestParams = {
          ...initialParams,
          offset: currentOffset,
          limit,
        };

        let response;

        // 根据是否有额外参数决定如何调用 fetchFunction
        if (fetchParams.creds && fetchParams.walletType) {
          response = await fetchFunction(fetchParams.creds, fetchParams.walletType, requestParams);
        } else {
          response = await fetchFunction(requestParams);
        }

        if (response?.data) {
          const newData = response.data.data || [];

          if (isLoadMore) {
            setData(prev => [...prev, ...newData]);
            setParams((prev: PaginationParams) => ({
              ...prev,
              offset: prev.offset + newData.length,
            }));
          } else {
            setData(newData);
            setParams((prev: PaginationParams) => ({
              ...prev,
              offset: newData.length,
            }));
            setIsInitialLoad(false);
          }

          // 判断是否还有更多数据
          setHasMore(newData.length === limit);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setHasMore(false);
      } finally {
        setLoading(false);
      }
    },
    [fetchFunction, initialParams, limit, loading, fetchParams, params.offset],
  );

  const refetch = useCallback(async () => {
    setData([]);
    setHasMore(true);
    setIsInitialLoad(true);
    setParams(prev => ({
      ...prev,
      offset: 0,
    }));

    // 延迟一下确保状态更新完成
    setTimeout(() => {
      fetchData(false);
    }, 0);
  }, [fetchData]);

  // 外部依赖变化时重置
  useEffect(() => {
    reset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies);

  // 初始加载
  useEffect(() => {
    if (fetchParams.creds && fetchParams.walletType && fetchParams.walletType !== "none") {
      fetchData(false);
    } else if (!fetchParams.creds && (params.user || Object.keys(initialParams).length === 0)) {
      fetchData(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchParams.creds, fetchParams.walletType, params.user]);

  // 加载更多
  const loadMore = useCallback(() => {
    if (!hasMore || loading || isInitialLoad) return;
    fetchData(true);
  }, [hasMore, loading, isInitialLoad, fetchData]);

  // 更新参数
  const updateParams = useCallback(
    (newParams: Partial<PaginationParams>) => {
      setParams((prev: PaginationParams) => ({
        ...prev,
        ...newParams,
        offset: 0,
      }));
      reset();
    },
    [reset],
  );

  // Intersection Observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        const target = entries[0];
        if (target.isIntersecting && hasMore && !loading && !isInitialLoad) {
          loadMore();
        }
      },
      {
        threshold: 0.1,
        rootMargin: "20px",
      },
    );

    const currentRef = observerRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [loadMore, hasMore, loading, isInitialLoad]);

  return {
    data,
    loading,
    hasMore,
    isInitialLoad,
    loadMore,
    reset,
    refetch,
    observerRef,
    updateParams,
  };
}
