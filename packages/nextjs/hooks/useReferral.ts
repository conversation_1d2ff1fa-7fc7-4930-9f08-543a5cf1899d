import { useCallback, useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface ReferralData {
  id: string;
  email: string;
  joinDate: string;
  status: "pending" | "completed";
  bonusEarned: number;
}

interface ReferralStats {
  totalInvites: number;
  successfulInvites: number;
  bonusBoxes: number;
  totalEarnings: number;
  conversionRate: number;
  thisMonthInvites: number;
}

interface ReferralState {
  referralCode: string;
  referralLink: string;
  stats: ReferralStats;
  referralData: ReferralData[];
  isLoading: boolean;
}

interface ReferralActions {
  generateReferralCode: () => string;
  copyReferralLink: () => Promise<void>;
  shareReferralLink: () => Promise<void>;
  processReferral: (referrerCode: string) => Promise<void>;
  loadReferralData: () => void;
  addBonusBox: () => void;
}

export const useReferral = (): ReferralState & ReferralActions => {
  const [state, setState] = useState<ReferralState>({
    referralCode: "",
    referralLink: "",
    stats: {
      totalInvites: 0,
      successfulInvites: 0,
      bonusBoxes: 0,
      totalEarnings: 0,
      conversionRate: 0,
      thisMonthInvites: 0,
    },
    referralData: [],
    isLoading: false,
  });

  // 生成邀请码
  const generateReferralCode = useCallback((): string => {
    let code = localStorage.getItem("referralCode");
    if (!code) {
      code = Math.random().toString(36).substring(2, 8).toUpperCase();
      localStorage.setItem("referralCode", code);
    }
    return code;
  }, []);

  // 复制邀请链接
  const copyReferralLink = useCallback(async (): Promise<void> => {
    try {
      await navigator.clipboard.writeText(state.referralLink);
      toast.success("Referral link copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy link");
    }
  }, [state.referralLink]);

  // 分享邀请链接
  const shareReferralLink = useCallback(async (): Promise<void> => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Join MysteryUSDC",
          text: "Join me on MysteryUSDC and get your first mystery box free!",
          url: state.referralLink,
        });
      } catch (error) {
        // 用户取消分享或分享失败，回退到复制
        await copyReferralLink();
      }
    } else {
      await copyReferralLink();
    }
  }, [state.referralLink, copyReferralLink]);

  // 处理邀请（当新用户通过邀请链接注册时）
  const processReferral = useCallback(async (referrerCode: string): Promise<void> => {
    if (!referrerCode) return;

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 检查邀请码是否有效
      const isValidCode = referrerCode.length === 6 && /^[A-Z0-9]+$/.test(referrerCode);

      if (!isValidCode) {
        toast.error("Invalid referral code");
        return;
      }

      // 记录被邀请的信息
      localStorage.setItem("referredBy", referrerCode);
      localStorage.setItem("referralProcessed", "true");

      // 如果是自己的邀请码，给自己增加奖励
      const myReferralCode = localStorage.getItem("referralCode");
      if (myReferralCode === referrerCode) {
        addBonusBox();
        toast.success("🎉 Referral bonus added! You got an extra mystery box!");
      }
    } catch (error) {
      console.error("Error processing referral:", error);
      toast.error("Failed to process referral");
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  // 添加奖励盲盒
  const addBonusBox = useCallback(() => {
    const currentBonusBoxes = parseInt(localStorage.getItem("bonusBoxes") || "0");
    const newBonusBoxes = currentBonusBoxes + 1;
    localStorage.setItem("bonusBoxes", newBonusBoxes.toString());

    // 更新统计
    loadReferralData();
  }, []);

  // 加载邀请数据
  const loadReferralData = useCallback(() => {
    // 从本地存储加载数据
    const referralDataStr = localStorage.getItem("referralData");
    const referralData: ReferralData[] = referralDataStr ? JSON.parse(referralDataStr) : [];

    // 计算统计数据
    const totalInvites = referralData.length;
    const successfulInvites = referralData.filter(item => item.status === "completed").length;
    const bonusBoxes = parseInt(localStorage.getItem("bonusBoxes") || "0");
    const totalEarnings = referralData.reduce((sum, item) => sum + item.bonusEarned, 0);
    const conversionRate = totalInvites > 0 ? (successfulInvites / totalInvites) * 100 : 0;

    const thisMonthInvites = referralData.filter(item => {
      const joinDate = new Date(item.joinDate);
      const now = new Date();
      return joinDate.getMonth() === now.getMonth() && joinDate.getFullYear() === now.getFullYear();
    }).length;

    setState(prev => ({
      ...prev,
      referralData,
      stats: {
        totalInvites,
        successfulInvites,
        bonusBoxes,
        totalEarnings,
        conversionRate,
        thisMonthInvites,
      },
    }));
  }, []);

  // 模拟添加新的邀请记录（实际应用中应该通过API）
  const addReferralRecord = useCallback(
    (email: string) => {
      const newRecord: ReferralData = {
        id: Date.now().toString(),
        email,
        joinDate: new Date().toISOString().split("T")[0],
        status: "completed",
        bonusEarned: Math.round((Math.random() * 2 + 0.5) * 100) / 100, // 0.5-2.5 USDC
      };

      const existingData = localStorage.getItem("referralData");
      const referralData: ReferralData[] = existingData ? JSON.parse(existingData) : [];
      referralData.push(newRecord);

      localStorage.setItem("referralData", JSON.stringify(referralData));
      addBonusBox();
      loadReferralData();

      toast.success(`🎉 ${email} joined through your referral! You earned a bonus box!`);
    },
    [addBonusBox, loadReferralData],
  );

  // 初始化
  useEffect(() => {
    const code = generateReferralCode();
    const link = `${window.location.origin}/welcome?ref=${code}`;

    setState(prev => ({
      ...prev,
      referralCode: code,
      referralLink: link,
    }));

    loadReferralData();

    // 检查URL中的邀请码
    const urlParams = new URLSearchParams(window.location.search);
    const refCode = urlParams.get("ref");
    if (refCode && !localStorage.getItem("referralProcessed")) {
      processReferral(refCode);
    }
  }, [generateReferralCode, loadReferralData, processReferral]);

  // 开发模式下的测试功能
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      // 添加全局测试函数
      (window as any).testReferral = () => {
        const testEmails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"];
        const randomEmail = testEmails[Math.floor(Math.random() * testEmails.length)];
        addReferralRecord(randomEmail);
      };
    }
  }, [addReferralRecord]);

  return {
    ...state,
    generateReferralCode,
    copyReferralLink,
    shareReferralLink,
    processReferral,
    loadReferralData,
    addBonusBox,
  };
};
