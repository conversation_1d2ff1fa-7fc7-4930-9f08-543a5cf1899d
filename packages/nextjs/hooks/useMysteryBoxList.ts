"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import {
  type MysteryBoxHistory,
  type MysteryBoxItem,
  getMysteryBoxHistory,
  getUserBoxList,
  openMysteryBox,
} from "@/api/mystery/mystery-box";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useMagicStore } from "@/services/store/magicStore";
import { signMysteryBoxAuth } from "@/utils/signature/signMysteryBoxAuth";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

interface MysteryBoxListState {
  readyBoxes: MysteryBoxItem[];
  cooldownBoxes: MysteryBoxItem[];
  loading: boolean;
  opening: { [boxId: number]: boolean };
  error: string | null;
  history: MysteryBoxHistory[];
  historyLoading: boolean;
  stats: {
    totalBoxes: number;
    totalRewards: number;
    statusCounts: {
      ready: number;
      cooldown: number;
    };
  } | null;
}

export const useMysteryBoxList = () => {
  const { t } = useTranslation();
  const { address, isConnected } = useUserAddress();
  const { magic: magicInstance } = useMagicStore();

  // 使用 ref 来跟踪初始化状态，避免重复调用
  const initializationRef = useRef<{
    isInitializing: boolean;
    lastAddress: string;
    lastConnectedState: boolean;
  }>({
    isInitializing: false,
    lastAddress: "",
    lastConnectedState: false,
  });

  const [state, setState] = useState<MysteryBoxListState>({
    readyBoxes: [],
    cooldownBoxes: [],
    loading: false,
    opening: {},
    error: null,
    history: [],
    historyLoading: false,
    stats: null,
  });

  // 初始化盲盒数据
  const initializeData = useCallback(
    async (forceRefresh = false) => {
      if (!address || !isConnected) return;

      // 防止重复初始化（除非强制刷新）
      const currentRef = initializationRef.current;
      if (
        !forceRefresh &&
        (currentRef.isInitializing ||
          (currentRef.lastAddress === address && currentRef.lastConnectedState === isConnected))
      ) {
        console.log("🔄 跳过重复初始化调用", {
          isInitializing: currentRef.isInitializing,
          lastAddress: currentRef.lastAddress,
          currentAddress: address,
          lastConnectedState: currentRef.lastConnectedState,
          currentConnectedState: isConnected,
        });
        return;
      }

      if (forceRefresh) {
        console.log("🔄 强制刷新盲盒数据", { address, isConnected });
      }

      // 标记正在初始化
      initializationRef.current = {
        isInitializing: true,
        lastAddress: address,
        lastConnectedState: isConnected,
      };

      setState(prev => ({ ...prev, loading: true, error: null }));

      try {
        // 获取盲盒列表
        const boxListResult = await getUserBoxList(address);

        // 处理盲盒列表结果
        if (boxListResult.success) {
          const boxData = boxListResult.data;

          // 完全基于 timestamp_next_ready 判断状态，确保使用 UTC 时间比较
          const nowUTC = new Date().getTime(); // 这已经是 UTC 时间戳

          const readyBoxes = boxData.boxes.filter(box => {
            if (!box.timestamp_next_ready) return true; // 如果没有冷却时间，认为可领取

            // 确保 timestamp_next_ready 被正确解析为 UTC 时间
            const nextReadyTimeUTC = new Date(
              box.timestamp_next_ready + (box.timestamp_next_ready.endsWith("Z") ? "" : "Z"),
            ).getTime();
            const isReady = nextReadyTimeUTC <= nowUTC;

            return isReady; // 冷却时间已过，可领取
          });
          const cooldownBoxes = boxData.boxes.filter(box => {
            if (!box.timestamp_next_ready) return false; // 如果没有冷却时间，不在冷却中
            const nextReadyTimeUTC = new Date(
              box.timestamp_next_ready + (box.timestamp_next_ready.endsWith("Z") ? "" : "Z"),
            ).getTime();
            return nextReadyTimeUTC > nowUTC; // 冷却时间未过，冷却中
          });

          setState(prev => ({
            ...prev,
            readyBoxes,
            cooldownBoxes,
            stats: boxData.summary
              ? {
                  totalBoxes: boxData.summary.totalBoxes,
                  totalRewards: boxData.summary.totalRewards,
                  statusCounts: {
                    ready: boxData.summary.statusCounts.ready,
                    cooldown: boxData.summary.statusCounts.cooldown,
                  },
                }
              : {
                  totalBoxes: boxData.boxes.length,
                  totalRewards: boxData.boxes.reduce((sum, box) => sum + box.reward, 0),
                  statusCounts: {
                    ready: readyBoxes.length,
                    cooldown: cooldownBoxes.length,
                  },
                },
            loading: false,
          }));

          console.log("✅ 盲盒数据初始化完成，地址:", address);
        } else {
          throw new Error("Failed to fetch mystery boxes");
        }
      } catch (error: any) {
        console.error("❌ 初始化盲盒数据失败:", error);
        setState(prev => ({
          ...prev,
          error: error.message || "Failed to initialize mystery box data",
          loading: false,
        }));
      } finally {
        // 重置初始化状态
        initializationRef.current.isInitializing = false;
      }
    },
    [address, isConnected],
  );

  // 单独获取盲盒列表（用于刷新）
  const fetchMysteryBoxes = useCallback(async () => {
    if (!address || !isConnected) return;

    try {
      const response = await getUserBoxList(address);

      if (response.success) {
        // 完全基于 timestamp_next_ready 判断状态，确保使用 UTC 时间比较
        const nowUTC = new Date().getTime();
        const readyBoxes = response.data.boxes.filter(box => {
          if (!box.timestamp_next_ready) return true; // 如果没有冷却时间，认为可领取
          const nextReadyTimeUTC = new Date(
            box.timestamp_next_ready + (box.timestamp_next_ready.endsWith("Z") ? "" : "Z"),
          ).getTime();
          return nextReadyTimeUTC <= nowUTC; // 冷却时间已过，可领取
        });
        const cooldownBoxes = response.data.boxes.filter(box => {
          if (!box.timestamp_next_ready) return false; // 如果没有冷却时间，不在冷却中
          const nextReadyTimeUTC = new Date(
            box.timestamp_next_ready + (box.timestamp_next_ready.endsWith("Z") ? "" : "Z"),
          ).getTime();
          return nextReadyTimeUTC > nowUTC; // 冷却时间未过，冷却中
        });

        setState(prev => ({
          ...prev,
          readyBoxes,
          cooldownBoxes,
          stats: response.data.summary
            ? {
                totalBoxes: response.data.summary.totalBoxes,
                totalRewards: response.data.summary.totalRewards,
                statusCounts: {
                  ready: response.data.summary.statusCounts.ready,
                  cooldown: response.data.summary.statusCounts.cooldown,
                },
              }
            : {
                totalBoxes: response.data.boxes.length,
                totalRewards: response.data.boxes.reduce((sum, box) => sum + box.reward, 0),
                statusCounts: {
                  ready: readyBoxes.length,
                  cooldown: cooldownBoxes.length,
                },
              },
        }));
      }
    } catch (error: any) {
      console.error("Error fetching mystery boxes:", error);
    }
  }, [address, isConnected]);

  // 获取历史记录
  const getHistory = useCallback(
    async (page = 1, limit = 10) => {
      if (!address) return;

      setState(prev => ({ ...prev, historyLoading: true }));

      try {
        const response = await getMysteryBoxHistory(address, page, limit);

        if (response.success) {
          setState(prev => ({
            ...prev,
            history: response.data.transactions,
            historyLoading: false,
          }));
        }
      } catch (error) {
        console.error("Error fetching history:", error);
        setState(prev => ({
          ...prev,
          history: [],
          historyLoading: false,
        }));
      }
    },
    [address],
  );

  // 开启盲盒 - 使用硬编码配置进行EIP-712签名
  const openBox = useCallback(
    async (boxId: number) => {
      if (!address) return null;

      // 盲盒功能仅限Magic用户使用（在页面级别已经过滤）

      // 检查冷却状态（从已有数据）
      const targetBox = [...state.readyBoxes, ...state.cooldownBoxes].find(box => box.id === boxId);
      if (!targetBox) {
        toast.error(t("mysteryBox.errors.boxNotFound"), { duration: 3000 });
        return null;
      }

      // 基于 timestamp_next_ready 检查冷却状态，确保使用 UTC 时间比较
      if (targetBox.timestamp_next_ready) {
        const nextReadyTimeUTC = new Date(
          targetBox.timestamp_next_ready + (targetBox.timestamp_next_ready.endsWith("Z") ? "" : "Z"),
        ).getTime();
        const nowUTC = new Date().getTime();
        if (nextReadyTimeUTC > nowUTC) {
          toast.error(t("mysteryBox.errors.boxCooldown"), { duration: 3000 });
          return null;
        }
      }

      setState(prev => ({
        ...prev,
        opening: { ...prev.opening, [boxId]: true },
      }));

      // 显示处理中的提示
      toast.loading(t("mysteryBox.toast.processing"), {
        id: `opening-${boxId}`,
        duration: 30000, // 30秒后自动消失
      });

      try {
        // 使用EIP-712签名（仅Magic用户）
        const signatureData = await signMysteryBoxAuth(address, magicInstance);
        // 添加盲盒ID到请求数据中
        const requestData = {
          ...signatureData,
          mysteryBoxId: boxId, // 🎯 传递盲盒ID
        };
        const result = await openMysteryBox(requestData);

        if (result.success && result.data) {
          // 关闭加载提示并显示成功消息
          toast.dismiss(`opening-${boxId}`);
          toast.success(t("mysteryBox.toast.successReward", { amount: result.data.reward }), {
            duration: 4000,
            style: {
              background: "#10B981",
              color: "white",
              fontSize: "16px",
              padding: "16px",
            },
          });

          // 开盒后刷新数据：同时更新盲盒列表和历史记录
          await Promise.all([
            fetchMysteryBoxes(),
            getHistory(), // 更新历史记录
          ]);

          return {
            success: true,
            data: {
              reward: result.data.reward,
              txHash: result.data.txHash,
            },
          };
        } else {
          toast.dismiss(`opening-${boxId}`);
          toast.error(t("mysteryBox.toast.openFailed"), {
            duration: 3000,
          });
          return null;
        }
      } catch (error: any) {
        // 关闭加载提示
        toast.dismiss(`opening-${boxId}`);
        console.error("❌ 开盒错误:", error);

        // 更详细的错误处理
        if (error.code === 4001 || error.message?.includes("User rejected")) {
          toast.error(t("mysteryBox.errors.userCancelled"), {
            duration: 3000,
          });
        } else if (error.code === "ECONNABORTED" || error.message?.includes("timeout")) {
          toast.error(t("mysteryBox.errors.requestTimeout"), {
            duration: 5000,
            style: {
              background: "#EF4444",
              color: "white",
            },
          });
        } else if (error.message?.includes("签名验证失败")) {
          toast.error(t("mysteryBox.errors.signatureVerificationFailed"), {
            duration: 4000,
          });
        } else {
          toast.error(
            t("mysteryBox.errors.claimFailed", { error: error.message || t("mysteryBox.errors.unknownError") }),
            {
              duration: 4000,
            },
          );
        }
        return null;
      } finally {
        setState(prev => ({
          ...prev,
          opening: { ...prev.opening, [boxId]: false },
        }));
      }
    },
    [address, magicInstance, state.readyBoxes, state.cooldownBoxes, fetchMysteryBoxes, getHistory, t],
  );

  // 计算剩余冷却时间（基于 timestamp_next_ready 实时计算）
  const getRemainingCooldown = useCallback((box: MysteryBoxItem) => {
    if (!box.timestamp_next_ready) {
      return 0;
    }

    // 强制添加Z后缀确保UTC时间解析
    let utcTimeString = box.timestamp_next_ready;
    if (!utcTimeString.endsWith("Z")) {
      utcTimeString += "Z";
    }

    const endTime = new Date(utcTimeString).getTime();
    const now = Date.now(); // 使用 Date.now() 获取UTC时间戳
    const remaining = Math.max(0, endTime - now);
    const seconds = Math.floor(remaining / 1000);

    return seconds; // 返回秒数
  }, []);

  // 检查是否有盲盒从冷却状态变为可领取状态
  const checkAndUpdateExpiredBoxes = useCallback(() => {
    if (state.cooldownBoxes.length === 0) return;

    const nowUTC = Date.now();
    let hasExpiredBoxes = false;

    // 检查冷却中的盲盒是否有已过期的
    for (const box of state.cooldownBoxes) {
      if (box.timestamp_next_ready) {
        const nextReadyTimeUTC = new Date(
          box.timestamp_next_ready + (box.timestamp_next_ready.endsWith("Z") ? "" : "Z"),
        ).getTime();

        if (nextReadyTimeUTC <= nowUTC) {
          hasExpiredBoxes = true;
          break;
        }
      }
    }

    // 如果有过期的盲盒，重新获取数据并更新状态
    if (hasExpiredBoxes) {
      console.log("🔄 检测到盲盒冷却时间结束，自动刷新状态");
      fetchMysteryBoxes();
    }
  }, [state.cooldownBoxes, fetchMysteryBoxes]);

  // 格式化冷却时间显示
  const formatCooldownTime = useCallback((seconds: number) => {
    if (seconds <= 0) {
      return "00:00";
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    let result;
    if (hours > 0) {
      result = `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    } else {
      result = `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }

    return result;
  }, []);

  // 初始化数据 - 使用防抖机制避免重复调用
  useEffect(() => {
    if (isConnected && address) {
      // 使用 setTimeout 进行防抖，避免快速连续的状态变化
      const timeoutId = setTimeout(() => {
        initializeData();
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [isConnected, address, initializeData]);

  // 地址切换时重置初始化状态
  useEffect(() => {
    const currentRef = initializationRef.current;
    if (address && address !== currentRef.lastAddress && currentRef.lastAddress !== "") {
      console.log("🔄 检测到地址切换，重置初始化状态", {
        oldAddress: currentRef.lastAddress,
        newAddress: address,
      });
      // 重置初始化状态，允许新地址初始化
      initializationRef.current = {
        isInitializing: false,
        lastAddress: "",
        lastConnectedState: false,
      };
    }
  }, [address]);

  return {
    // 状态
    ...state,

    // 计算属性
    totalBoxes: state.stats?.totalBoxes || state.readyBoxes.length + state.cooldownBoxes.length,
    hasBoxes: state.readyBoxes.length > 0 || state.cooldownBoxes.length > 0,
    isAnyBoxOpening: Object.values(state.opening).some(isOpening => isOpening),

    // 方法
    initializeData,
    fetchMysteryBoxes,
    openBox,
    getRemainingCooldown,
    formatCooldownTime,
    getHistory,
    checkAndUpdateExpiredBoxes,

    // 便捷访问
    totalReady: state.stats?.statusCounts.ready || state.readyBoxes.length,
    totalCooldown: state.stats?.statusCounts.cooldown || state.cooldownBoxes.length,
    totalRewards: state.stats?.totalRewards || 0,
  };
};
