import { useCallback, useEffect, useState } from "react";
import computeProxyAddress from "@/contracts/computeProxyAddress";
import { useUserAddress } from "@/hooks/useUserAddress";
import { getItem, setItem } from "@/utils/other/storage";

export function usePromotionModal() {
  const [promotionVisible, setPromotionVisible] = useState(false);
  const [proxyWallet, setProxyWallet] = useState("");
  const { address: userAddress } = useUserAddress();

  const whitelistWallets = [
    "******************************************",
    "******************************************",
    "******************************************",
  ].map(addr => addr.toLowerCase());

  const getProxyWallet = useCallback(async () => {
    if (!userAddress) return;

    try {
      const proxyAddress = await computeProxyAddress(userAddress);
      if (proxyAddress) {
        setProxyWallet(proxyAddress);
      }
    } catch (error) {
      console.error("Failed to compute proxy address:", error);
    }
  }, [userAddress]);

  // 获取 proxyWallet
  useEffect(() => {
    if (userAddress) {
      const localProxyWallet = getItem(`login_proxyWallet`);
      if (localProxyWallet) {
        setProxyWallet(localProxyWallet);
      } else {
        getProxyWallet();
      }
    } else {
      setProxyWallet("");
    }
  }, [userAddress, getProxyWallet]);

  // 检查是否显示促销弹窗
  useEffect(() => {
    // 检查当前 proxyWallet 是否在白名单中
    if (!proxyWallet || !whitelistWallets.includes(proxyWallet.toLowerCase())) {
      return;
    }

    const lastShown = getItem("lastTemporaryPromotionModalShown");
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;

    if (!lastShown || now - lastShown > oneDay) {
      const timer = setTimeout(() => {
        setPromotionVisible(true);
        setItem("lastTemporaryPromotionModalShown", now);
      }, 2000); // 2秒延迟

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [proxyWallet]);

  return { promotionVisible, setPromotionVisible, proxyWallet };
}
