import { useCallback, useEffect } from "react";
import { getOpenOrderData } from "@/api/order";
import { calculateOrderTotalWithFee } from "@/utils";
import { useGlobalState } from "~~/services/store/store";

const BULK_DATA_LIMIT = parseInt(process.env.NEXT_PUBLIC_BULK_DATA_LIMIT || "99999");

export function useFrozen(clobApis: string) {
  const { walletType } = useGlobalState();
  const frozenValue = useGlobalState(state => state.frozenValue);
  const setFrozenValue = useGlobalState(state => state.setFrozenValue);

  const refreshFrozenValue = useCallback(
    async (clobApis?: string, forceRefresh = false) => {
      if (!clobApis || walletType == "none") {
        return;
      }

      try {
        const params = {
          offset: 0,
          limit: BULK_DATA_LIMIT,
          ...(forceRefresh && {
            _t: Date.now(),
            force: true,
          }),
        };

        const res = await getOpenOrderData(clobApis, walletType, params);

        if (res?.data?.data && Array.isArray(res.data.data)) {
          let totalFrozenValue = 0;

          res.data.data.forEach((order: any) => {
            const { side, size_current, price } = order;
            if (side === "BUY") {
              const orderTotal = calculateOrderTotalWithFee(side, size_current, price);
              totalFrozenValue += orderTotal;
            }
          });

          setFrozenValue(totalFrozenValue);
          return totalFrozenValue;
        } else {
          setFrozenValue(0);
          return 0;
        }
      } catch (error) {
        console.error("❌ Error fetching open order data:", error);
        return null;
      }
    },
    [setFrozenValue, walletType],
  );

  useEffect(() => {
    if (clobApis && walletType !== "none") {
      refreshFrozenValue(clobApis);
    }
  }, [clobApis, refreshFrozenValue, walletType]);

  // 立即锁定资金（在下单时调用）
  const lockFunds = useCallback(
    (amount: number) => {
      const newFrozenValue = frozenValue + amount;
      console.log(`🔒 Locking funds immediately: ${frozenValue} + ${amount} = ${newFrozenValue}`);
      setFrozenValue(newFrozenValue);
    },
    [frozenValue, setFrozenValue],
  );

  // 释放锁定资金（在订单失败时调用）
  const unlockFunds = useCallback(
    (amount: number) => {
      const newFrozenValue = Math.max(0, frozenValue - amount);
      console.log(`🔓 Unlocking funds: ${frozenValue} - ${amount} = ${newFrozenValue}`);
      setFrozenValue(newFrozenValue);
    },
    [frozenValue, setFrozenValue],
  );

  return { frozenValue, refreshFrozenValue, lockFunds, unlockFunds };
}

export default useFrozen;
