import { useCallback, useEffect, useState } from "react";
import { checkIfContract } from "@/contracts/checkIfContract";
import { hasApproved } from "@/contracts/encodeMultiSendTransaction";
import { useUserAddress } from "@/hooks/useUserAddress";
import { getItem } from "@/utils";
import { useTranslation } from "react-i18next";

interface WalletStatus {
  isActiveWallet: boolean;
  isApproved: boolean;
  error?: string;
}

export const useWalletValidation = () => {
  const { t } = useTranslation();
  const { address, isConnected } = useUserAddress();
  const [isValidating, setIsValidating] = useState(false);
  const [walletStatus, setWalletStatus] = useState<WalletStatus>({
    isActiveWallet: false,
    isApproved: false,
  });

  // 确保 proxyWallet 类型正确
  const proxyWallet = getItem(`login_proxyWallet`) as string | null;

  // 验证钱包状态
  const validateWallet = useCallback(async (): Promise<string | false> => {
    if (!address || !proxyWallet) {
      setWalletStatus({
        isActiveWallet: false,
        isApproved: false,
        error: t("wallet_not_connected"),
      });
      return false;
    }

    setIsValidating(true);

    try {
      // 检查是否为合约钱包 - 移除类型声明，让 TypeScript 推断
      const contractResult = await checkIfContract(proxyWallet);
      const isActiveWallet = Boolean(contractResult); // 转换为布尔值

      if (!isActiveWallet) {
        setWalletStatus({
          isActiveWallet: false,
          isApproved: false,
          error: t("invalid_wallet_type"),
        });
        setIsValidating(false);
        return "invalid_wallet";
      }

      // 检查授权状态 - hasApproved 返回字符串，需要转换为布尔值
      const approvalStatus = await hasApproved(proxyWallet);
      const isApproved = approvalStatus === "success";

      setWalletStatus({
        isActiveWallet: true,
        isApproved,
        error: isApproved ? undefined : t("wallet_not_approved"),
      });

      setIsValidating(false);
      return isApproved ? "approved" : "not_approved";
    } catch (error) {
      console.error("钱包验证失败:", error);
      setWalletStatus({
        isActiveWallet: false,
        isApproved: false,
        error: t("wallet_validation_failed"),
      });
      setIsValidating(false);
      return false;
    }
  }, [address, proxyWallet, t]);

  // 初始化时验证钱包状态
  useEffect(() => {
    if (isConnected && address && proxyWallet) {
      validateWallet();
    } else {
      // 重置状态当连接状态改变时
      setWalletStatus({
        isActiveWallet: false,
        isApproved: false,
      });
    }
  }, [isConnected, address, proxyWallet, validateWallet]);

  return {
    walletStatus,
    isValidating,
    validateWallet,
    proxyWallet,
    isConnected,
    address,
    // 添加便捷访问属性
    isApproved: walletStatus.isApproved,
    isActiveWallet: walletStatus.isActiveWallet,
    error: walletStatus.error,
  };
};
