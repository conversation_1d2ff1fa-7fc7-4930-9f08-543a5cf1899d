import { useEffect, useState } from "react";
import useNavigationStore from "@/services/store/navigationStore";

const useEventParams = (initialParams: any) => {
  const { currentNavItem } = useNavigationStore();
  const [eventParams, setEventParams] = useState(initialParams);

  useEffect(() => {
    if (currentNavItem?.id) {
      setEventParams((prevParams: any) => ({
        ...prevParams,
        tag_id: currentNavItem.id,
      }));
    }
  }, [currentNavItem?.id]);

  return [eventParams, setEventParams] as const;
};

export default useEventParams;
