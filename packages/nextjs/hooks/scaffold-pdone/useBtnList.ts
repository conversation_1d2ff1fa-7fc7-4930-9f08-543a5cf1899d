import { useEffect, useState } from "react";
import { getBooksList } from "@/api/markets/detail";

const useBtnList = (marketsDataList: any[], selectedButtonType: string, selectedItemId: number) => {
  const [btnData, setBtnList] = useState<any>({});
  const [btnDataList, setBtnDataList] = useState<any[]>([]);

  useEffect(() => {
    const fetchBtnData = async () => {
      const tokenIds = marketsDataList
        .flatMap((item: { question_market: { clob_token_ids: string[] } }) => [
          { token_id: item.question_market.clob_token_ids?.[0] },
          { token_id: item.question_market.clob_token_ids?.[1] },
        ])
        .filter(token => token.token_id);

      if (tokenIds.length === 0) return;

      try {
        const response = await getBooksList(tokenIds);
        const booksData = response.data;

        const newBtnDataList = marketsDataList.map(
          (item: { question_market: { id: number; clob_token_ids: string[] } }) => {
            const yesTokenId = item.question_market.clob_token_ids?.[0];
            const noTokenId = item.question_market.clob_token_ids?.[1];

            const yesBookData = booksData.find((book: any) => book.asset_id === yesTokenId);
            const noBookData = booksData.find((book: any) => book.asset_id === noTokenId);

            return {
              yesBookData,
              noBookData,
              token_id: item.question_market.id,
            };
          },
        );

        setBtnDataList(newBtnDataList);

        const selectedMarket = newBtnDataList.find(data => data.token_id === selectedItemId);
        if (selectedMarket) {
          setBtnList(selectedMarket);
        }
      } catch (error) {
        console.error("Error:", error);
      }
    };

    fetchBtnData();
  }, [marketsDataList, selectedItemId, selectedButtonType]);

  useEffect(() => {
    const selectedMarket = btnDataList.find(data => data.token_id === selectedItemId);
    if (selectedMarket) {
      setBtnList(selectedMarket);
    }
  }, [selectedButtonType, selectedItemId, btnDataList]);

  const getMatchingBook = (qid: number) => {
    const matchingData = btnDataList.find(data => data.token_id === qid);

    if (!matchingData) {
      return null;
    }

    if (selectedButtonType === "yes") {
      return matchingData.yesBookData;
    } else if (selectedButtonType === "no") {
      return matchingData.noBookData;
    }

    return null;
  };

  return { btnData, btnDataList, getMatchingBook, setBtnList, setBtnDataList };
};

export default useBtnList;
