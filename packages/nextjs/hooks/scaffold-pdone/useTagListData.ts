import { useEffect, useMemo, useState } from "react";
import { getTagsByRegion } from "@/api/markets";
import { filterOutCreatorTags, getItem, removeDuplicatesByTagSlug } from "@/utils";

export interface TagTypes {
  id: string;
  label: string;
  slug: string;
  forceShow: boolean;
  publishedAt: string;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
  forceHide: boolean;
}
const allTag: TagTypes = {
  id: "all",
  label: "All",
  slug: "all",
  forceShow: true,
  publishedAt: "",
  updatedBy: 0,
  createdAt: "",
  updatedAt: "",
  forceHide: false,
};

const useTagListData = () => {
  const currentNavItemKey = "currentNavItem";
  const currentNavItem = useMemo(() => getItem(currentNavItemKey), [currentNavItemKey]);

  const [tagListData, setTagListData] = useState<TagTypes[]>([]);

  useEffect(() => {
    if (currentNavItem?.region) {
      getTagsByRegion(currentNavItem.region).then(res => {
        const uniqueItems = removeDuplicatesByTagSlug(res.data.tags);
        // 过滤掉creator相关的tag，但保留其他标签包括单边事件
        const filteredOutCreator = filterOutCreatorTags(uniqueItems, true); // 使用高级过滤
        setTagListData([allTag, ...filteredOutCreator]);
      });
    }
  }, [currentNavItem]);

  return tagListData;
};

export default useTagListData;
