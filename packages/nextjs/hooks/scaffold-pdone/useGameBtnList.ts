import { useEffect, useState } from "react";
import { getBooksList } from "@/api/markets/detail";

const useGameBtnList = (marketsDataList: any[], selectedButtonType: string, selectedItemId: number) => {
  const [btnData, setBtnList] = useState<any>({});
  const [btnDataList, setBtnDataList] = useState<any[]>([]);

  // 获取所有市场的按钮数据
  useEffect(() => {
    const fetchBtnData = async () => {
      if (!marketsDataList || marketsDataList.length === 0) {
        setBtnDataList([]);
        return;
      }

      // 收集所有市场的 token IDs - 对于 Game 卡片只收集 Yes 的情况（clob_token_ids[0]）
      const tokenIds = marketsDataList
        .flatMap(event => event.event_markets || [])
        .map((item: { question_market: { clob_token_ids: string[] } }) => ({
          token_id: item.question_market.clob_token_ids?.[0],
        }))
        .filter(token => token.token_id);

      if (tokenIds.length === 0) {
        setBtnDataList([]);
        return;
      }

      try {
        const response = await getBooksList(tokenIds);
        const booksData = response.data;

        // 为每个市场创建按钮数据 - 只处理 Yes 的情况
        const newBtnDataList = marketsDataList
          .flatMap(event => event.event_markets || [])
          .map((item: { question_market: { id: number; clob_token_ids: number[] } }) => {
            const marketId = item.question_market.id;
            const yesTokenId = item.question_market.clob_token_ids?.[0]?.toString();

            const yesBookData = booksData.find((book: any) => book.asset_id === yesTokenId);

            return {
              token_id: marketId, // 使用市场 ID 作为主键
              marketId: marketId, // 明确的市场 ID
              yesBookData,
              yesTokenId,
              // 对于 Game 卡片，不需要 No 的数据
              noBookData: null,
              noTokenId: null,
            };
          });

        setBtnDataList(newBtnDataList);
      } catch (error) {
        console.error("Error fetching button data:", error);
        setBtnDataList([]);
      }
    };

    fetchBtnData();
  }, [marketsDataList]); // 只依赖 marketsDataList

  // 获取当前选中市场的按钮数据
  useEffect(() => {
    if (selectedItemId && btnDataList.length > 0) {
      const currentBtnData = btnDataList.find((item: any) => item.marketId === Number(selectedItemId));
      setBtnList(currentBtnData || {});
    } else {
      setBtnList({});
    }
  }, [btnDataList, selectedItemId]); // 移除 selectedButtonType 依赖

  const getMatchingBook = (selectedButtonType: string) => {
    if (selectedButtonType === "yes") {
      return btnData.yesBookData;
    } else if (selectedButtonType === "no") {
      return btnData.noBookData;
    }
  };

  return { btnData, btnDataList, getMatchingBook, setBtnList, setBtnDataList };
};

export default useGameBtnList;
