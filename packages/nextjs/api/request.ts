import request from "@/hooks/axios";
import nextConfig from "@/next.config";
import { AxiosRequestConfig, AxiosResponse } from "axios";

interface MyRequestConfig extends AxiosRequestConfig {
  url: string;
  retryCount?: number;
  retryDelay?: number;
}

const executeWithRetry = async (
  requestFn: () => Promise<AxiosResponse>,
  maxRetries = 3,
  delay = 1000,
): Promise<AxiosResponse> => {
  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error: any) {
      lastError = error;

      // 如果是最后一次尝试，直接抛出错误
      if (attempt === maxRetries) {
        break;
      }

      // 检查是否是可重试的错误
      const isRetryableError =
        error.code === "NETWORK_ERROR" ||
        error.code === "TIMEOUT" ||
        error.code === "ECONNABORTED" ||
        error.message?.includes("network") ||
        error.message?.includes("timeout") ||
        error.message?.includes("Signer is undefined") ||
        (error.response?.status >= 500 && error.response?.status < 600);

      console.log("🔍 Retry check:", {
        attempt: attempt + 1,
        maxRetries: maxRetries + 1,
        isRetryableError,
        errorMessage: error.message,
        errorCode: error.code,
        errorStatus: error.response?.status,
      });

      if (!isRetryableError) {
        console.log("❌ Error is not retryable, stopping retry attempts");
        break;
      }

      const retryDelay = delay * Math.pow(2, attempt); // 指数退避：1s, 2s, 4s
      console.log(`🔄 Request failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${retryDelay}ms...`, {
        error: error.message,
        code: error.code,
        status: error.response?.status,
        url: error.config?.url,
      });

      // 等待指定时间后重试
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  throw lastError;
};

// 定义 myRequest 函数, 用于处理重复请求（节流）和重试
const myRequest = (function () {
  let hasRequest: string[] = [];

  return async function (config: MyRequestConfig): Promise<AxiosResponse> {
    if (hasRequest.includes(config.url)) {
      return Promise.reject(new Error("重复请求"));
    }

    // 将 pageSize 和 offset 配置添加到请求参数中
    config.params = {
      limit: nextConfig.pageSize[0], // 使用第一个 pageSize 值，或者根据需要选择其他值
      offset: nextConfig.offset,
      ...config.params,
    };

    const { retryCount = 3, retryDelay = 1000, ...requestConfig } = config;

    hasRequest.push(config.url);

    try {
      const response = await executeWithRetry(() => request(requestConfig), retryCount, retryDelay);
      hasRequest = hasRequest.filter(item => item !== config.url);
      return response;
    } catch (error) {
      hasRequest = hasRequest.filter(item => item !== config.url);
      throw error;
    }
  };
})();

export { myRequest as request, request as initRequest };
