import { initRequest } from "@/api/request";

interface Params {
  interval: string;
  fidelity: string;
}

const getParams = (timeRange: string): Params => {
  switch (timeRange) {
    case "1H":
      return { interval: "1h", fidelity: "1" };
    case "6H":
      return { interval: "6h", fidelity: "1" };
    case "1D":
      return { interval: "1d", fidelity: "5" };
    case "1W":
      return { interval: "1w", fidelity: "30" };
    case "1M": {
      return { interval: "1m", fidelity: "180" };
    }
    case "ALL": {
      return { interval: "all", fidelity: "720" };
    }
    default:
      return { interval: "all", fidelity: "720" };
  }
};

const getHistoryData = (firstClobTokenId: string, timeRange: any) => {
  const params = getParams(timeRange);
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/prices-history`,
    method: "get",
    params: {
      market: firstClobTokenId,
      interval: params.interval,
      fidelity: params.fidelity,
    },
  });
};

const getGraphHistoryData = (tokenId: any, timeRange: any) => {
  const params = getParams(timeRange);
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_CLOB_ENDPOINT}/prices-history`,
    method: "get",
    params: {
      market: tokenId,
      interval: params.interval,
      fidelity: params.fidelity,
    },
  });
};

export { getHistoryData, getGraphHistoryData };
