import { initRequest } from "@/api/request";

const getCurrentEventData = (id: any) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getDetailById?tid=${id}`,
    method: "get",
  });
};

const getCurrentItemRewards = (conditionId: any) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_GAMMA_ENDPOINT}/rewards/markets/${conditionId}`,
    method: "get",
  });
};

const getBooksList = (tokenIds: { token_id: string }[]) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/books`,
    method: "post",
    data: tokenIds,
  });
};

const getQuestionByConditionId = (conditionId: string) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getQuestionByConditionId`,
    method: "get",
    params: {
      conditionId: conditionId,
    },
  });
};

const getCommentsList = () => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getCommentsList`,
    method: "get",
  });
};

export { getCurrentEventData, getCurrentItemRewards, getBooksList, getQuestionByConditionId, getCommentsList };
