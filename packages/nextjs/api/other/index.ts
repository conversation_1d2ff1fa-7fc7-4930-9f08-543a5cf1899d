import { initRequest } from "../request";

const getGeoInfo = () => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/geo`,
    method: "get",
  });
};

/**
 * Check if remote pictures exist
 */
const checkImageExists = async (url: string): Promise<boolean> => {
  try {
    await initRequest({
      url,
      method: "head",
    });
    return true;
  } catch {
    return false;
  }
};
export { getGeoInfo, checkImageExists };
