import { initRequest } from "@/api/request";

const getSportGamesEventsListData = (params: any) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { limit, ...restParams } = params;

  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/sortEventsByTrending`,
    method: "get",
    params: {
      limit: "20",
      ...restParams,
    },
  });
};

export { getSportGamesEventsListData };
