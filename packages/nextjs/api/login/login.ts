import axios from "axios";

export default async function fetchLogin(authHeaderBase64: string, socialMediaDataBase64?: string) {
  try {
    const response = await axios({
      url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/login`,
      method: "get",
      headers: {
        Authorization: `${authHeaderBase64}`,
        userInfo: `${socialMediaDataBase64 || ""}`,
      },
    });

    return response;
  } catch (error) {
    throw error;
  }
}
