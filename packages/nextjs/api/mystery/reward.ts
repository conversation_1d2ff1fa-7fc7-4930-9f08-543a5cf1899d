import axios from "axios";

const REWARD_API_BASE = (process.env.NEXT_PUBLIC_MYSTERY_BOX_API || "http://localhost:5201").replace(
  /\/api(\/mystery-box)?$/,
  "",
);

// 创建专门用于奖励API的axios实例
const rewardAPI = axios.create({
  baseURL: REWARD_API_BASE,
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 接口类型定义
export interface CheckAndCreateRewardRequest {
  userAddress: string;
  signature: string;
  timestamp: string;
  nonce: number;
}

export interface CheckAndCreateRewardResponse {
  success: boolean;
  message: string;
  data: {
    createdBoxes: number;
    totalUnOpenedBoxes: number;
    rewardReasons: string[];
    existingBoxes: number;
    newBoxIds: string[];
  };
}

export interface MysteryBox {
  id: number;
  reward: number;
  timestamp_next_ready: string;
  timestamp_latest_opened?: string | null;
  remainingCooldown?: number;
}

export interface RewardStatsResponse {
  success: boolean;
  data: {
    user: {
      address: string;
      proxy_wallet: string;
      username: string;
      invitation_code: string;
      invited_by: string | null;
    };
    boxes: {
      total: number;
      opened: number;
      unopened: number;
      ready: number; // 可以开启的盲盒数量
      cooldown: number; // 冷却中的盲盒数量
    };
    invites: {
      sent_count: number;
      invited_users: Array<{
        username: string;
        proxy_wallet: string;
        invitation_code: string;
        invited_by: string;
      }>;
    };
    readyBoxes: MysteryBox[]; // 可以开启的盲盒列表
    cooldownBoxes: MysteryBox[]; // 冷却中的盲盒列表
  };
}

/**
 * 检查并创建奖励盲盒
 */
export const checkAndCreateReward = async (
  data: CheckAndCreateRewardRequest,
): Promise<CheckAndCreateRewardResponse> => {
  const response = await rewardAPI.post("/api/reward/check-and-create", data);
  return response.data;
};

/**
 * 获取用户奖励统计
 */
export const getRewardStats = async (userAddress: string): Promise<RewardStatsResponse> => {
  const response = await rewardAPI.get(`/api/reward/stats/${userAddress}`);
  return response.data;
};
