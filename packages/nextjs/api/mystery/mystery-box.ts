import { initRequest } from "../request";
import axios from "axios";

// API 基础 URL
const MYSTERY_BOX_API_BASE = process.env.NEXT_PUBLIC_MYSTERY_BOX_API || "http://localhost:5201/api/mystery-box";

// 接口类型定义
export interface SignatureConfig {
  domain: {
    name: string;
    version: string;
    chainId: number;
    verifyingContract: string;
  };
  types: {
    EIP712Domain: Array<{
      name: string;
      type: string;
    }>;
    MysteryBoxAuth: Array<{
      name: string;
      type: string;
    }>;
  };
  primaryType: string;
  timestamp: string;
  signatureValidityPeriod: number;
  instructions: {
    message: string;
    steps: string[];
  };
}

export interface CreateMysteryBoxRequest {
  userAddress: string;
  proxyWallet: string;
}

export interface OpenMysteryBoxRequest {
  userAddress: string;
  signature: string;
  timestamp: string;
  nonce: number;
  mysteryBoxId: number;
}

export interface CooldownStatus {
  canOpen: boolean;
  needsCreation: boolean;
  remainingTime: number;
  nextOpenTime: string | null;
  lastOpenTime: string | null;
  mysteryBoxId: number | null;
}

export interface OpenMysteryBoxResponse {
  reward: number;
  txHash: string;
  nextOpenTime: string;
}

export interface MysteryBoxHistory {
  id: number;
  owner: string;
  mystery_box_id: number;
  transaction_hash: string;
  amount: number;
  timestamp: string;
}

export interface MysteryBoxHistoryResponse {
  transactions: MysteryBoxHistory[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  summary: {
    totalRewards: number;
    totalTransactions: number;
  };
}

export interface MysteryBoxStats {
  totalBoxesOpened: number;
  totalBoxes: number;
  totalRewardsDistributed: number;
  averageReward: number;
  lastUpdated: string;
}

export interface MysteryBoxItem {
  id: number;
  owner: string;
  proxy_wallet: string;
  reward: number;
  timestamp_created: string;
  timestamp_latest_opened?: string;
  timestamp_next_ready?: string;
}

export interface UserBoxListResponse {
  boxes: MysteryBoxItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  summary: {
    totalBoxes: number;
    totalRewards: number;
    statusCounts: {
      ready: number;
      cooldown: number;
    };
  };
}

// API 函数实现

/**
 * 创建盲盒
 */
export const createMysteryBox = async (
  data: CreateMysteryBoxRequest,
): Promise<{ success: boolean; message: string }> => {
  return initRequest({
    url: `${MYSTERY_BOX_API_BASE}/create`,
    method: "post",
    data,
  });
};

/**
 * 开盒（需要签名验证）
 */
export const openMysteryBox = async (
  data: OpenMysteryBoxRequest,
): Promise<{ success: boolean; data: OpenMysteryBoxResponse }> => {
  try {
    const response = await axios.post(`${MYSTERY_BOX_API_BASE}/open`, data, {
      timeout: 30000, // 增加到30秒，因为涉及签名验证和可能的链上交易
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Mystery box open API error:", error);

    // 提供更详细的错误信息
    if (error.code === "ECONNABORTED") {
      throw new Error("开盒请求超时，请检查网络连接后重试");
    } else if (error.response?.status === 400) {
      throw new Error(error.response.data?.message || "请求参数错误");
    } else if (error.response?.status === 401) {
      throw new Error("签名验证失败，请重新连接钱包");
    } else if (error.response?.status >= 500) {
      throw new Error("服务器错误，请稍后重试");
    } else {
      throw new Error(error.message || "开盒失败，请重试");
    }
  }
};

/**
 * 获取用户历史
 */
export const getMysteryBoxHistory = async (
  address: string,
  page = 1,
  limit = 10,
): Promise<{ success: boolean; data: MysteryBoxHistoryResponse }> => {
  try {
    const response = await axios.get(`${MYSTERY_BOX_API_BASE}/history/${address}`, {
      params: { page, limit },
      timeout: 30000,
    });
    return response.data;
  } catch (error: any) {
    console.error("Mystery box history API error:", error);
    throw error;
  }
};

/**
 * 获取用户盲盒列表（包括所有状态的盒子）
 */
export const getUserBoxList = async (
  userAddress: string,
  page = 1,
  limit = 20,
): Promise<{ success: boolean; data: UserBoxListResponse }> => {
  try {
    const response = await axios.get(`${MYSTERY_BOX_API_BASE}/list/${userAddress}`, {
      params: { page, limit },
      timeout: 30000,
    });
    return response.data;
  } catch (error: any) {
    console.error("Mystery box API error:", error);
    throw error;
  }
};
