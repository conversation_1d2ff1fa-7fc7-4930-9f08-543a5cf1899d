import { initRequest } from "./request";

interface TagListParams {
  currentNavItem: any;
  status: string;
}

const getEventsListData = (params: any) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/events`,
    method: "get",
    params: {
      limit: "20",
      ...params,
    },
  });
};

const sortOptions = [
  { key: "trending", label: "Trending", order: "volume_24hr" },
  { key: "liquidity", label: "Liquidity", order: "liquidity" },
  { key: "volume", label: "Volume", order: "volume" },
  { key: "newest", label: "Newest", order: "start_date" },
  { key: "ending-soon", label: "EndingSoon", order: "end_date" },
  { key: "competitive", label: "Competitive", order: "competitive" },
];

const getInitEventsListData = (params: any) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { order, tag_id, limit, ...restParams } = params;
  const apiPoint = sortOptions.find(option => option.order === order) || sortOptions[0];
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getInitEventsBy${apiPoint.label}`,
    method: "get",
    params: {
      limit: limit || "20",
      ...restParams,
    },
  });
};

const sortEventsListData = (params: any) => {
  const { order, ...restParams } = params;
  const apiPoint = sortOptions.find(option => option.order === order) || sortOptions[0];

  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/sortEventsBy${apiPoint.label}`,
    method: "get",
    params: {
      limit: "20",
      ...restParams,
    },
  });
};

const getTagBySlugListData = (params: TagListParams) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getTagBySlug`,
    method: "get",
    params: {
      limit: 20,
      offset: 0,
      slug: params.currentNavItem.slug,
    },
  });
};

const getTagsByRegion = (regions: string) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getTagsByRegtion`,
    method: "get",
    params: {
      region: regions,
    },
  });
};

const getBannerByRegion = (regions: string) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getBannerByRegion`,
    method: "get",
    params: {
      region: regions,
    },
  });
};

export {
  getEventsListData,
  getTagBySlugListData,
  sortEventsListData,
  getTagsByRegion,
  getBannerByRegion,
  getInitEventsListData,
};
