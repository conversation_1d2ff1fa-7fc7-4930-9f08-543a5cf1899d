import { initRequest } from "../request";
import { ActivityListDataParams, PositionsListParams } from "@/components/portfolio/types";
import { WalletType } from "@/services/store/store";
import { getProviderAndSigner, getProviderAndSignerWithRetry } from "@/utils";
import { createL2Headers } from "pedone-clob-client";

// 公共接口配置
interface ApiEndpointConfig {
  path: string;
  requestPath: string;
}
export interface MarketQuoteResponse {
  actualCost: number;
  amount: number;
}

// API 端点配置
const API_ENDPOINTS: Record<string, ApiEndpointConfig> = {
  position: {
    path: "/data/position",
    requestPath: "/data/position",
  },
  filledOrders: {
    path: "/data/filled-orders",
    requestPath: "/data/filled-orders",
  },
  orders: {
    path: "/data/orders",
    requestPath: "/data/orders",
  },
};

const getInformationListData = (market: string[]) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getQuestionMarketAndEventInfomation`,
    method: "post",
    data: {
      market: market,
    },
  });
};

const getInformationListDataByQuestionId = (market: number[]) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getQuestionMarketInfomationByQuestionId`,
    method: "post",
    data: {
      market: market,
    },
  });
};

// 通用的带认证的数据获取函数
const fetchAuthenticatedData = async (
  creds: any,
  walletType: WalletType,
  params: { offset?: number; limit?: number; [key: string]: any },
  endpoint: keyof typeof API_ENDPOINTS,
) => {
  const signer = await getProviderAndSignerWithRetry(walletType);
  if (!signer) {
    throw new Error("Signer is undefined in fetchAuthenticatedData");
  }

  const address = await signer.getAddress();

  const config = API_ENDPOINTS[endpoint];
  const headerArgs = {
    method: "GET",
    requestPath: config.requestPath,
  };

  const l2Headers = await createL2Headers(signer, creds[address], headerArgs);

  const requestParams = {
    offset: params.offset ?? 0,
    limit: params.limit ?? 10,
  };

  const response = await initRequest({
    url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}${config.path}`,
    method: "get",
    headers: {
      ...l2Headers,
    },
    params: requestParams,
  });

  if (response.data.data && Array.isArray(response.data.data)) {
    const parsedData = response.data.data
      .map((jsonString: string) => {
        try {
          return JSON.parse(jsonString);
        } catch (error) {
          console.error(`解析 ${endpoint} JSON 字符串失败:`, error, jsonString);
          return null;
        }
      })
      .filter((item: any) => item !== null);

    return {
      ...response,
      data: { data: parsedData },
    };
  }

  return response;
};

const getPositionsListDataInPofolio = async (creds: any, walletType: WalletType, params: PositionsListParams) => {
  return fetchAuthenticatedData(creds, walletType, params, "position");
};

const getHistoryListData = async (creds: any, walletType: WalletType, params: ActivityListDataParams) => {
  return fetchAuthenticatedData(creds, walletType, params, "filledOrders");
};

const getOpenOrderData = async (creds: any, walletType: WalletType, params: { offset: number; limit: number }) => {
  return fetchAuthenticatedData(creds, walletType, params, "orders");
};

const cancelOrders = async (creds: any, orderID: string, setShowNotification: any, walletType: WalletType) => {
  const signer = await getProviderAndSigner(walletType);
  if (!signer) {
    throw new Error("Signer is undefined");
  }
  const address = await signer.getAddress();

  const headerArgs = {
    method: "DELETE",
    requestPath: "/order",
    body: JSON.stringify({ orderID: orderID }),
  };
  // @ts-ignore
  const l2Headers = await createL2Headers(signer, creds[address], headerArgs);
  try {
    const response = await initRequest({
      url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/order`,
      method: "delete",
      headers: {
        ...l2Headers,
      },
      data: {
        orderID: orderID,
      },
    });

    if (response.status === 200) {
      setShowNotification(true);
    }

    return response;
  } catch (error) {
    console.error("Error cancelling order:", error);
    throw error;
  }
};

const getMarketOrderQuote = async (tokenId: string, side: number, value: number): Promise<MarketQuoteResponse> => {
  try {
    const response = await initRequest({
      url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/data/market-order`,
      method: "get",
      params: {
        token_id: tokenId,
        side: side,
        value: value,
      },
    });

    return {
      actualCost: parseInt(response.data.actual_cost),
      amount: parseInt(response.data.amount),
    };
  } catch (error) {
    throw error;
  }
};

export {
  getOpenOrderData,
  getHistoryListData,
  cancelOrders,
  getMarketOrderQuote,
  getInformationListData,
  getPositionsListDataInPofolio,
  getInformationListDataByQuestionId,
};
