import { OrderType, orderToJson } from "@/utils";
import { JsonRpcSigner } from "@ethersproject/providers";
import { SignedOrder } from "@polymarket/order-utils";
import axios from "axios";
import { createL2Headers } from "pedone-clob-client";

interface ApiKeyCreds {
  key: string;
  passphrase: string;
  secret: string;
}

const createOrder = async ({
  value,
  clobApis,
  timestamp,
  signer,
  type = OrderType.GTC,
}: {
  value: SignedOrder;
  clobApis: ApiKeyCreds;
  timestamp: number;
  signer: JsonRpcSigner;
  type?: OrderType;
}) => {
  try {
    const orderPayload = orderToJson(value, clobApis.key, type);

    const l2HeaderArgs = {
      method: "POST",
      requestPath: "/order",
      body: JSON.stringify(orderPayload),
    };

    const headers = await createL2Headers(signer, clobApis, l2HeaderArgs, timestamp);

    const response = await axios({
      url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/order`,
      method: "post",
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
      data: {
        ...orderPayload,
      },
    });

    return response.data;
  } catch (error) {
    console.error("Error in createClobAuth:", error);
    throw error;
  }
};

export default createOrder;
