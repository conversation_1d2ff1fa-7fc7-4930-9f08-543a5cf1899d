import axios from "axios";

const createProxyWallet = async ({
  from,
  proxyWallet,
  signature,
  cookie,
  inviteCode,
  socialMedia,
}: {
  from: string;
  proxyWallet: string;
  signature: string;
  cookie: string;
  inviteCode: string | undefined;
  socialMedia?: {
    social_media_id: string;
    email: string;
    social_media: string;
    social_media_user_name: string;
  };
}) => {
  try {
    const requestData: any = {
      from: from,
      proxyWallet: proxyWallet,
      signature: signature,
      cookie: cookie,
      inviteCode: inviteCode,
    };

    if (socialMedia) {
      requestData.socialMedia = {
        social_media_id: socialMedia.social_media_id,
        email: socialMedia.email,
        social_media: socialMedia.social_media,
        social_media_user_name: socialMedia.social_media_user_name,
      };
    }

    const response = await axios({
      url: "/api/proxyWallet",
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      data: requestData,
    });

    return response.data;
  } catch (error) {
    console.error("Error in createProxyWallet:", error);
    throw error;
  }
};

export default createProxyWallet;
