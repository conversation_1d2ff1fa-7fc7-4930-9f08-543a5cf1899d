import axios from "axios";

const createRedPocket = async ({
  address,
  signature,
  timestamp,
  password,
}: {
  address: string;
  signature: string;
  timestamp: string;
  password: string;
}) => {
  try {
    // 使用Next.js API路由作为代理，避免CORS问题
    const response = await axios({
      url: "/api/redpocket/open",
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        address,
        signature,
        timestamp,
        password,
      },
    });

    return response.data;
  } catch (error) {
    console.error("Error in createRedPocket:", error);
    throw error;
  }
};

export default createRedPocket;
