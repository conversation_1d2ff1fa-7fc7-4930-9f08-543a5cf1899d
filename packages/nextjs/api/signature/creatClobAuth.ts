import { getItem, setItem } from "@/utils";
import axios from "axios";

// 存储 CLOB API 密钥到 localStorage
const storeClobApiKey = (
  address: string,
  apiKeyData: { apiKey: string; secret: string; passphrase: string; baseAddress: string },
) => {
  const existingData = getItem("poly_clob_api_key_map") || {};
  existingData[address] = {
    key: apiKeyData.apiKey,
    secret: apiKeyData.secret,
    passphrase: apiKeyData.passphrase,
    baseAddress: address,
  };
  setItem("poly_clob_api_key_map", existingData);
};

const createClobAuth = async ({
  address,
  signature,
  timestamp,
}: {
  address: string;
  signature: string;
  timestamp: string;
}) => {
  try {
    const response = await axios({
      url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/auth/derive-api-key`,
      method: "get",
      headers: {
        "Content-Type": "application/json",
        "Pedone-address": address,
        "Pedone-signature": signature,
        "Pedone-timestamp": timestamp,
        "Pedone-nonce": "0",
      },
    });

    storeClobApiKey(address, response.data);
    return response.data;
  } catch (error) {
    console.error("Error in createClobAuth:", error);
    throw error;
  }
};

export default createClobAuth;
