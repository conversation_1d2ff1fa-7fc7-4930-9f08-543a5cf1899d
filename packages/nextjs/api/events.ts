import { initRequest } from "./request";

const getAllTagsListData = () => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getAllTags`,
    method: "get",
  });
};

const getAllTagsWithEventCount = (params?: { active?: boolean; closed?: boolean }) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getAllTagsWithEventCount`,
    method: "get",
    params: {
      active: params?.active ?? true,
      closed: params?.closed ?? false,
    },
  });
};

const getAllTagsWithCreatorEventCount = (params: { creatorTagId: number; active?: boolean; closed?: boolean }) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getAllTagsWithCreatorEventCount`,
    method: "get",
    params: {
      creatorTagId: params.creatorTagId,
      active: params.active ?? true,
      closed: params.closed ?? false,
    },
  });
};

const getSubHeaderListData = () => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getSubHeaderList`,
    method: "get",
  });
};

const getTopNewsListData = async () => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getAllNews`,
    method: "get",
  });
};

const getEventByKeyword = (keyword: string) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getEventByKeyword`,
    method: "get",
    params: {
      keyword: `%${keyword}%`,
    },
  });
};
const getEventListByIds = (ids: string[]) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getEventListByIds`,
    method: "post",
    data: {
      ids,
    },
  });
};

const getActivityBannerList = (limit = 20) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/last-trades?limit=${limit}`,
    method: "get",
  });
};
const getEventsByTagId = (params: { limit?: number; offset?: number; closed?: boolean; tag_id: number }) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getEventsByTagId`,
    method: "get",
    params: {
      limit: params.limit || 20,
      offset: params.offset || 0,
      closed: params.closed,
      tag_id: params.tag_id,
    },
  });
};

const getCreatorEvents = (params: { limit?: number; offset?: number; active?: boolean; closed?: boolean }) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getCreatorEvents`,
    method: "get",
    params: {
      limit: params.limit || 20,
      offset: params.offset || 0,
      active: params.active ?? true,
      closed: params.closed ?? false,
    },
  });
};

const getAllHomeEvents = (params: { limit?: number; offset?: number; active?: boolean; closed?: boolean }) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getAllHomeEvents`,
    method: "get",
    params: {
      limit: params.limit || 20,
      offset: params.offset || 0,
      active: params.active ?? true,
      closed: params.closed ?? false,
    },
  });
};

const getHomeEventsByTag = (params: {
  limit?: number;
  offset?: number;
  active?: boolean;
  closed?: boolean;
  tag_id: number;
}) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getHomeEventsByTag`,
    method: "get",
    params: {
      limit: params.limit || 20,
      offset: params.offset || 0,
      active: params.active ?? true,
      closed: params.closed ?? false,
      tag_id: params.tag_id,
    },
  });
};

export {
  getAllTagsListData,
  getAllTagsWithEventCount,
  getAllTagsWithCreatorEventCount,
  getSubHeaderListData,
  getTopNewsListData,
  getEventByKeyword,
  getEventListByIds,
  getActivityBannerList,
  getEventsByTagId,
  getCreatorEvents,
  getAllHomeEvents,
  getHomeEventsByTag,
};
