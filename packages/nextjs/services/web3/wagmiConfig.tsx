import { wagmiConnectors } from "./wagmiConnectors";
import { getItem } from "@/utils";
import { Chain, createClient, fallback, http } from "viem";
import { hardhat, mainnet } from "viem/chains";
import { createConfig } from "wagmi";
import scaffoldConfig from "~~/scaffold.config";

const LAST_WORKING_RPC_KEY = "lastWorkingRpc"; // Key for caching the last working RPC
const { targetNetworks } = scaffoldConfig;

// We always want to have mainnet enabled (ENS resolution, ETH price, etc). But only once.
export const enabledChains = targetNetworks.find((network: Chain) => network.id === 1)
  ? targetNetworks
  : ([...targetNetworks, mainnet] as const);

const cachedRpc = getItem(LAST_WORKING_RPC_KEY);
const providerUrls = JSON.parse(process.env.NEXT_PUBLIC_PROVIDER_URLS || "[]");

const fallbackRpc = fallback([http(cachedRpc || ""), ...providerUrls.map((url: string) => http(url))], {
  retryCount: 3,
  retryDelay: 1000,
});

export const wagmiConfig = createConfig({
  chains: enabledChains,
  connectors: wagmiConnectors,
  ssr: true,
  client({ chain }) {
    return createClient({
      chain,
      transport: fallbackRpc,
      ...(chain.id !== (hardhat as Chain).id
        ? {
            pollingInterval: scaffoldConfig.pollingInterval,
          }
        : {}),
    });
  },
});
