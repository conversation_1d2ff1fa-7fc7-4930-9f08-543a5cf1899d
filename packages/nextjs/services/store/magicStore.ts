import { OAuthExtension } from "@magic-ext/oauth";
import { Magic } from "magic-sdk";
import create from "zustand";

interface MagicUser {
  publicAddress: string;
  email?: string;
  issuer?: string;
}

interface MagicStore {
  magic: Magic<OAuthExtension[]> | null;
  magicUser: MagicUser | null;
  isLoggedIn: boolean;
  isInitialized: boolean;
  isLoading: boolean;

  initializeMagic: () => Promise<void>;
  login: (user: MagicUser) => void;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  setUser: (user: MagicUser | null) => void;
  setLoading: (loading: boolean) => void;
}

export const useMagicStore = create<MagicStore>((set, get) => ({
  magic: null,
  magicUser: null,
  isLoggedIn: false,
  isInitialized: false,
  isLoading: false,

  initializeMagic: async () => {
    if (typeof window === "undefined") return;

    const state = get();
    if (state.isInitialized || state.magic) return;

    set(() => ({ isLoading: true }));

    try {
      const apiKey = process.env.NEXT_PUBLIC_MAGIC_API_KEY;
      if (!apiKey) {
        throw new Error("NEXT_PUBLIC_MAGIC_API_KEY is not defined");
      }

      const magicInstance = new Magic(apiKey, {
        extensions: [new OAuthExtension()],
      });

      set(() => ({
        magic: magicInstance,
        isInitialized: true,
      }));

      // 检查现有登录状态
      await get().refreshAuth();
    } catch (error) {
      console.error("Magic initialization failed:", error);
      set(() => ({ isInitialized: false }));
    } finally {
      set(() => ({ isLoading: false }));
    }
  },

  login: (user: MagicUser) => {
    set(() => ({
      magicUser: user,
      isLoggedIn: true,
    }));
  },

  logout: async () => {
    const state = get();
    if (!state.magic) return;

    set(() => ({ isLoading: true }));

    try {
      await state.magic.user.logout();
    } catch (error) {
      console.error("Magic logout failed:", error);
    } finally {
      set(() => ({
        magicUser: null,
        isLoggedIn: false,
        isLoading: false,
      }));
    }
  },

  refreshAuth: async () => {
    const state = get();
    if (!state.magic) return;

    try {
      const isLoggedIn = await state.magic.user.isLoggedIn();
      if (isLoggedIn) {
        const metadata = await state.magic.user.getInfo();
        if (metadata) {
          set(() => ({
            magicUser: {
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              publicAddress: metadata.publicAddress!,
              email: metadata.email || undefined,
              issuer: metadata.issuer || undefined,
            },
            isLoggedIn: true,
          }));
        }
      } else {
        set(() => ({
          magicUser: null,
          isLoggedIn: false,
        }));
      }
    } catch (error) {
      console.error("Refresh magic auth failed:", error);
      set(() => ({
        magicUser: null,
        isLoggedIn: false,
      }));
    }
  },

  setUser: (user: MagicUser | null) => {
    set(() => ({
      magicUser: user,
      isLoggedIn: Boolean(user),
    }));
  },

  setLoading: (loading: boolean) => {
    set(() => ({ isLoading: loading }));
  },
}));
