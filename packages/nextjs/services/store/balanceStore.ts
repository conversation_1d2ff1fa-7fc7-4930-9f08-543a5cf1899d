import { getERC20Balance } from "@/contracts/getERC20Balance";
import backoff from "backoff";
import { isAddress } from "ethers";
import create from "zustand";

// 余额状态接口
interface BalanceState {
  balance: string | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number;
}

// 余额存储接口
interface BalanceStore {
  balances: Record<string, BalanceState>;

  setBalance: (address: string, balance: string) => void;
  setBalanceLoading: (address: string, isLoading: boolean) => void;
  setBalanceError: (address: string, error: string | null) => void;
  clearBalance: (address: string) => void;

  refreshBalance: (address: string) => Promise<void>;
  forceRefreshBalance: (address: string) => Promise<void>; // 强制刷新，忽略缓存
  getBalanceState: (address: string) => BalanceState | null;

  // 批量操作
  refreshMultipleBalances: (addresses: string[]) => Promise<void>;
  clearAllBalances: () => void;
}

const fetchBalanceWithBackoff = async (address: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const call = backoff.call(
      async (cb: (err: Error | null, result?: string) => void) => {
        try {
          if (!isAddress(address)) {
            throw new Error("Invalid address format");
          }
          const balance = await getERC20Balance(address);
          cb(null, balance || "0.00");
        } catch (error: any) {
          cb(error);
        }
      },
      (err: Error | null, result?: string) => {
        if (err) {
          reject(err);
        } else {
          resolve(result || "0.00");
        }
      },
    );

    call.setStrategy(
      new backoff.ExponentialStrategy({
        initialDelay: 1000,
        maxDelay: 4000,
      }),
    );
    call.failAfter(3);
    call.start();
  });
};

export const useBalanceStore = create<BalanceStore>((set, get) => ({
  balances: {},

  // 设置余额
  setBalance: (address: string, balance: string) => {
    if (!address || !isAddress(address)) return;

    set(state => ({
      balances: {
        ...state.balances,
        [address]: {
          ...state.balances[address],
          balance,
          isLoading: false,
          error: null,
          lastUpdated: Date.now(),
        },
      },
    }));
  },

  // 设置加载状态
  setBalanceLoading: (address: string, isLoading: boolean) => {
    if (!address || !isAddress(address)) return;

    set(state => ({
      balances: {
        ...state.balances,
        [address]: {
          balance: state.balances[address]?.balance || null,
          error: state.balances[address]?.error || null,
          lastUpdated: state.balances[address]?.lastUpdated || 0,
          isLoading,
        },
      },
    }));
  },

  // 设置错误状态
  setBalanceError: (address: string, error: string | null) => {
    if (!address || !isAddress(address)) return;

    set(state => ({
      balances: {
        ...state.balances,
        [address]: {
          balance: state.balances[address]?.balance || null,
          lastUpdated: state.balances[address]?.lastUpdated || 0,
          error,
          isLoading: false,
        },
      },
    }));
  },

  // 清除单个地址的余额
  clearBalance: (address: string) => {
    if (!address) return;

    set(state => {
      const newBalances = { ...state.balances };
      delete newBalances[address];
      return { balances: newBalances };
    });
  },

  // 刷新余额（带 backoff 重试）
  refreshBalance: async (address: string) => {
    if (!address || !isAddress(address)) {
      console.warn("⚠️ Invalid address for balance refresh:", address);
      return;
    }

    const { setBalanceLoading, setBalance, setBalanceError } = get();

    setBalanceLoading(address, true);

    try {
      const balance = await fetchBalanceWithBackoff(address);
      setBalance(address, balance);
    } catch (error: any) {
      setBalanceError(address, error.message);
    }
  },

  // 强制刷新余额（忽略缓存，立即刷新）
  forceRefreshBalance: async (address: string) => {
    if (!address || !isAddress(address)) {
      console.warn("⚠️ Invalid address for force refresh:", address);
      return;
    }

    const { setBalanceLoading, setBalance, setBalanceError } = get();

    console.log(`🔄 Force refreshing balance for ${address} (ignoring cache)`);
    setBalanceLoading(address, true);

    try {
      const balance = await fetchBalanceWithBackoff(address);
      setBalance(address, balance);
      console.log(`✅ Balance force refreshed for ${address}: ${balance}`);
    } catch (error: any) {
      console.error(`❌ Failed to force refresh balance for ${address}:`, error);
      setBalanceError(address, error.message);
    }
  },

  // 获取余额状态
  getBalanceState: (address: string) => {
    if (!address || !isAddress(address)) return null;
    return get().balances[address] || null;
  },

  // 批量刷新余额
  refreshMultipleBalances: async (addresses: string[]) => {
    const validAddresses = addresses.filter(addr => addr && isAddress(addr));

    const promises = validAddresses.map(address => get().refreshBalance(address));
    await Promise.allSettled(promises);
  },

  // 清除所有余额
  clearAllBalances: () => {
    set({ balances: {} });
  },
}));

// 导出类型
export type { BalanceState, BalanceStore };

// 导出全局强制刷新函数，供组件直接调用
export const forceRefreshGlobalBalance = (address: string) => {
  if (!address || !isAddress(address)) {
    return Promise.resolve();
  }

  return useBalanceStore.getState().forceRefreshBalance(address);
};
