import { getItem } from "@/utils";
import create from "zustand";
import scaffoldConfig from "~~/scaffold.config";
import { ChainWithAttributes } from "~~/utils/scaffold-eth";

export { useBalanceStore } from "./balanceStore";
export type { BalanceState, BalanceStore } from "./balanceStore";

// 定义钱包类型枚举
export enum WalletType {
  METAMASK = "metamask",
  MAGIC = "magic",
  NONE = "none",
}

type GlobalState = {
  nativeCurrency: {
    price: number;
    isFetching: boolean;
    current_language: string;
  };
  targetNetwork: ChainWithAttributes;
  hasRpcError: boolean; // Tracks the RPC error state
  frozenValue: number;
  frozenValueLastUpdated: number; // 添加frozen值最后更新时间
  walletType: WalletType;
  account: string | null; // 添加账户地址状态

  setFrozenValue: (value: number) => void;
  setTargetNetwork: (newTargetNetwork: ChainWithAttributes) => void;
  setCurrentLanguage: (newCurrentLanguage: string) => void;
  setRpcError: (hasError: boolean) => void; // Updates the RPC error state
  setWalletType: (type: WalletType) => void; // 设置钱包类型
  setAccount: (address: string | null) => void; // 添加设置账户地址的方法
};

export const useGlobalState = create<GlobalState>(set => ({
  nativeCurrency: {
    price: 0,
    isFetching: true,
    current_language: getItem("current_language") || "en", // Default language is "en"
  },
  targetNetwork: scaffoldConfig.targetNetworks[0], // Default target network
  hasRpcError: false, // RPC error state initialized to false
  frozenValue: 0,
  frozenValueLastUpdated: 0, // 初始化frozen值更新时间
  walletType: WalletType.NONE,
  account: null, // 初始化账户为 null

  setFrozenValue: (value: number) =>
    set(() => ({
      frozenValue: value,
      frozenValueLastUpdated: Date.now(),
    })),
  setTargetNetwork: (newTargetNetwork: ChainWithAttributes) => set(() => ({ targetNetwork: newTargetNetwork })), // Updates the target network
  setCurrentLanguage: (newCurrentLanguage: string) =>
    set(state => ({ nativeCurrency: { ...state.nativeCurrency, current_language: newCurrentLanguage } })), // Updates the current language
  setRpcError: (hasError: boolean) => set(() => ({ hasRpcError: hasError })), // Updates the RPC error state
  setWalletType: (type: WalletType) => set(() => ({ walletType: type })), // 设置钱包类型
  setAccount: (address: string | null) => set(() => ({ account: address })), // 设置账户地址
}));
