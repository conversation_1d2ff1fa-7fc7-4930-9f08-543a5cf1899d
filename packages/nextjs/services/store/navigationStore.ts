import create from "zustand";

interface NavItem {
  id: string;
  label: string;
  slug: string;
  region: string;
  forceShow: boolean;
  forceHide?: boolean;
}

interface NavigationState {
  currentNavItem: NavItem | null;
  setCurrentNavItem: (item: NavItem | null) => void;
}

const useNavigationStore = create<NavigationState>(set => ({
  currentNavItem: null,
  setCurrentNavItem: item => set({ currentNavItem: item }),
}));

export default useNavigationStore;
