import { metaMaskSignature } from "@/utils";
import { fetchWithFailover } from "@/utils/signature";
import axios from "axios";
import backoff from "backoff";
import {
  Contract,
  Interface,
  JsonRpcProvider,
  MaxUint256,
  concat,
  ethers,
  getBytes,
  parseUnits,
  toBeHex,
  zeroPadBytes,
  zeroPadValue,
} from "ethers";

type SpenderObj = {
  ERC1155: string;
  CTFExchange: string;
  PolymarketNegRiskCTFExchange: string;
  NegRiskAdapter: string;
};

type OperatorObj = {
  CTFExchange: string;
  PolymarketNegRiskCTFExchange: string;
  NegRiskAdapter: string;
};

// ABI
const erc20ApproveAbi = ["function approve(address spender, uint256 amount) public returns (bool)"];
const multiSendAbi = ["function multiSend(bytes transactions) public"];
const setApprovalForAllAbi = ["function setApprovalForAll(address operator, bool state) public returns (bool)"];
const getTransactionHashAbi = [
  "function getTransactionHash(address to, uint256 value, bytes data, uint8 operation, uint256 safeTxGas, uint256 baseGas, uint256 gasPrice, address gasToken, address refundReceiver, uint256 _nonce) view returns (bytes32)",
];
const nonceAbi = ["function nonce() view returns (uint256)"];
const erc20AllowanceAbi = ["function allowance(address owner, address spender) view returns (uint256)"];
const setGuardianAbi = ["function setGuard(address guardian) public"];

// Interfaces
const erc20Interface = new Interface(erc20ApproveAbi);
const multiSendInterface = new Interface(multiSendAbi);
const setApprovalInterface = new Interface(setApprovalForAllAbi);
const abiSetGuardianAbi = new Interface(setGuardianAbi);

// Contracts
const ConditionTokenContracts = process.env.NEXT_PUBLIC_CONDITION_TOKEN_CONTRACTS || "";
const feemoduleContracts = process.env.NEXT_PUBLIC_FEEMODULE_CONTRACTS || "";
const WrapUSDCContracts = process.env.NEXT_PUBLIC_WRAP_USDC_CONTRACTS || "";
const CTFExchangeContracts = process.env.NEXT_PUBLIC_CTF_EXCHANGE_CONTRACTS || "";
const pdoneNegRiskCTFExchange = process.env.NEXT_PUBLIC_PDONE_NEG_RISK_CTF_EXCHANGE || "";
const NegRiskAdapter = process.env.NEXT_PUBLIC_NEG_RISK_ADAPTER || "";
const GnosismultisendContract = process.env.NEXT_PUBLIC_GNOSISMULTISEND_CONTRACT || "";
const GuardContractAddr = process.env.NEXT_PUBLIC_GUARD_CONTRACT_ADDR || "";
// Addresses
const spenderAddressObj = {
  ERC1155: ConditionTokenContracts,
  CTFExchange: CTFExchangeContracts,
  PolymarketNegRiskCTFExchange: pdoneNegRiskCTFExchange,
  NegRiskAdapter: NegRiskAdapter,
  Feemodule: feemoduleContracts,
};

const operatorAddressObj = {
  CTFExchange: CTFExchangeContracts,
  PolymarketNegRiskCTFExchange: pdoneNegRiskCTFExchange,
  NegRiskAdapter: NegRiskAdapter,
};

const signatureParams = {
  gasPrice: "0",
  operation: "1",
  safeTxnGas: "0",
  baseGas: "0",
  gasToken: "******************************************",
  refundReceiver: "******************************************",
};

function createApproveTransaction(spenderAddress: string, amount: any): string {
  return erc20Interface.encodeFunctionData("approve", [spenderAddress, amount]);
}

function createSetApprovalTransaction(operator: string, approved: boolean): string {
  return setApprovalInterface.encodeFunctionData("setApprovalForAll", [operator, approved]);
}

async function getNonce(proxyWallet: string) {
  const providerUrl = await fetchWithFailover();
  const provider = new JsonRpcProvider(providerUrl);
  const contract = new Contract(proxyWallet, nonceAbi, provider);
  try {
    const nonce = await contract.nonce();
    return nonce;
  } catch (error) {
    console.error("Error fetching nonce:", error);
    throw error;
  }
}

async function createGetTransactionHash(proxyWallet: string, nonce: number) {
  const providerUrl = await fetchWithFailover();
  const provider = new JsonRpcProvider(providerUrl);
  const contract = new Contract(proxyWallet, getTransactionHashAbi, provider);

  const payload = {
    to: GnosismultisendContract, // Safe: Multi Send 1.3.0
    value: parseUnits("0", "ether"),
    data: getMultiSendResult(proxyWallet),
    operation: 1, // 0 表示 CALL，1 表示 DELEGATECALL
    safeTxGas: 0,
    baseGas: 0,
    gasPrice: 0,
    gasToken: "******************************************", // 使用 ETH 作为 gas token
    refundReceiver: "******************************************",
  };

  try {
    const transactionHash = await contract.getTransactionHash(
      payload.to,
      payload.value,
      payload.data,
      payload.operation,
      payload.safeTxGas,
      payload.baseGas,
      payload.gasPrice,
      payload.gasToken,
      payload.refundReceiver,
      nonce,
    );

    return transactionHash;
  } catch (error) {
    console.error("Error fetching nonce:", error);
    throw error;
  }
}

async function approveSignature(
  address: string,
  proxyWallet: string,
  transactionHash: string,
  nonce: any,
  cookie: any,
  setApproveState: any,
) {
  setApproveState("loading");
  const signature = await metaMaskSignature(transactionHash);
  // 创建一个新的 Uint8Array，长度与 newSignature 相同
  const modifiedSignature = new Uint8Array(getBytes(signature));
  // 将最后一位加上 4
  modifiedSignature[modifiedSignature.length - 1] += 4;
  const hexSignature = "0x" + Buffer.from(modifiedSignature).toString("hex");

  const payload = {
    to: GnosismultisendContract, // Safe: Multi Send 1.3.0
    value: parseUnits("0", "ether"),
    data: getMultiSendResult(proxyWallet),
    operation: 1, // 0 表示 CALL，1 表示 DELEGATECALL
    safeTxGas: 0,
    baseGas: 0,
    gasPrice: 0,
    gasToken: "******************************************", // 使用 ETH 作为 gas token
    refundReceiver: "******************************************",
  };

  try {
    const response = await axios({
      url: "/api/submitApprove",
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        data: payload.data,
        from: address,
        nonce: String(nonce),
        proxyWallet: proxyWallet,
        signature: hexSignature,
        signatureParams: signatureParams,
        to: payload.to,
        type: "SAFE",
        cookie: cookie,
      },
    });

    if (!response || response.data.error) {
      setApproveState("none");
    } else {
      setApproveState("success");
    }
  } catch (error) {
    const errorMessage = (error as Error).message;
    console.error("Error:", errorMessage);
    setApproveState("none");
  }
}
async function approveSignatureWithMagic(
  address: string,
  proxyWallet: string,
  transactionHash: string,
  nonce: any,
  cookie: any,
  magicInstance: any,
) {
  try {
    // 使用 Magic 进行签名
    const provider = new ethers.BrowserProvider(magicInstance.rpcProvider);
    const signer = await provider.getSigner();
    const signature = await signer.signMessage(ethers.getBytes(transactionHash));
    // 创建一个新的 Uint8Array，长度与 newSignature 相同
    const modifiedSignature = new Uint8Array(getBytes(signature));
    // 将最后一位加上 4
    modifiedSignature[modifiedSignature.length - 1] += 4;
    const hexSignature = "0x" + Buffer.from(modifiedSignature).toString("hex");

    const payload = {
      to: GnosismultisendContract, // Safe: Multi Send 1.3.0
      value: parseUnits("0", "ether"),
      data: getMultiSendResult(proxyWallet),
      operation: 1, // 0 表示 CALL，1 表示 DELEGATECALL
      safeTxGas: 0,
      baseGas: 0,
      gasPrice: 0,
      gasToken: "******************************************", // 使用 ETH 作为 gas token
      refundReceiver: "******************************************",
    };

    await axios({
      url: "/api/submitApprove",
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        data: payload.data,
        from: address,
        nonce: String(nonce),
        proxyWallet: proxyWallet,
        signature: hexSignature,
        signatureParams: signatureParams,
        to: payload.to,
        type: "SAFE",
        cookie: cookie,
      },
    });
  } catch (error) {
    const errorMessage = (error as Error).message;
    console.error("Magic signature error:", errorMessage);
  }
}

const onClickExecTransaction = async (
  address: string | undefined,
  proxyWallet: string | undefined,
  session: any,
  setApproveState: any,
  setShowTooltip: any,
) => {
  if (address && proxyWallet) {
    try {
      if (!session || !session.cookie) {
        setShowTooltip(true);
        setTimeout(() => setShowTooltip(false), 3000);
        return;
      }

      const nonce = await getNonce(proxyWallet);
      const transactionHash = await createGetTransactionHash(proxyWallet, nonce);
      await approveSignature(address, proxyWallet, transactionHash, nonce, session.cookie, setApproveState);
    } catch (error) {
      console.error("Error:", error);
      setApproveState("none");
    }
  }
};

function encodeMultiSendTransaction(
  target: string, // Ethereum address as a string
  value: bigint, // BigNumber instance from ethers.js
  data: Uint8Array, // Data as a Uint8Array
): string {
  const operation = new Uint8Array([0x00]);
  const valuePadded = zeroPadValue(toBeHex(value), 32);
  const addressPadded = zeroPadBytes(getBytes(target), 20);
  const dataLength = BigInt(data.length);
  const dataLengthPadded = zeroPadValue(toBeHex(dataLength), 32);

  // Concatenate all parts
  const concatenated = concat([operation, addressPadded, valuePadded, dataLengthPadded, data]);

  return concatenated;
}

function getApprovesTransaction() {
  const approvesTransactionList = [];

  for (const key in spenderAddressObj) {
    if (spenderAddressObj.hasOwnProperty(key)) {
      const spenderAddress = spenderAddressObj[key as keyof SpenderObj];
      const approveData = createApproveTransaction(spenderAddress, MaxUint256);
      const approveTransaction = encodeMultiSendTransaction(WrapUSDCContracts, BigInt(0), getBytes(approveData));
      approvesTransactionList.push(approveTransaction);
    }
  }

  return approvesTransactionList;
}

function setGuard(proxyWalletAddr: string) {
  const setApprovalsTransactionList = [];
  const data = abiSetGuardianAbi.encodeFunctionData("setGuard", [GuardContractAddr]);
  const approveTransaction = encodeMultiSendTransaction(proxyWalletAddr, BigInt(0), getBytes(data));
  setApprovalsTransactionList.push(approveTransaction);
  return setApprovalsTransactionList;
}

function getSetApprovalsTransaction() {
  const setApprovalsTransactionList = [];
  for (const key in operatorAddressObj) {
    if (operatorAddressObj.hasOwnProperty(key)) {
      const operatorAddress = operatorAddressObj[key as keyof OperatorObj];
      const setApprovalsData = createSetApprovalTransaction(operatorAddress, true);
      const approveTransaction = encodeMultiSendTransaction(
        ConditionTokenContracts,
        BigInt(0),
        getBytes(setApprovalsData),
      );
      setApprovalsTransactionList.push(approveTransaction);
    }
  }

  return setApprovalsTransactionList;
}

function getMultiSendResult(proxyWalletAddr: string) {
  const approvesTransactionList = getApprovesTransaction();
  const setApprovalsTransactionList = getSetApprovalsTransaction();

  const multiSendEncodedList = approvesTransactionList.concat(setApprovalsTransactionList);

  setGuard(proxyWalletAddr);

  // const finalList = multiSendEncodedList;
  const finalList = multiSendEncodedList.concat(setGuard(proxyWalletAddr));

  let combinedString = finalList[0];
  for (let i = 1; i <= 8; i++) {
    combinedString += finalList[i].slice(2);
  }

  const multiSendEncodedData = multiSendInterface.encodeFunctionData("multiSend", [combinedString]);

  return multiSendEncodedData;
}

/**
 * judgment user has signed Approve Tokens
 * @param {string} owner Address of token owner（proxyWallet）
 * @returns {Promise<string>}
 */
async function hasApproved(owner: string): Promise<string> {
  return new Promise<string>(resolve => {
    const call = backoff.call(
      async (cb: (err: Error | null, result?: string) => void) => {
        try {
          const providerUrl = await fetchWithFailover();
          const provider = new JsonRpcProvider(providerUrl);
          if (WrapUSDCContracts && spenderAddressObj.PolymarketNegRiskCTFExchange) {
            const erc20Contract = new Contract(WrapUSDCContracts, erc20AllowanceAbi, provider);
            const allowance = await erc20Contract.allowance(owner, spenderAddressObj.PolymarketNegRiskCTFExchange);
            cb(null, BigInt(allowance) > BigInt(0) ? "success" : "none");
            return;
          }
          cb(null, "none");
        } catch (error: any) {
          cb(error);
        }
      },
      (err, result) => {
        if (err) {
          console.error("Error checking approval:", err);
          resolve("none");
        } else {
          resolve(result ?? "none");
        }
      },
    );
    call.setStrategy(new backoff.ExponentialStrategy({ initialDelay: 1000, maxDelay: 4000 }));
    call.failAfter(3);
    call.start();
  });
}

export {
  onClickExecTransaction,
  hasApproved,
  approveSignatureWithMagic,
  approveSignature,
  createGetTransactionHash,
  getNonce,
};
