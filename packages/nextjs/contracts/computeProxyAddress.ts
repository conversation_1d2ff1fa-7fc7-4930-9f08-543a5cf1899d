import { fetchWithFailover } from "@/utils/signature";
import backoff from "backoff";
import { Contract, JsonRpcProvider } from "ethers";

const proxyFactoryAddress = process.env.NEXT_PUBLIC_FACTORY_ADDRESS;
if (!proxyFactoryAddress) {
  throw new Error("NEXT_PUBLIC_FACTORY_ADDRESS is not defined");
}

const abi = ["function computeProxyAddress(address user) public view returns (address)"];

async function computeProxyAddress(address: string): Promise<string> {
  return new Promise<string>(resolve => {
    const call = backoff.call(
      async (cb: (err: Error | null, result?: string) => void) => {
        try {
          const providerUrl = await fetchWithFailover();
          const provider = new JsonRpcProvider(providerUrl);
          const contract = new Contract(proxyFactoryAddress as string, abi, provider);
          const result = await contract.computeProxyAddress(address);
          cb(null, result);
        } catch (error: any) {
          cb(error);
        }
      },
      (err, result) => {
        if (err) {
          console.error("Error fetching proxy address:", err);
          resolve("");
        } else {
          resolve(result ?? "");
        }
      },
    );
    call.setStrategy(new backoff.ExponentialStrategy({ initialDelay: 1000, maxDelay: 4000 }));
    call.failAfter(3);
    call.start();
  });
}

export default computeProxyAddress;
