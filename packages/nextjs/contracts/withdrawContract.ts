import { WalletType } from "@/services/store/store";
import { getSignature } from "@/utils";
import { fetchWithFailover } from "@/utils/signature";
import axios from "axios";
import { Contract, Interface, JsonRpcProvider, getBytes, parseUnits } from "ethers";

const getTransactionHashAbi = [
  "function getTransactionHash(address to, uint256 value, bytes data, uint8 operation, uint256 safeTxGas, uint256 baseGas, uint256 gasPrice, address gasToken, address refundReceiver, uint256 _nonce) view returns (bytes32)",
];

const nonceAbi = ["function nonce() view returns (uint256)"];
const unwrapAbi = ["function unwrap(address _to, uint256 _amount)"];

const UnwrapTransferInterface = new Interface(unwrapAbi);

const wrapToken = process.env.NEXT_PUBLIC_WRAP_USDC_CONTRACTS || "";

const signatureParams = {
  gasPrice: "0",
  operation: "0",
  safeTxnGas: "0",
  baseGas: "0",
  gasToken: "******************************************",
  refundReceiver: "******************************************",
};

function createTransferTransaction(to: string, value: number): string {
  const valueInSmallestUnit = Math.floor(value * 10 ** 6);
  return UnwrapTransferInterface.encodeFunctionData("unwrap", [to, valueInSmallestUnit]);
}

function getTransferTransaction(to: string, value: string) {
  const newValue = parseFloat(value);
  const transferData = createTransferTransaction(to, newValue);

  return transferData;
}

async function getNonce(proxyWallet: string) {
  const providerUrl = await fetchWithFailover();
  const provider = new JsonRpcProvider(providerUrl);
  const contract = new Contract(proxyWallet, nonceAbi, provider);
  try {
    const nonce = await contract.nonce();
    return nonce;
  } catch (error) {
    console.error("Error fetching nonce:", error);
    throw error;
  }
}

const onClickWidthdraw = async (
  proxyWallet: string,
  address: string,
  value: string,
  cookie: any,
  setCompletedStatus: any,
  setButtonState: any,
  setTransactionHash: any,
  inputAdress: string,
  setSpecialErrorText: any,
  walletType: WalletType,
) => {
  const providerUrl = await fetchWithFailover();
  const provider = new JsonRpcProvider(providerUrl);
  const contract = new Contract(proxyWallet, getTransactionHashAbi, provider);
  const nonce = await getNonce(proxyWallet);
  const transferData = getTransferTransaction(inputAdress, value);

  const payload = {
    to: wrapToken,
    value: parseUnits("0", "ether"),
    data: transferData,
    operation: 0, // 0 表示 CALL，1 表示 DELEGATECALL
    safeTxGas: 0,
    baseGas: 0,
    gasPrice: 0,
    gasToken: "******************************************",
    refundReceiver: "******************************************",
    nonce: nonce,
  };

  try {
    setButtonState("loading");
    const transactionHash = await contract.getTransactionHash(
      payload.to,
      payload.value,
      payload.data,
      payload.operation,
      payload.safeTxGas,
      payload.baseGas,
      payload.gasPrice,
      payload.gasToken,
      payload.refundReceiver,
      payload.nonce,
    );

    const signature = await getSignature(transactionHash, walletType);

    // 创建一个新的 Uint8Array，长度与 newSignature 相同
    const modifiedSignature = new Uint8Array(getBytes(signature));
    // 将最后一位加上 4
    modifiedSignature[modifiedSignature.length - 1] += 4;
    const hexSignature = "0x" + Buffer.from(modifiedSignature).toString("hex");

    const response = await axios({
      url: "/api/submitApprove",
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        data: payload.data,
        from: address,
        nonce: String(nonce),
        proxyWallet: proxyWallet,
        signature: hexSignature,
        signatureParams: signatureParams,
        to: payload.to,
        type: "SAFE",
        cookie: cookie,
      },
    });

    if (response) {
      setCompletedStatus("success");
      setButtonState("success");

      setTransactionHash(response.data.transactionHash);
    }
  } catch (error) {
    const errorDataText = (error as any)?.response?.data?.error?.error || null;
    setSpecialErrorText(errorDataText);

    setCompletedStatus("failed");
    setButtonState("success");
    throw error;
  }
};

export { onClickWidthdraw };
