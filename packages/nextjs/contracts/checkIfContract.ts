import { fetchWithFailover } from "@/utils/signature";
import { addToast } from "@heroui/react";
import backoff from "backoff";
import { JsonRpcProvider } from "ethers";

export async function checkIfContract(address: string, setIsDisableBtn?: any): Promise<boolean> {
  return new Promise<boolean>(resolve => {
    const call = backoff.call(
      async (cb: (err: Error | null, result?: boolean) => void) => {
        try {
          const providerUrl = await fetchWithFailover();
          const provider = new JsonRpcProvider(providerUrl);
          const code = await provider.getCode(address);
          cb(null, code.length > 2);
        } catch (error: any) {
          cb(error);
        }
      },
      (err, result) => {
        if (err) {
          console.error("Error checking if address is a contract:", err);
          addToast({
            title: "Query contract address failed",
            description: "Please refresh the page",
            color: "danger",
            timeout: 5000,
          });
          setIsDisableBtn && setIsDisableBtn(true);
          resolve(false);
        } else {
          setIsDisableBtn && setIsDisableBtn(false);
          resolve(result ?? false);
        }
      },
    );
    call.setStrategy(new backoff.ExponentialStrategy({ initialDelay: 1000, maxDelay: 4000 }));
    call.failAfter(3);
    call.start();
  });
}
