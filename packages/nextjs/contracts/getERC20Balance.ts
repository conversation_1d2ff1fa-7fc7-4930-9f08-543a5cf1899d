import { truncateToTwoDecimals } from "@/utils";
import { createRpcProvider } from "@/utils/rpc/rpcClient";
import axios from "axios";
import backoff from "backoff";
import { Interface, formatUnits } from "ethers";

const wraptoken_addr = process.env.NEXT_PUBLIC_WRAP_USDC_CONTRACTS;

export async function getUserPromotionBalance(proxy_wallet: string) {
  try {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getPromotionBalance`, {
      params: { proxy_wallet },
      headers: { "Content-Type": "application/json" },
    });

    if (response.data && response.data.promotion && Array.isArray(response.data.promotion)) {
      return response.data.promotion[0] || null;
    }

    // console.warn("Invalid promotion balance response structure:", response.data);
    return null;
  } catch (error) {
    console.error("Error fetching user promotion balance:", error);
    return null;
  }
}

export async function getERC20Balance(proxyWallet: string) {
  if (!wraptoken_addr) {
    throw new Error("USDC_ADDRESS is not defined");
  }

  if (!proxyWallet) {
    console.warn("⚠️ getERC20Balance: No proxyWallet provided");
    return;
  }

  // Use backoff retry to check the balance on the chain
  return await new Promise<string | undefined>(resolve => {
    const call = backoff.call(
      async (cb: (err: Error | null, result?: string) => void) => {
        try {
          const provider = createRpcProvider();

          // 检查地址有效性
          if (!proxyWallet || !/^0x[a-fA-F0-9]{40}$/.test(proxyWallet)) {
            console.warn("❌ Invalid proxyWallet address:", proxyWallet);
            cb(null, "0.00");
            return;
          }

          if (!wraptoken_addr || !/^0x[a-fA-F0-9]{40}$/.test(wraptoken_addr)) {
            console.warn("❌ Invalid token contract address:", wraptoken_addr);
            cb(null, "0.00");
            return;
          }

          const abi = ["function balanceOf(address account) view returns (uint256)"];
          const iface = new Interface(abi);
          const data = iface.encodeFunctionData("balanceOf", [proxyWallet]);

          const result = await provider.call({ to: wraptoken_addr, data });

          // 检查返回结果是否有效
          if (!result || result === "0x" || result.length <= 2) {
            // 尝试检查合约是否存在
            try {
              const codeResult = await provider.send("eth_getCode", [wraptoken_addr, "latest"]);

              if (codeResult === "0x") {
                console.error("❌ Contract does not exist at address:", wraptoken_addr);
              }
            } catch (codeError) {
              console.error("❌ Failed to check contract code:", codeError);
            }

            cb(null, "0.00");
            return;
          }

          const balance = iface.decodeFunctionResult("balanceOf", result)[0];
          const formattedBalance = formatUnits(balance, 6);
          cb(null, truncateToTwoDecimals(parseFloat(formattedBalance)));
        } catch (error: any) {
          console.error("Error in getERC20Balance:", error);
          // 如果是解码错误，返回 0 而不是抛出错误
          if (error.message?.includes("could not decode result data")) {
            console.warn("Decode error, returning 0 balance");
            cb(null, "0.00");
          } else {
            cb(error);
          }
        }
      },
      (err, result) => {
        if (err) {
          console.error("Error fetching ERC20 balance:", err);
          resolve(undefined);
        } else {
          resolve(result);
        }
      },
    );
    call.setStrategy(new backoff.ExponentialStrategy({ initialDelay: 1000, maxDelay: 4000 }));
    call.failAfter(3);
    call.start();
  });
}
