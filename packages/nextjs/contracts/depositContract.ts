import { WalletType } from "@/services/store/store";
import { getSignature } from "@/utils";
import { fetchWithFailover } from "@/utils/signature";
import axios from "axios";
import { Contract, Interface, MaxUint256, getBytes, parseUnits } from "ethers";
import { JsonRpcProvider } from "ethers";

const erc20ApproveAbi = [
  "function approve(address spender, uint256 amount) public returns (bool)",
  "function allowance(address owner, address spender) public view returns (uint256)",
  "function balanceOf(address _owner) view returns (uint256)",
];
const erc20BalanceAbi = ["function balanceOf(address owner) view returns (uint256)"];
const nonceAbi = ["function nonce() view returns (uint256)"];
const wrapAbi = ["function wrap(address _to, uint256 _amount)"];
const getTransactionHashAbi = [
  "function getTransactionHash(address to, uint256 value, bytes data, uint8 operation, uint256 safeTxGas, uint256 baseGas, uint256 gasPrice, address gasToken, address refundReceiver, uint256 _nonce) view returns (bytes32)",
];

const WrapUSDCContracts = process.env.NEXT_PUBLIC_WRAP_USDC_CONTRACTS || "";
const USDCContractAddress = process.env.NEXT_PUBLIC_USDC_CONTRACT_ADDRESS || "";

const wrapContractInterface = new Interface(wrapAbi);

const signatureParams = {
  gasPrice: "0",
  operation: "0",
  safeTxnGas: "0",
  baseGas: "0",
  gasToken: "******************************************",
  refundReceiver: "******************************************",
};

function getWrapTransaction(to: string, value: number) {
  const targetAmount = Math.floor(value * 10 ** 6);
  return wrapContractInterface.encodeFunctionData("wrap", [to, targetAmount]);
}

function getErc20ApprovalTransaction(to: string) {
  const erc20Interface = new Interface(erc20ApproveAbi);
  return erc20Interface.encodeFunctionData("approve", [to, MaxUint256]);
}

async function getNonce(proxyWallet: string) {
  const providerUrl = await fetchWithFailover();
  const provider = new JsonRpcProvider(providerUrl);
  const contract = new Contract(proxyWallet, nonceAbi, provider);
  try {
    const nonce = await contract.nonce();
    return nonce;
  } catch (error) {
    console.error("Error fetching nonce:", error);
    throw error;
  }
}

const increaseAllowance = async (proxyWalletAddr: string, signerAddr: string, cookie: any, walletType: WalletType) => {
  const providerUrl = await fetchWithFailover();
  const provider = new JsonRpcProvider(providerUrl);

  const proxyWalletContract = new Contract(proxyWalletAddr, getTransactionHashAbi, provider);
  const nonce = await getNonce(proxyWalletAddr);

  const wrapData = getErc20ApprovalTransaction(WrapUSDCContracts);

  const payload = {
    to: USDCContractAddress,
    value: parseUnits("0", "ether"),
    data: wrapData,
    operation: 0, // 0 表示 CALL，1 表示 DELEGATECALL
    safeTxGas: 0,
    baseGas: 0,
    gasPrice: 0,
    gasToken: "******************************************",
    refundReceiver: "******************************************",
    nonce: nonce,
  };
  try {
    const transactionHash = await proxyWalletContract.getTransactionHash(
      payload.to,
      payload.value,
      payload.data,
      payload.operation,
      payload.safeTxGas,
      payload.baseGas,
      payload.gasPrice,
      payload.gasToken,
      payload.refundReceiver,
      payload.nonce,
    );

    const signature = await getSignature(transactionHash, walletType);

    // 创建一个新的 Uint8Array，长度与 newSignature 相同
    const modifiedSignature = new Uint8Array(getBytes(signature));
    // 将最后一位加上 4
    modifiedSignature[modifiedSignature.length - 1] += 4;
    const hexSignature = "0x" + Buffer.from(modifiedSignature).toString("hex");

    const response = await axios({
      url: "/api/submitApprove",
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        data: payload.data,
        from: signerAddr,
        nonce: String(nonce),
        proxyWallet: proxyWalletAddr,
        signature: hexSignature,
        signatureParams: signatureParams,
        to: payload.to,
        type: "SAFE",
        cookie: cookie,
      },
    });

    if (response) {
      return response.data.transaction;
    }
  } catch (error) {
    console.error("Error fetching nonce:", error);
    throw error;
  }
};

const checkAllowance = async (ownerAddress: string) => {
  const providerUrl = await fetchWithFailover();
  const provider = new JsonRpcProvider(providerUrl);
  const erc20Contract = new Contract(USDCContractAddress, erc20ApproveAbi, provider);

  try {
    const allowance = await erc20Contract.allowance(ownerAddress, WrapUSDCContracts);
    return allowance;
  } catch (error) {
    console.error("Error fetching allowance:", error);
  }
};

const depositConstruct = async ({
  proxyWalletAddr,
  signerAddr,
  amount,
  setTransactionHash,
  setCompletedStatus,
  setButtonState,
  cookie,
  walletType,
}: {
  proxyWalletAddr: string;
  signerAddr: string;
  amount: number;
  setTransactionHash: any;
  setCompletedStatus: any;
  setButtonState: any;
  cookie: any;
  walletType: WalletType;
}) => {
  const providerUrl = await fetchWithFailover();
  const provider = new JsonRpcProvider(providerUrl);

  const proxyWalletContract = new Contract(proxyWalletAddr, getTransactionHashAbi, provider);
  const nonce = await getNonce(proxyWalletAddr);

  const contractErc20 = new Contract(USDCContractAddress, erc20ApproveAbi, provider);
  const usdcBalance = await contractErc20.balanceOf(proxyWalletAddr);

  if (usdcBalance < amount) {
    setCompletedStatus("failed");
    console.log("Insufficient balance");
    return;
  }

  const wrapData = getWrapTransaction(proxyWalletAddr, amount);
  const payload = {
    to: WrapUSDCContracts,
    value: parseUnits("0", "ether"),
    data: wrapData,
    operation: 0, // 0 表示 CALL，1 表示 DELEGATECALL
    safeTxGas: 0,
    baseGas: 0,
    gasPrice: 0,
    gasToken: "******************************************",
    refundReceiver: "******************************************",
    nonce: nonce,
  };
  try {
    setButtonState("loading");
    const transactionHash = await proxyWalletContract.getTransactionHash(
      payload.to,
      payload.value,
      payload.data,
      payload.operation,
      payload.safeTxGas,
      payload.baseGas,
      payload.gasPrice,
      payload.gasToken,
      payload.refundReceiver,
      payload.nonce,
    );

    const signature = await getSignature(transactionHash, walletType);

    const modifiedSignature = new Uint8Array(getBytes(signature));
    modifiedSignature[modifiedSignature.length - 1] += 4;
    const hexSignature = "0x" + Buffer.from(modifiedSignature).toString("hex");

    const response = await axios({
      url: "/api/submitApprove",
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        data: payload.data,
        from: signerAddr,
        nonce: String(nonce),
        proxyWallet: proxyWalletAddr,
        signature: hexSignature,
        signatureParams: signatureParams,
        to: payload.to,
        type: "SAFE",
        cookie: cookie,
      },
    });

    if (response) {
      setCompletedStatus("success");
      setButtonState("success");

      setTransactionHash(response.data.transactionHash);
    }
  } catch (error) {
    console.error("Error fetching nonce:", error);
    setCompletedStatus("failed");
    setButtonState("success");
    throw error;
  }
};

const getUSDCBalance = async (address: string) => {
  const providerUrl = await fetchWithFailover();
  const provider = new JsonRpcProvider(providerUrl);
  const contract = new Contract(USDCContractAddress, erc20BalanceAbi, provider);

  try {
    const balance = await contract.balanceOf(address);
    // 将余额转换为 USDC 的标准单位
    const formattedBalance = parseFloat(balance.toString()) / 10 ** 6;
    return formattedBalance.toFixed(6);
  } catch (error) {
    console.error("Error fetching USDC balance:", error);
    throw error;
  }
};

export { increaseAllowance, checkAllowance, depositConstruct, getUSDCBalance };
