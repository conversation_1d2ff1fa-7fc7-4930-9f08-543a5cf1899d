import { WalletType } from "@/services/store/store";
import { getSignature } from "@/utils";
import { isTokenExpiredError } from "@/utils/auth/tokenErrorHandler";
import { createRpcProvider } from "@/utils/rpc/rpcClient";
import axios from "axios";
import { Interface, getBytes, parseUnits } from "ethers";

const AdaptorContracts = process.env.NEXT_PUBLIC_ADAPTOR_ADDRESS;

const nonceAbi = ["function nonce() view returns (uint256)"];
const balanceOfAbi = ["function balanceOf(address _owner, uint256 _id) view returns (uint256)"];
const redeemPositionsAbi = ["function redeemPositions(bytes32 _conditionId, uint256[] _amounts)"];
const getTransactionHashAbi = [
  "function getTransactionHash(address to, uint256 value, bytes data, uint8 operation, uint256 safeTxGas, uint256 baseGas, uint256 gasPrice, address gasToken, address refundReceiver, uint256 _nonce) view returns (bytes32)",
];
const redeemPositionsInterface = new Interface(redeemPositionsAbi);

const signatureParams = {
  gasPrice: "0",
  operation: "0",
  safeTxnGas: "0",
  baseGas: "0",
  gasToken: "******************************************",
  refundReceiver: "******************************************",
};

function getRedeemPositionsTransaction(conditionId: string, amounts: number[]): string {
  const newConditionId = "0x" + conditionId;
  return redeemPositionsInterface.encodeFunctionData("redeemPositions", [newConditionId, amounts]);
}

async function getNonce(proxyWallet: string) {
  const provider = createRpcProvider();
  try {
    // 直接调用 nonce() 方法，这是一个标准的 Safe 钱包方法
    const iface = new Interface(nonceAbi);
    const data = iface.encodeFunctionData("nonce", []);
    const result = await provider.call({ to: proxyWallet, data });
    const nonce = iface.decodeFunctionResult("nonce", result)[0];
    return nonce;
  } catch (error) {
    console.error("Error fetching nonce:", error);
    throw error;
  }
}

async function getBalance(proxyWallet: string, currentTokenId: string) {
  if (!AdaptorContracts) {
    throw new Error("AdaptorContracts is not defined");
  }
  const provider = createRpcProvider();
  try {
    const iface = new Interface(balanceOfAbi);
    const tokenIdBigNumber = BigInt(currentTokenId);
    const data = iface.encodeFunctionData("balanceOf", [proxyWallet, tokenIdBigNumber]);
    const result = await provider.call({ to: AdaptorContracts, data });
    const balance = iface.decodeFunctionResult("balanceOf", result)[0];

    return balance;
  } catch (error) {
    console.error("Error fetching balance:", error);
    throw error;
  }
}

// 重试配置
const CLAIM_RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1秒
  maxDelay: 8000, // 最大8秒
};

// 检查是否是可重试的错误
const isRetryableClaimError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || "";
  const errorCode = error?.code;

  // Token过期错误不应该重试，需要重新登录
  if (isTokenExpiredError(error)) {
    return false;
  }

  // 网络相关错误
  if (errorCode === "NETWORK_ERROR" || errorCode === "TIMEOUT" || errorCode === "ECONNABORTED") {
    return true;
  }

  // 常见的可重试错误
  if (
    errorMessage.includes("network") ||
    errorMessage.includes("timeout") ||
    errorMessage.includes("connection") ||
    errorMessage.includes("signer is undefined") ||
    errorMessage.includes("failed to fetch") ||
    errorMessage.includes("internal server error")
  ) {
    return true;
  }

  // HTTP 5xx 错误
  if (error?.response?.status >= 500 && error?.response?.status < 600) {
    return true;
  }

  return false;
};

// 延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const onClickClaim = async (
  proxyWallet: string,
  address: string,
  cookie: any,
  currentConditionId: string,
  clob_token_ids: string,
  notificationParams: any,
  walletType: WalletType,
) => {
  const executeClaimWithRetry = async (attempt = 0): Promise<any> => {
    try {
      console.log(`🎯 Claim attempt ${attempt + 1}/${CLAIM_RETRY_CONFIG.maxRetries + 1}`);

      const provider = createRpcProvider();
      const nonce = await getNonce(proxyWallet);
      const balanceYes = await getBalance(proxyWallet, clob_token_ids[0]);
      const balanceNo = await getBalance(proxyWallet, clob_token_ids[1]);

      const balance = [balanceYes, balanceNo];
      const redeemPositionsData = getRedeemPositionsTransaction(currentConditionId, balance);

      // 继续执行原有的领奖逻辑
      return await executeClaimTransaction(
        provider,
        proxyWallet,
        nonce,
        redeemPositionsData,
        address,
        cookie,
        walletType,
        notificationParams,
      );
    } catch (error) {
      console.error(`❌ Claim attempt ${attempt + 1} failed:`, error);

      // 检查是否是token过期错误
      if (isTokenExpiredError(error)) {
        console.error("🔐 Token expired during claim process");
        const tokenExpiredError = new Error("Login session expired, please login again");
        (tokenExpiredError as any).isTokenExpired = true;
        throw tokenExpiredError;
      }

      // 检查是否可以重试
      if (attempt < CLAIM_RETRY_CONFIG.maxRetries && isRetryableClaimError(error)) {
        const delayMs = Math.min(CLAIM_RETRY_CONFIG.baseDelay * Math.pow(2, attempt), CLAIM_RETRY_CONFIG.maxDelay);

        console.log(`🔄 Retrying claim in ${delayMs}ms... (attempt ${attempt + 1}/${CLAIM_RETRY_CONFIG.maxRetries})`);
        await delay(delayMs);
        return executeClaimWithRetry(attempt + 1);
      }

      // 重试失败或不可重试的错误，抛出原始错误
      throw error;
    }
  };

  // 执行重试逻辑
  return executeClaimWithRetry();
};

// 提取原有的领奖交易执行逻辑
const executeClaimTransaction = async (
  provider: any,
  proxyWallet: string,
  nonce: any,
  redeemPositionsData: any,
  address: string,
  cookie: any,
  walletType: WalletType,
  notificationParams: any,
) => {
  const { setNotiTitle, setNotiStatus, setButtonState, setShowNotification } = notificationParams;
  const payload = {
    to: AdaptorContracts,
    value: parseUnits("0", "ether"),
    data: redeemPositionsData,
    operation: 0, // 0 means CALL, 1 means DELEGATECALL
    safeTxGas: 0,
    baseGas: 0,
    gasPrice: 0,
    gasToken: "******************************************",
    refundReceiver: "******************************************",
    nonce: nonce,
  };

  try {
    setButtonState("loading");

    // 使用 RPC 调用 getTransactionHash
    const iface = new Interface(getTransactionHashAbi);
    const data = iface.encodeFunctionData("getTransactionHash", [
      payload.to,
      payload.value,
      payload.data,
      payload.operation,
      payload.safeTxGas,
      payload.baseGas,
      payload.gasPrice,
      payload.gasToken,
      payload.refundReceiver,
      payload.nonce,
    ]);
    const result = await provider.call({ to: proxyWallet, data });
    const transactionHash = iface.decodeFunctionResult("getTransactionHash", result)[0];
    const signature = await getSignature(transactionHash, walletType);

    // Create a new Uint8Array with the same length as newSignature
    const modifiedSignature = new Uint8Array(getBytes(signature));
    // Add 4 to the last byte
    modifiedSignature[modifiedSignature.length - 1] += 4;
    const hexSignature = "0x" + Buffer.from(modifiedSignature).toString("hex");

    const response = await axios({
      url: "/api/claim",
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        data: payload.data,
        from: address,
        nonce: String(payload.nonce),
        proxyWallet: proxyWallet,
        signature: hexSignature,
        signatureParams: signatureParams,
        to: payload.to,
        type: "SAFE",
        cookie: cookie,
      },
    }).catch(error => {
      // 检查token过期错误
      if (error?.response?.status === 401 && error?.response?.data?.isTokenExpired) {
        const tokenError = new Error("Login session expired, please login again");
        (tokenError as any).isTokenExpired = true;
        throw tokenError;
      }
      throw error;
    });

    if (response.data?.state === "STATE_NEW") {
      setButtonState("success");
      setShowNotification(true);
      setNotiTitle("Claim Success");
      setNotiStatus("success");
    }
  } catch (error: any) {
    // 检查是否是token过期错误
    if (isTokenExpiredError(error) || error?.isTokenExpired) {
      setButtonState("failure");
      setShowNotification(true);
      setNotiTitle("Login session expired");
      setNotiStatus("failure");
      console.error("🔐 Token expired during claim transaction");
    } else if (error?.message?.includes("User denied signature request")) {
      setButtonState("none");
      setShowNotification(true);
      setNotiTitle("Claim Cancelled");
      setNotiStatus("failure");
    } else {
      setButtonState("failure");
      setShowNotification(true);
      setNotiTitle("Claim Failed");
      setNotiStatus("failure");
    }
    throw error;
  }
};

export { onClickClaim };
