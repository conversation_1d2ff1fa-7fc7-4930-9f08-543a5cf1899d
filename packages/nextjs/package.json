{"name": "@se-2/nextjs", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "check-types": "tsc --noEmit --incremental", "dev": "next dev", "format": "prettier --write . '!(node_modules|.next|contracts)/**/*'", "lint": "next lint", "serve": "next start --hostname 0.0.0.0", "start": "next dev", "vercel": "vercel", "vercel:yolo": "vercel --build-env NEXT_PUBLIC_IGNORE_BUILD_ERROR=true"}, "dependencies": {"@ethersproject/abi": "^5.7.0", "@glidejs/glide": "^3.7.1", "@heroicons/react": "~2.1.5", "@heroui/react": "2.7.2", "@polymarket/clob-client": "^4.11.0", "@polymarket/order-utils": "^2.1.0", "@radix-ui/react-collapsible": "^1.1.1", "@rainbow-me/rainbowkit": "^2.2.0", "@rainbow-me/rainbowkit-siwe-next-auth": "^0.5.0", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.59.15", "@uniswap/sdk-core": "~4.0.1", "@uniswap/v2-sdk": "~3.0.1", "@web3icons/react": "^3.11.0", "axios": "^1.7.9", "blo": "~1.0.1", "burner-connector": "~0.0.8", "d3": "^7.9.0", "daisyui": "4.12.10", "dotenv": "^16.4.7", "embla-carousel": "^8.3.0", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.3.0", "ethers": "^6.13.4", "framer-motion": "^11.11.7", "i18next": "^24.0.2", "i18next-browser-languagedetector": "^8.0.4", "lucide-react": "^0.453.0", "next": "~14.2.11", "next-auth": "^4.24.8", "next-i18next": "^15.4.0", "next-nprogress-bar": "~2.3.13", "next-themes": "~0.3.0", "pedone-clob-client": "^1.0.1", "punycode": "^2.3.1", "qrcode.react": "^4.1.0", "react": "~18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-day-picker": "^9.3.0", "react-device-detect": "^2.2.3", "react-dom": "~18.3.1", "react-file-viewer": "^1.2.1", "react-hot-toast": "~2.4.0", "react-i18next": "^15.1.2", "react-markdown": "^9.0.1", "react-spinners": "^0.14.1", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.0", "sharp": "^0.29.3", "siwe": "^2.3.2", "tailwind-merge": "^2.5.3", "tailwindcss": "^3.4.13", "tailwindcss-animate": "^1.0.7", "use-debounce": "~8.0.4", "usehooks-ts": "2.13.0", "viem": "2.21.7", "wagmi": "^2.12.19", "zustand": "~4.1.2"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "~4.1.1", "@types/d3": "^7", "@types/glidejs__glide": "^3", "@types/node": "~18.19.50", "@types/punycode": "^2", "@types/react": "~18.3.5", "@types/react-syntax-highlighter": "^15", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "~5.40.0", "abitype": "1.0.6", "autoprefixer": "~10.4.20", "eslint": "~8.24.0", "eslint-config-next": "~14.0.4", "eslint-config-prettier": "~8.5.0", "eslint-plugin-prettier": "~4.2.1", "postcss": "~8.4.45", "prettier": "~2.8.4", "tailwindcss": "~3.4.11", "type-fest": "~4.6.0", "typescript": "5.5.3", "vercel": "~37.4.2"}}