{"name": "se-2", "version": "0.0.1", "private": true, "workspaces": {"packages": ["packages/*"]}, "scripts": {"account": "yarn workspace @se-2/hardhat account", "chain": "yarn workspace @se-2/hardhat chain", "compile": "yarn workspace @se-2/hardhat compile", "deploy": "yarn workspace @se-2/hardhat deploy", "flatten": "yarn workspace @se-2/hardhat flatten", "fork": "yarn workspace @se-2/hardhat fork", "format": "yarn next:format && yarn hardhat:format", "generate": "yarn workspace @se-2/hardhat generate", "hardhat-verify": "yarn workspace @se-2/hardhat hardhat-verify", "hardhat:format": "yarn workspace @se-2/hardhat format", "hardhat:lint": "yarn workspace @se-2/hardhat lint", "hardhat:lint-staged": "yarn workspace @se-2/hardhat lint-staged", "hardhat:test": "yarn workspace @se-2/hardhat test", "postinstall": "husky install", "next:build": "yarn workspace @se-2/nextjs build", "next:check-types": "yarn workspace @se-2/nextjs check-types", "next:format": "yarn workspace @se-2/nextjs format", "next:lint": "yarn workspace @se-2/nextjs lint", "next:serve": "yarn workspace @se-2/nextjs serve", "precommit": "lint-staged", "start": "yarn workspace @se-2/nextjs dev", "test": "jest", "vercel": "yarn workspace @se-2/nextjs vercel", "vercel:yolo": "yarn workspace @se-2/nextjs vercel:yolo", "verify": "yarn workspace @se-2/hardhat verify"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/backoff": "^2", "@types/canvas-confetti": "^1", "@types/lodash": "^4", "@types/react": "^18", "@types/react-dom": "^18", "husky": "~8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "~13.2.2", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "packageManager": "yarn@3.2.3", "engines": {"node": ">=18.18.0"}, "dependencies": {"@artalk/plugin-auth": "^0.0.1", "@magic-ext/connect": "^9.1.0", "@magic-ext/oauth": "^23.0.7", "@magic-sdk/admin": "^2.4.1", "@react-spring/web": "^10.0.0", "artalk": "^2.9.1", "backoff": "^2.5.0", "canvas-confetti": "^1.9.3", "clsx": "^2.1.1", "fuse.js": "^7.1.0", "js-sha256": "^0.11.0", "lodash": "^4.17.21", "magic-sdk": "^29.0.6", "motion": "^12.12.1", "react-number-format": "^5.4.4", "react-responsive": "^10.0.0", "simplex-noise": "^4.0.3", "tailwind-merge": "^3.3.0"}}