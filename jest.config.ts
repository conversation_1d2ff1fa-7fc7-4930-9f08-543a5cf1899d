import type { Config } from 'jest';

const config: Config = {
  // 自动清除 mock 调用、实例、上下文和结果
  clearMocks: true,

  // 收集测试覆盖率
  collectCoverage: true,

  // 覆盖率输出目录
  coverageDirectory: 'coverage',

  // 使用 V8 引擎收集覆盖率
  coverageProvider: 'v8',

  // 测试环境
  testEnvironment: 'jsdom',

  // Jest 使用的预设
  preset: 'ts-jest',

  // Jest 用于检测测试文件的模式
  testMatch: ['**/__tests__/**/*.[jt]s?(x)', '**/?(*.)+(spec|test).[tj]s?(x)'],

  // 模块文件扩展名
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/packages/nextjs/$1',
  },
  // 转换器配置
  transform: {
    '^.+\\.tsx?$': 'ts-jest',
  },

  // 忽略对 `node_modules` 的转换
  transformIgnorePatterns: ['/node_modules/'],
};

export default config;
